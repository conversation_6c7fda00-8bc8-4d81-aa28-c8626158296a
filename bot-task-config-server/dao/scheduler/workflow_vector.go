package scheduler

import (
	"context"
	"fmt"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
)

// NewUpgradeWorkflowVectorTask 创建升级工作流embedding向量任务
func NewUpgradeWorkflowVectorTask(ctx context.Context, param entity.TaskUpgradeWorkflowVectorParams) error {
	log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask, appID: %s, params: %+v", param.RobotID, param)
	task, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(param.RobotID))), entity.TaskUpgradeWorkflowVector,
		entity.TaskMutexNone, param)
	taskID, err := taskScheduler.CreateTask(ctx, task)
	if err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed, appID: %s, task: %+v, err: %+v", param.RobotID, task, err)
		return err
	}
	log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask success, appID: %s, taskID: %d", param.RobotID, taskID)
	return nil
}

// NewUpgradeWorkflowVectorTasks 创建升级工作流embedding向量任务
func NewUpgradeWorkflowVectorTasks(ctx context.Context, params []entity.TaskUpgradeWorkflowVectorParams) ([]string, []string, []error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	succeededAppIDs := make([]string, 0)
	failedAppIDs := make([]string, 0)
	failedErrors := make([]error, 0)
	for _, param := range params {
		wg.Add(1)
		go func(p entity.TaskUpgradeWorkflowVectorParams) {
			defer wg.Done()
			log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask, appID: %s, params: %+v", p.RobotID, p)
			task, _ := task_scheduler.NewTask(
				ctx, task_scheduler.UserID(uint64(encode.StringToInt64(p.RobotID))), entity.TaskUpgradeWorkflowVector,
				entity.TaskMutexNone, p,
			)
			taskID, err := taskScheduler.CreateTask(ctx, task)
			if err != nil {
				log.ErrorContextf(ctx, "taskScheduler.CreateTask failed, appID: %s, task: %+v, err: %+v", p.RobotID, task, err)
				mu.Lock()
				failedAppIDs = append(failedAppIDs, p.RobotID)
				failedErrors = append(failedErrors, err)
				mu.Unlock()
				return
			}
			log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask success, appID: %s, taskID: %d", p.RobotID, taskID)
			mu.Lock()
			succeededAppIDs = append(succeededAppIDs, p.RobotID)
			mu.Unlock()
		}(param)
	}
	wg.Wait()
	return succeededAppIDs, failedAppIDs, failedErrors
}

// GetUpgradeWorkflowVectorTasks 获取升级工作流embedding向量任务列表
func GetUpgradeWorkflowVectorTasks(ctx context.Context, pageSize, pageNum int) ([]*entity.SchedulerTask, int64, error) {
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks, pageSize:%d, pageNum:%d", pageSize, pageNum)
	db := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx)
	// 构建UNION查询，确保两个表的列结构一致
	unionQuery := fmt.Sprintf(`
		(SELECT id, user_id, task_type, task_mutex, params, retry_times, max_retry_times,
		 timeout, runner, runner_instance, result, trace_id, start_time, end_time,
		 next_start_time, create_time, update_time
		 FROM %s WHERE task_type = ?)
		UNION ALL
		(SELECT id, user_id, task_type, task_mutex, params, retry_times, max_retry_times,
		 timeout, runner, runner_instance, result, trace_id, start_time, end_time,
		 next_start_time, create_time, '' as update_time
		 FROM %s WHERE task_type = ?)`,
		entity.SchedulerTask{}.TableName(),
		entity.SchedulerTaskLog{}.TableName())

	// 计算总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM (%s) as union_table", unionQuery)
	var totalCount int64
	err := db.Raw(countQuery, entity.TaskUpgradeWorkflowVector, entity.TaskUpgradeWorkflowVector).Count(&totalCount).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Count err: %+v", err)
		return nil, 0, err
	}

	// 查询分页数据
	var tasks []*entity.SchedulerTask
	offset := (pageNum - 1) * pageSize
	order := fmt.Sprintf("%s DESC", entity.TSchedulerTaskColumns.CreateTime)
	finalQuery := fmt.Sprintf("SELECT * FROM (%s) as union_table ORDER BY %s LIMIT ? OFFSET ?", unionQuery, order)
	err = db.Raw(finalQuery, entity.TaskUpgradeWorkflowVector, entity.TaskUpgradeWorkflowVector, pageSize, offset).Find(&tasks).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Find err: %+v", err)
		return nil, 0, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks success, total: %d, len(tasks): %d", totalCount, len(tasks))
	return tasks, totalCount, nil
}

// GetUpgradeWorkflowVectorTaskByAppIDAndTraceID 获取升级工作流embedding向量任务
func GetUpgradeWorkflowVectorTaskByAppIDAndTraceID(ctx context.Context, appID string, traceID string) ([]*entity.SchedulerTask, error) {
	// task_scheduler 根据AppID查询向量升级任务
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID, appID: %s, traceID: %s", appID, traceID)
	if appID == "" && traceID == "" {
		return nil, nil
	}
	taskTable := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTask{}.TableName())
	taskHistoryTable := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTaskLog{}.TableName())
	if appID != "" {
		appIDQuery := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.UserID)
		taskTable = taskTable.Where(appIDQuery, appID)
		taskHistoryTable = taskHistoryTable.Where(appIDQuery, appID)
	}
	if traceID != "" {
		traceIDQuery := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.TraceID)
		taskTable = taskTable.Where(traceIDQuery, traceID)
		taskHistoryTable = taskHistoryTable.Where(traceIDQuery, traceID)
	}
	// 查询未完成的任务
	var tasks []*entity.SchedulerTask
	err := taskTable.Find(&tasks).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID success, len(task): %d", len(tasks))
	// 查询已完成的任务
	var taskHistories []*entity.SchedulerTaskLog
	err = taskHistoryTable.Find(&taskHistories).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID success, len(taskHistories): %d", len(taskHistories))
	// 合并tasks和taskHistories
	for _, taskHistory := range taskHistories {
		tasks = append(tasks, &entity.SchedulerTask{
			ID:             taskHistory.ID,
			UserID:         taskHistory.UserID,
			Type:           taskHistory.Type,
			Mutex:          taskHistory.Mutex,
			Params:         taskHistory.Params,
			RetryTimes:     taskHistory.RetryTimes,
			MaxRetryTimes:  taskHistory.MaxRetryTimes,
			Timeout:        taskHistory.Timeout,
			Runner:         taskHistory.Runner,
			RunnerInstance: taskHistory.RunnerInstance,
			Result:         taskHistory.Result,
			TraceID:        taskHistory.TraceID,
			StartTime:      taskHistory.StartTime,
			EndTime:        taskHistory.EndTime,
			NextStartTime:  taskHistory.NextStartTime,
			CreateTime:     taskHistory.CreateTime,
		})
	}
	return tasks, nil
}

// // StopUpgradeWorkflowVectorTaskByTaskID 停止升级工作流embedding向量任务
// func StopUpgradeWorkflowVectorTaskByTaskID(ctx context.Context, taskID uint64) error {
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID, taskID: %d", taskID)
// 	err := taskScheduler.StopTask(ctx, task_scheduler.TaskID(taskID))
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID failed, taskID:%d, err:%+v", taskID, err)
// 		return err
// 	}
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID success, taskID:%d", taskID)
// 	return nil
// }

// // StopUpgradeWorkflowVectorTaskByAppID 停止升级工作流embedding向量任务
// func StopUpgradeWorkflowVectorTaskByAppID(ctx context.Context, appID string) error {
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID, appID: %s", appID)
// 	task, err := GetUpgradeWorkflowVectorTaskByAppID(ctx, appID)
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID failed, appID:%s, err:%+v", appID, err)
// 		return err
// 	}
// 	taskID := task.ID
// 	err = taskScheduler.StopTask(ctx, task_scheduler.TaskID(taskID))
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID failed, appID:%s, taskID:%d, err:%+v", appID, taskID, err)
// 		return err
// 	}
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID success, appID:%s, taskID:%d", appID, taskID)
// 	return nil
// }

// RestartUpgradeWorkflowVectorTaskByAppID 重启升级工作流embedding向量任务
func RestartUpgradeWorkflowVectorTaskByAppID(ctx context.Context, appID string) error {
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID, appID: %s", appID)
	// 查询未完成的任务
	query := fmt.Sprintf("%s = %d AND %s = ?", entity.TSchedulerTaskColumns.Type, entity.TaskUpgradeWorkflowVector,
		entity.TSchedulerTaskColumns.UserID)
	var task entity.SchedulerTask
	err := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTask{}.TableName()).
		Where(query, appID).
		Take(&task).Error
	if err != nil {
		log.ErrorContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
		return err
	}
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID get one task success, task: %+v", task)
	taskID := task.ID
	err = taskScheduler.ContinueTerminatedTask(ctx, task_scheduler.TaskID(taskID), task.RetryTimes, 0)
	if err != nil {
		log.ErrorContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID failed, appID:%s, taskID:%d, err:%+v", appID, taskID, err)
		return err
	}
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID success, appID:%s, taskID:%d", appID, taskID)
	return nil
}
