// Package vector 向量数据库相关
package vector

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/go-comm/json0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (

	// WorkflowSandboxGroupInfix 沙箱环境向量库中缀
	WorkflowSandboxGroupInfix = "sandbox"
	// WorkflowProdGroupInfix 正式环境向量库中缀
	WorkflowProdGroupInfix = "prod"
	// Workflow 工作流前缀
	Workflow      = "workflow"
	WorkflowEntry = "entry"

	WorkflowNameEmbedding          = "FLOW_NAME"
	WorkExampleEmbedding           = "EXAMPLE"
	WorkflowNameExamsDescEmbedding = "FLOW_NAME_EXAMPLE_DESC"

	FieldWorkflowId        = "_sys_str_workflow_id"
	FieldWorkflowEnable    = "_sys_int_workflow_enable"
	FieldExampleId         = "_sys_str_example_id"
	FieldExampleValue      = "_sys_str_example_value"
	FieldFLowNameExamsDesc = "_sys_str_workflow_flow_example_desc"
)

// DelWorkflowCorpusVector 删除工作流向量
func (d dao) DelWorkflowCorpusVector(ctx context.Context, robotID, groupId, modelName string, featIds []string) error {
	log.InfoContextf(ctx, "DeleteCorpusVector, robotID:%s, intentExampleIds:%+v", robotID, featIds)
	requestId := util.RequestID(ctx)
	useModelInfo := config.GetUsingVectorModelInfo(ctx, modelName)
	appInfo := getWorkflowVectorAppInfo(useModelInfo, robotID)
	if len(featIds) == 0 {
		return nil
	}

	for _, fIds := range types.SplitStringSlice(featIds, useModelInfo.OperationMaxIDs) {
		delVectorReq := &vector_db_manager.DeleteVectorReq{
			RequestId: requestId,
			GroupId:   groupId,
			Ids:       fIds,
			AppInfo:   appInfo,
		}
		log.DebugContextf(ctx, "DelWorkflowCorpusVector DelVector req:%s", delVectorReq.String())
		delVectorRsp, err := d.client.DeleteVector(ctx, delVectorReq)
		if err != nil || delVectorRsp == nil || delVectorRsp.Code != 0 {
			err = fmt.Errorf("delVectorRsp:%+v, err:%v", delVectorRsp, err)
			log.ErrorContextf(ctx, "DelWorkflowCorpusVector delVectorRsp, err:%v", err)
			return err
		}

		log.InfoContextf(ctx, "DelWorkflowCorpusVector delVectorRsp rsp:%s", delVectorRsp.String())
	}

	return nil
}

// GetWorkflowCorpusVector 通过flowId获取工作流向量
func (d dao) GetWorkflowCorpusVector(ctx context.Context, robotID,
	flowId string) ([]*vector_db_manager.GetVectorRsp_Index, error) {
	sandboxGroupID, _, modelName, err := GetWorkflowVectorGroupSandboxAndProdIdFromDB(ctx, robotID,
		entity.SaveWorkflowType)
	useModelInfo := config.GetUsingVectorModelInfo(ctx, modelName)
	appInfo := getWorkflowVectorAppInfo(useModelInfo, robotID)

	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowCorpusVector|"+
			"GetWorkflowVectorGroupSandboxAndProdIdFromDB err:%v", err)
		return nil, err
	}
	vectors, err := d.GetVectors(ctx, util.RequestID(ctx), sandboxGroupID, appInfo, []string{flowId})
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowCorpusVector|GetVectors|fail|err:%+v", err)
		return nil, err
	}
	return vectors, nil
}

// SaveWorkflowToVector 保存工作流相关信息到向量
func (d dao) SaveWorkflowToVector(ctx context.Context, tx *gorm.DB, robotID string,
	examples []*entity.WorkflowExample, newWorkflow *entity.Workflow) error {
	// 判断应用ID对应的向量库是否在升级中，如果在升级中，则报错返回
	err := d.CheckWorkflowVectorInfoUpgrade(ctx, tx, robotID, entity.SaveWorkflowType)
	if err != nil {
		return err
	}
	workflowId := newWorkflow.WorkflowID
	wfExamplesDescTempleStr := ""
	hasWfExamplesDescVector := false

	//modelName := useModelInfo.LatestEmbeddingModelName
	workflowStatus := GetWfVectorEnableByFlowState(newWorkflow.WorkflowState, newWorkflow.IsEnable)
	name := strings.TrimSpace(newWorkflow.WorkflowName)
	desc := strings.TrimSpace(newWorkflow.WorkflowDesc)
	embeddingRenderWorkflow := new(entity.EmbeddingRenderWorkflow)
	embeddingRenderWorkflow.WorkflowName = name
	embeddingRenderWorkflow.WorkflowDescription = desc

	sandboxGroupID, _, eModelName, err := d.GetWorkflowVectorGroupId(ctx, tx, robotID)
	// 通过groupId的模型信息，获取embedding模型信息
	useModelInfo := config.GetUsingVectorModelInfo(ctx, eModelName)
	appInfo := getWorkflowVectorAppInfo(useModelInfo, robotID)
	if err != nil {
		return err
	}
	// https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075754831
	// f"问题:{工作流名称}\n相似问:{相似问1}\n相似问:{相似问2}\n相似问:{相似问3}......\n答案:{工作流描述}"
	// 安全地获取指定数量的示例，避免数组越界
	endIndex := useModelInfo.WorkflowCombinationExampleNum
	if endIndex > len(examples) {
		endIndex = len(examples)
	}
	examStrs := make([]string, 0, endIndex)
	if len(examples) > 0 {
		for _, v := range examples[0:endIndex] {
			if v != nil {
				examStrs = append(examStrs, v.Example)
			}
		}
	}
	embeddingRenderWorkflow.WorkflowExampleList = examStrs
	wfNameTempleStr, err := util.Render(ctx, useModelInfo.WorkflowNameTemplate, embeddingRenderWorkflow)
	if err != nil {
		log.DebugContextf(ctx, "get WorkflowNameTemplate err :%+v", err)
		return err
	}
	if len(strings.TrimSpace(useModelInfo.WorkflowCombinationTemplate)) > 0 {
		wfExamplesDescTempleStr, err = util.Render(ctx, useModelInfo.WorkflowCombinationTemplate, embeddingRenderWorkflow)
		if err != nil {
			log.DebugContextf(ctx, "get WorkflowCombinationTemplate err :%+v", err)
			return err
		}
		hasWfExamplesDescVector = true
	}

	workflowVectorOrg := &entity.WorkflowVectorOrg{
		WorkflowID:                 workflowId,
		WorkflowName:               newWorkflow.WorkflowName,
		FlowNameExamsDescID:        fmt.Sprintf(entity.FlowNameExamsDescID, workflowId),
		WorkflowNameVectorOrg:      wfNameTempleStr,
		WorkflowDesc:               newWorkflow.WorkflowDesc,
		WorkflowState:              newWorkflow.WorkflowState,
		RobotId:                    newWorkflow.RobotId,
		ReleaseStatus:              newWorkflow.ReleaseStatus,
		IsEnable:                   newWorkflow.IsEnable,
		FlowNameExamsDescVectorOrg: wfExamplesDescTempleStr,
		Action:                     newWorkflow.Action,
	}
	// 获取工作流的向量数据
	workflowEmbed, err := d.GetWorkflowEmbedding(ctx, tx, workflowVectorOrg, appInfo, eModelName)
	if err != nil {
		return err
	}
	fieldValueWorkflow := &vector_db_manager.FieldValue{
		FieldName:        FieldWorkflowId,
		FieldType:        vector_db_manager.FieldValue_STRING,
		FieldValueString: workflowId,
	}

	// 工作流的开启开关同步到向量
	fieldValueEnable := &vector_db_manager.FieldValue{
		FieldName:        FieldWorkflowEnable,
		FieldType:        vector_db_manager.FieldValue_UINT64,
		FieldValueUint64: workflowStatus, // 1:启用，0：禁用
	}

	dataList := []*vector_db_manager.AddVectorReq_Index{
		{
			Id:        workflowId,
			Embedding: workflowEmbed[workflowId],
			AttributeFields: &vector_db_manager.AttributeFields{
				Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow, fieldValueEnable},
			},
			EntityId: workflowId, // 私有化弃用
		},
	}

	// 作流名称、相似问、描述组合配置
	if hasWfExamplesDescVector {
		// 获取 组合工作流名称、相似问、描述 Embedding的数据
		workflowNameExamsDescEmbed, err := d.GetWorkflowExamDescEmbedding(ctx, tx, workflowVectorOrg, appInfo, eModelName)
		if err != nil {
			return err
		}
		if workflowNameExamsDescEmbed != nil {
			dataList = append(dataList, &vector_db_manager.AddVectorReq_Index{
				Id:        workflowVectorOrg.FlowNameExamsDescID,
				Embedding: workflowNameExamsDescEmbed[workflowVectorOrg.FlowNameExamsDescID],
				AttributeFields: &vector_db_manager.AttributeFields{
					Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow, fieldValueEnable},
				},
				EntityId: workflowVectorOrg.FlowNameExamsDescID, // 私有化弃用
			})
		}
	}

	log.InfoContextf(ctx, "len:%d|examples:%+v", len(examples), json0.Marshal2StringNoErr(examples))
	if len(examples) > 0 {
		// 包含示例问法的
		eAllIds := make([]string, 0, len(examples))
		eAllExam := make(map[string]string, 0)
		orgExamsVector := make([]*entity.WorkflowExampleVectorOrg, 0, len(examples))
		for _, v := range examples {
			eAllIds = append(eAllIds, v.ExampleID)
			//V2.9.0更新  https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075754831
			//orgExam := fmt.Sprintf("问题:%s", strings.TrimSpace(v.Example))
			orgExam, err := util.Render(ctx, useModelInfo.WorkflowExampleTemplate, v)
			if err != nil {
				log.DebugContextf(ctx, "get WorkflowCombinationTemplate err :%+v", err)
				return err
			}
			eAllExam[v.ExampleID] = orgExam
			orgExamsVector = append(orgExamsVector, &entity.WorkflowExampleVectorOrg{
				FlowID:           v.FlowID,
				ExampleID:        v.ExampleID,
				Example:          v.Example,
				ExampleVectorOrg: orgExam,
				RobotId:          v.RobotId,
			})
		}
		examplesEmbed, err := d.GetWorkflowExampleEmbedding(ctx, tx, orgExamsVector, appInfo, eModelName)
		if err != nil {
			return err
		}
		log.InfoContextf(ctx, "len:%d|examplesEmbed:%+v", len(examples),
			json0.Marshal2StringNoErr(examplesEmbed))
		for _, eIds := range types.SplitStringSlice(eAllIds, ********************************(ctx, eModelName)-2) {
			for _, id := range eIds {
				fieldValueExample := &vector_db_manager.FieldValue{
					FieldName:        FieldExampleId,
					FieldType:        vector_db_manager.FieldValue_STRING,
					FieldValueString: id,
				}
				fieldValueExampleOrg := &vector_db_manager.FieldValue{
					FieldName:        FieldExampleValue,
					FieldType:        vector_db_manager.FieldValue_STRING,
					FieldValueString: eAllExam[id], // 示例问法原文
				}
				dataList = append(dataList, &vector_db_manager.AddVectorReq_Index{
					Id:        id,                // 示例问法ID
					Embedding: examplesEmbed[id], // 示例问法向量化数据
					AttributeFields: &vector_db_manager.AttributeFields{
						Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow,
							fieldValueEnable, fieldValueExample, fieldValueExampleOrg},
					},
					EntityId: workflowId, // 私有化弃用
				})
			}
		}
		log.InfoContextf(ctx, "len:%d|examplesEmbed:%+v", len(examples), examplesEmbed)
	}

	if err = d.AddVectorByBatch(ctx, dataList, appInfo, util.RequestID(ctx), sandboxGroupID); err != nil {
		log.ErrorContextf(ctx, "SaveCorpusVector AddVector, err:%v", err)
		return err
	}
	return nil
}

// GetWorkflowExampleEmbedding 获取示例问法向量化结果
func (d dao) GetWorkflowExampleEmbedding(ctx context.Context, tx *gorm.DB, examples []*entity.WorkflowExampleVectorOrg,
	appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error) {
	// 找出需要重新embedding的示例问法，重新embedding
	var examplesVector []entity.VectorStore
	robotId := appInfo.AppKey
	needEmbeddingExam := make([]*Content, 0, len(examples))
	allWorkflowExamEmbed := make(map[string][]float32, 0)
	// 先获取exampleIds
	eIds := make([]string, 0, len(examples))
	for _, v := range examples {
		eIds = append(eIds, v.ExampleID)
	}
	if err := tx.Table(entity.VectorStore{}.TableName()).
		Where("f_is_deleted=0 AND f_type=? AND f_robot_id=? AND f_embedding_model=? AND f_biz_id IN ?",
			WorkExampleEmbedding, appInfo.AppKey, modelName, eIds).
		Find(&examplesVector).Error; err != nil {
		return nil, err
	}
	edVectorMap := make(map[string]string)
	edVectorStoreMap := make(map[string][]float32)

	// 找到需要重新embedding的示例问法
	if len(examplesVector) > 0 {
		for _, v := range examplesVector {
			edVectorMap[v.BizID] = v.Content
			edVectorStoreMap[v.BizID] = v.Vector()
		}
	}

	for _, v := range examples {
		//V2.9.0更新  https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075754831
		if v.ExampleVectorOrg != edVectorMap[v.ExampleID] {
			needEmbeddingExam = append(needEmbeddingExam, &Content{
				Text:  v.ExampleVectorOrg,
				BizId: v.ExampleID,
			})
		} else {
			allWorkflowExamEmbed[v.ExampleID] = edVectorStoreMap[v.ExampleID]
		}
	}
	newExamVectorEmbedding, err := d.GetBatchEmbedding(ctx, needEmbeddingExam, appInfo, modelName)
	if err != nil {
		return nil, err
	}
	// upInsert
	upInsertVs := make([]entity.VectorStore, 0)
	for _, v := range needEmbeddingExam {
		allWorkflowExamEmbed[v.BizId] = newExamVectorEmbedding[v.BizId]
		upInsertVs = append(upInsertVs, entity.VectorStore{
			BizID:     v.BizId,
			RobotID:   robotId,
			SaveType:  WorkExampleEmbedding,
			Content:   v.Text,
			ModelName: modelName,
		})
	}

	if newExamVectorEmbedding != nil {
		for i := range upInsertVs {
			upInsertVs[i].SetVector(newExamVectorEmbedding[upInsertVs[i].BizID])
		}
	}

	err = InsertOrUpdateVectorStores(ctx, tx, upInsertVs)
	if err != nil {
		return nil, err
	}

	return allWorkflowExamEmbed, nil
}

// GetWorkflowEmbedding 获取工作流的Embedding
func (d dao) GetWorkflowEmbedding(ctx context.Context, tx *gorm.DB,
	workflow *entity.WorkflowVectorOrg, appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error) {
	// 对比vector表中的Embedding数据，如果相同，就不Embedding直接拿结果
	var flowVector entity.VectorStore
	flowEmbedding := make(map[string][]float32, 0)
	if err := tx.Table(entity.VectorStore{}.TableName()).
		Where("f_is_deleted=0 AND f_type=? AND f_robot_id=? AND f_biz_id=? "+
			"AND f_embedding_model=?", WorkflowNameEmbedding, workflow.RobotId,
			workflow.WorkflowID, modelName).
		Find(&flowVector).Error; err != nil {
		return nil, err
	}
	log.DebugContextf(ctx, "getWorkflowEmbedding|flowVector:%+v|workflow:%+v",
		json0.Marshal2StringNoErr(flowVector), json0.Marshal2StringNoErr(workflow))
	//	 2.9.0 工作流算法策略修改 https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075754831
	//保存工作流的名称格式 f"问题:{工作流名称}"，问题:路由器上不了网
	if flowVector.Content == workflow.WorkflowNameVectorOrg {
		flowEmbedding[workflow.WorkflowID] = flowVector.Vector()
	} else {
		content := &Content{
			Text:  workflow.WorkflowNameVectorOrg,
			BizId: workflow.WorkflowID,
		}
		flowVectorEmbedding, err := d.GetEmbedding(ctx, content, appInfo, modelName)
		if err != nil {
			return nil, err
		}
		flowEmbedding[workflow.WorkflowID] = flowVectorEmbedding[workflow.WorkflowID]
		// 如果不存在则新建，存在则更新
		vs := make([]entity.VectorStore, 0)
		vs = append(vs, entity.VectorStore{
			BizID:     workflow.WorkflowID,
			RobotID:   workflow.RobotId,
			SaveType:  WorkflowNameEmbedding,
			Content:   workflow.WorkflowNameVectorOrg,
			ModelName: modelName,
		})
		log.DebugContextf(ctx, "getWorkflowEmbedding|vs:%+v", vs)
		vs[0].SetVector(flowEmbedding[workflow.WorkflowID])
		err = InsertOrUpdateVectorStores(ctx, tx, vs)
		if err != nil {
			return nil, err
		}
	}
	return flowEmbedding, nil
}

// GetWorkflowExamDescEmbedding 获取工作流名称 + 相似问（最多前10个） + 描述 的Embedding
func (d dao) GetWorkflowExamDescEmbedding(ctx context.Context, tx *gorm.DB, workflow *entity.WorkflowVectorOrg,
	appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error) {
	if workflow == nil || len(strings.TrimSpace(workflow.FlowNameExamsDescVectorOrg)) == 0 {
		return nil, nil
	}
	// 对比vector表中的Embedding数据，如果相同，就不Embedding直接拿结果
	var flowVector entity.VectorStore
	flowEmbedding := make(map[string][]float32, 0)
	if err := tx.Table(entity.VectorStore{}.TableName()).
		Where("f_is_deleted=0 AND f_type=? AND f_robot_id=? AND f_biz_id=? "+
			"AND f_embedding_model=?", WorkflowNameExamsDescEmbedding, workflow.RobotId,
			workflow.FlowNameExamsDescID, modelName).
		Find(&flowVector).Error; err != nil {
		return nil, err
	}
	log.DebugContextf(ctx, "getWorkflowEmbedding|flowVector:%+v|workflow:%+v",
		json0.Marshal2StringNoErr(flowVector), json0.Marshal2StringNoErr(workflow))
	//	 2.9.0 工作流算法策略修改 https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075754831
	//保存格式 f"问题:{工作流名称}\n相似问:{相似问1}\n相似问:{相似问2}\n相似问:{相似问3}......\n答案:{工作流描述}"
	if flowVector.Content == workflow.FlowNameExamsDescVectorOrg {
		flowEmbedding[workflow.FlowNameExamsDescID] = flowVector.Vector()
	} else {
		content := &Content{
			Text:  workflow.FlowNameExamsDescVectorOrg,
			BizId: workflow.FlowNameExamsDescID,
		}
		flowVectorEmbedding, err := d.GetEmbedding(ctx, content, appInfo, modelName)
		if err != nil {
			return nil, err
		}
		flowEmbedding[workflow.FlowNameExamsDescID] = flowVectorEmbedding[workflow.FlowNameExamsDescID]
		// 如果不存在则新建，存在则更新
		vs := make([]entity.VectorStore, 0)
		vs = append(vs, entity.VectorStore{
			BizID:     workflow.FlowNameExamsDescID,
			RobotID:   workflow.RobotId,
			SaveType:  WorkflowNameExamsDescEmbedding,
			Content:   workflow.FlowNameExamsDescVectorOrg,
			ModelName: modelName,
		})
		log.DebugContextf(ctx, "getWorkflowEmbedding|vs:%+v", vs)
		vs[0].SetVector(flowEmbedding[workflow.FlowNameExamsDescID])
		err = InsertOrUpdateVectorStores(ctx, tx, vs)
		if err != nil {
			return nil, err
		}
	}
	return flowEmbedding, nil
}

// InsertOrUpdateVectorStores 保存向量
func InsertOrUpdateVectorStores(ctx context.Context, tx *gorm.DB, vectorStores []entity.VectorStore) error {
	sid := util.RequestID(ctx)
	if len(vectorStores) == 0 {
		return nil
	}
	log.InfoContextf(ctx, "sid:%s|InsertOrUpdateVectorStores|vectorStores:%+v",
		sid, json0.Marshal2StringNoErr(vectorStores))
	if err := tx.WithContext(ctx).Table(entity.VectorStore{}.TableName()).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "f_biz_id"}, {Name: "f_robot_id"}, {Name: "f_type"}}, // 假设这是您用于检测冲突的列
			DoUpdates: clause.AssignmentColumns([]string{"f_content", "f_embedding_model", "f_vector"}),
		}).Create(&vectorStores).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|InsertOrUpdateVectorStores|err:%v", sid, err)
		return err
	}
	return nil
}

// GetWorkflowVectorGroupId 获取意图向量GroupId，没有就创建
func (d dao) GetWorkflowVectorGroupId(ctx context.Context, tx *gorm.DB, robotID string) (string, string, string, error) {
	// 从数据库中查找
	workflowCorpusVectorInfo, err := GetWorkflowVectorInfoByRobotIdAndType(ctx, tx, robotID, entity.SaveWorkflowType)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowVectorGroupId GetWorkflowVectorInfoByRobotIdAndType Failed "+
			"err:%v", err)
		return "", "", "", err
	}
	if len(workflowCorpusVectorInfo) == 0 {
		workflowCorpusVectorInfo = buildWorkFlowVectorInfo(ctx, robotID, entity.SaveWorkflowType, "")
		// 表t_vector_group里面没有，则创建workflowVectorGroup
		if err := tx.Model(&entity.WorkflowVectorGroup{}).
			CreateInBatches(workflowCorpusVectorInfo, 20).Error; err != nil {
			log.ErrorContextf(ctx, "GetWorkflowVectorGroupId CreateVectorGroup err:%+v", err)
			return "", "", "", err
		}
		log.InfoContextf(ctx, "GetWorkflowVectorGroupId CreateVectorGroup Success VectorInfo:%+v",
			workflowCorpusVectorInfo)
		//	调用向量接口存储
		_, _, err := d.CreateBotWorkflowGroup(ctx, robotID, workflowCorpusVectorInfo)
		if err != nil {
			log.ErrorContextf(ctx, "GetWorkflowVectorGroupId CreateBotWorkflowGroup Failed err:%v", err)
			return "", "", "", err
		}

		// 向量通知DM,只需要sandbox的部分
		req := &KEP_WF_DM.UpsertAppToSandboxRequest{
			AppID:                    robotID,
			RetrievalWorkflowGroupID: workflowCorpusVectorInfo[0].VectorGroupID,
			RetrievalWorkflowModel:   workflowCorpusVectorInfo[0].EmbeddingModeName,
		}
		if _, err := rpc.UpsertAppToSandbox(ctx, req); err != nil {
			log.ErrorContextf(ctx, "GetWorkflowVectorGroupId UpsertAppToSandboxRequest Failed err:%v", err)
			return "", "", "", err
		}
	}
	sandboxGroupID, prodGroupID := getWorkflowSandboxProdGroupID(workflowCorpusVectorInfo)
	return sandboxGroupID, prodGroupID, workflowCorpusVectorInfo[0].EmbeddingModeName, nil
}

func (d dao) CreateBotWorkflowGroup(ctx context.Context, robotID string, vectorInfo []entity.WorkflowVectorGroup) (
	sandboxGroupID string, prodGroupID string, err error) {
	log.InfoContextf(ctx, "CreateBotWorkflowGroup, robotID:%s", robotID)
	modelName := vectorInfo[0].EmbeddingModeName
	requestID := util.RequestID(ctx)
	usingModelInfo := config.GetUsingVectorModelInfo(ctx, modelName)
	appInfo := getWorkflowVectorAppInfo(usingModelInfo, robotID)
	sandboxGroupID, prodGroupID = getWorkflowSandboxProdGroupID(vectorInfo)
	defer func() {
		if err != nil {
			_ = d.DeleteBotWorkflowGroup(ctx, robotID, usingModelInfo)
		}
	}()

	reqSandbox := &vector_db_manager.CreateGroupReq{
		RequestId: requestID,
		GroupId:   sandboxGroupID,
		AppInfo:   appInfo,
		UseVdb:    usingModelInfo.WorkflowUseVdb,
	}
	log.InfoContextf(ctx, "CreateBotWorkflowGroup CreateGroup reqSandbox:%s", reqSandbox.String())
	rspSandbox, err := d.client.CreateGroup(ctx, reqSandbox)
	if err != nil || rspSandbox == nil || (rspSandbox.Code != 0 && rspSandbox.Code != 71014) {
		err = fmt.Errorf("rspSandbox:%+v, err:%v", rspSandbox, err)
		log.ErrorContextf(ctx, "CreateBotWorkflowGroup CreateGroup err:%v", err)
		return sandboxGroupID, prodGroupID, err
	}

	reqProd := &vector_db_manager.CreateGroupReq{
		RequestId: requestID,
		GroupId:   prodGroupID,
		AppInfo:   appInfo,
		UseVdb:    usingModelInfo.WorkflowUseVdb,
	}
	log.InfoContextf(ctx, "CreateBotWorkflowGroup CreateGroup reqProd:%s", reqProd.String())
	rspProd, err := d.client.CreateGroup(ctx, reqProd)
	if err != nil || rspProd == nil || (rspProd.Code != 0 && rspProd.Code != 71014) {
		err = fmt.Errorf("rspProd:%+v, err:%v", rspProd, err)
		log.ErrorContextf(ctx, "CreateBotWorkflowGroup CreateGroup, err:%v", err)
		return sandboxGroupID, prodGroupID, err
	}

	log.InfoContextf(ctx, "CreateBotWorkflowGroup success, sandboxGroupID:%s, prodGroupID:%s",
		sandboxGroupID, prodGroupID)
	return sandboxGroupID, prodGroupID, nil
}

func (d dao) DeleteBotWorkflowGroup(ctx context.Context, robotID string, useModelInfo *config.UsingVectorModelInfo) error {
	log.InfoContextf(ctx, "DeleteBotWorkflowGroup, robotID:%s", robotID)
	appInfo := &vector_db_manager.AppInfo{
		Biz:    useModelInfo.Biz,
		AppKey: robotID,
		Secret: useModelInfo.Secret,
	}
	requestID := util.RequestID(ctx)
	sandboxGroupID, prodGroupID := GetWorkFlowVectorGroupIDStr(ctx, robotID, entity.SaveWorkflowType, useModelInfo.Biz, useModelInfo.GroupSuffix)
	reqSandbox := &vector_db_manager.DeleteGroupReq{
		RequestId: requestID,
		GroupId:   sandboxGroupID,
		AppInfo:   appInfo,
	}
	log.InfoContextf(ctx, "DeleteBotWorkflowGroup DeleteGroup reqSandbox:%s", reqSandbox.String())
	rspSandbox, err := d.client.DeleteGroup(ctx, reqSandbox)
	if err != nil || rspSandbox == nil || (rspSandbox.Code != 0 && rspSandbox.Code != 71014) {
		err = fmt.Errorf("rspSandbox:%+v, err:%v", rspSandbox, err)
		log.ErrorContextf(ctx, "DeleteBotWorkflowGroup DeleteGroup, err:%v", err)
		return err
	}

	reqProd := &vector_db_manager.DeleteGroupReq{
		RequestId: requestID,
		GroupId:   prodGroupID,
		AppInfo:   appInfo,
	}
	log.InfoContextf(ctx, "DeleteBotWorkflowGroup CreateGroup reqProd:%s", reqProd.String())
	rspProd, err := d.client.DeleteGroup(ctx, reqProd)
	if err != nil || rspProd == nil || (rspProd.Code != 0 && rspProd.Code != 71014) {
		err = fmt.Errorf("rspProd:%+v, err:%v", rspProd, err)
		log.ErrorContextf(ctx, "DeleteBotWorkflowGroup CreateGroup, err:%v", err)
		return err
	}

	log.InfoContextf(ctx, "DeleteBotCorpusGroup success, sandboxGroupID:%s, prodGroupID:%s",
		sandboxGroupID, prodGroupID)
	return nil
}

// GetWorkflowVectorGroupSandboxAndProdIdFromDB 从DB获取向量组的GroupID
func GetWorkflowVectorGroupSandboxAndProdIdFromDB(ctx context.Context,
	robotId, saveType string) (string, string, string, error) {
	var vectorInfo []entity.WorkflowVectorGroup
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Model(&entity.WorkflowVectorGroup{}).
		Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?",
			robotId, saveType).
		Find(&vectorInfo).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowVectorGroupSandboxAndProdIdFromDB db err:%v", err)
		return "", "", "", err
	}
	sandboxGroupId, prodGroupId := getWorkflowSandboxProdGroupID(vectorInfo)

	if len(vectorInfo) == 0 && saveType == Workflow {
		vdb := NewDao()
		vectorInfo = buildWorkFlowVectorInfo(ctx, robotId, saveType, "")
		sandboxGroupId, prodGroupId, err = vdb.CreateBotWorkflowGroup(ctx, robotId, vectorInfo)
		if err != nil {
			log.ErrorContextf(ctx, "GetWorkflowVectorGroupSandboxAndProdIdFromDB db err:%v", err)
			return "", "", "", nil
		}
	}
	return sandboxGroupId, prodGroupId, vectorInfo[0].EmbeddingModeName, nil
}

// GetWorkflowVectorInfoByRobotIdAndType 通过robotId和type到 VectorGroup 查询 向量组
func GetWorkflowVectorInfoByRobotIdAndType(ctx context.Context, tx *gorm.DB,
	robotId, saveType string) ([]entity.WorkflowVectorGroup, error) {
	var vectorInfo []entity.WorkflowVectorGroup
	err := tx.Model(&entity.WorkflowVectorGroup{}).
		Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?",
			robotId, saveType).Find(&vectorInfo).Error
	log.InfoContextf(ctx, "GetWorkflowVectorInfoByRobotIdAndType result:%+v", vectorInfo)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowVectorInfoByRobotIdAndType err:%+v", err)
		return vectorInfo, err
	}
	return vectorInfo, nil
}

// buildWorkFlowVectorInfo 构建vectorInfo对象信息
func buildWorkFlowVectorInfo(ctx context.Context, robotID, saveType, modelName string) []entity.WorkflowVectorGroup {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	useModelInfo := config.GetUsingVectorModelInfo(ctx, modelName)
	sandboxGroup, prodGroup := GetWorkFlowVectorGroupIDStr(ctx, robotID, saveType, useModelInfo.Biz, useModelInfo.GroupSuffix)

	return []entity.WorkflowVectorGroup{
		{
			VectorGroupID:     sandboxGroup,
			VectorGroupType:   WorkflowSandboxGroupInfix,
			RobotID:           robotID,
			SaveType:          saveType,
			EmbeddingModeName: useModelInfo.LatestEmbeddingModelName,
			UIN:               uin,
			SubUIN:            subUin,
		},
		{
			VectorGroupID:     prodGroup,
			VectorGroupType:   WorkflowProdGroupInfix,
			RobotID:           robotID,
			SaveType:          saveType,
			EmbeddingModeName: useModelInfo.LatestEmbeddingModelName,
			UIN:               uin,
			SubUIN:            subUin,
		},
	}
}

// GetWorkFlowVectorGroupIDStr 构建vector group ID
func GetWorkFlowVectorGroupIDStr(ctx context.Context, robotID, saveType, biz string,
	suffixVersion string) (sandboxGroupID, prodGroupID string) {
	sandboxGroupID = fmt.Sprintf("%s-%s-%s-%s", biz, saveType, WorkflowSandboxGroupInfix, robotID)
	prodGroupID = fmt.Sprintf("%s-%s-%s-%s", biz, saveType, WorkflowProdGroupInfix, robotID)
	suffix := strings.TrimSpace(suffixVersion)
	if len(suffix) > 0 {
		return fmt.Sprintf("%s-%s", sandboxGroupID, suffixVersion), fmt.Sprintf("%s-%s", prodGroupID, suffixVersion)
	}
	return sandboxGroupID, prodGroupID
}

// getWorkflowSandboxProdGroupID 通过 VectorGroup 获取 groupId
func getWorkflowSandboxProdGroupID(vectorInfo []entity.WorkflowVectorGroup) (sandboxGroupID, prodGroupID string) {
	for _, v := range vectorInfo {
		if v.VectorGroupType == WorkflowSandboxGroupInfix {
			sandboxGroupID = v.VectorGroupID
		}
		if v.VectorGroupType == WorkflowProdGroupInfix {
			prodGroupID = v.VectorGroupID
		}
	}
	return sandboxGroupID, prodGroupID
}

func ********************************(ctx context.Context, eModelName string) int {
	return config.GetUsingVectorModelInfo(ctx, eModelName).OperationMaxIDs
}

// GetWfVectorEnableByFlowState 根据停启用开关状态和画布编辑状态计算向量可用状态
// 1表示可用，0表示不可用
func GetWfVectorEnableByFlowState(flowState string, isEnable bool) uint64 {
	// 仅画布状态是Enable或者PublishedChange，而且停启用开关状态是启用状态时，向量状态才是可用
	if (flowState == entity.WorkflowStateEnable || flowState == entity.WorkflowStatePublishedChange) && isEnable {
		return 1
	}
	return 0
}

// WorkflowEnableSetRedis 同步工作流开关可用状态到redis
func WorkflowEnableSetRedis(ctx context.Context, robotID, flowId, env string, enable uint64) error {
	key := fmt.Sprintf(entity.WfEnableRedisKey, env, robotID)
	log.InfoContextf(ctx, "WorkflowEnableSetRedis|key:%s|flowId:%s|enable:%+v", key, flowId, enable)
	err := database.GetRedis().HSet(ctx, key, flowId, enable).Err()
	if err != nil {
		return err
	}
	return nil
}

func getWorkflowVectorAppInfo(vectorInfo *config.UsingVectorModelInfo, robotID string) *vector_db_manager.AppInfo {
	appInfo := &vector_db_manager.AppInfo{
		Biz:    vectorInfo.Biz,
		AppKey: robotID,
		Secret: vectorInfo.Secret,
	}
	return appInfo
}

// GetWorkflowVectorInfo 通过 robotId和saveType 到 VectorGroup 查询向量组
func (d dao) GetWorkflowVectorInfo(ctx context.Context, tx *gorm.DB, robotID, saveType string) ([]entity.WorkflowVectorGroup, error) {
	var vectorInfo []entity.WorkflowVectorGroup
	err := tx.Model(&entity.WorkflowVectorGroup{}).
		Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?", robotID, saveType).
		Find(&vectorInfo).Error
	if err != nil {
		log.WarnContextf(ctx, "CheckWorkflowVectorInfoUpgrade err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "CheckWorkflowVectorInfoUpgrade result:%+v", vectorInfo)
	if len(vectorInfo) == 0 {
		return nil, errors.ErrVectorGroupNotFound
	}
	return vectorInfo, nil
}

// GetNeedUpgradeWorkflowVectorAppIDs 通过 saveType和模型名 到 VectorGroup 查询待升级的应用ID
func (d dao) GetNeedUpgradeWorkflowVectorAppIDs(ctx context.Context, tx *gorm.DB, saveType, modelName string) ([]string, error) {
	var appIDs []string
	err := tx.Model(&entity.WorkflowVectorGroup{}).
		Where("f_is_deleted = 0 and f_save_type = ? and f_embedding_mode_name != ?", saveType, modelName).
		Distinct().Pluck("f_robot_id", &appIDs).Error
	if err != nil {
		log.WarnContextf(ctx, "GetNeedUpgradeWorkflowVectorAppIDs err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetNeedUpgradeWorkflowVectorAppIDs result:%+v", appIDs)
	return appIDs, nil
}

// CheckWorkflowVectorInfoUpgrade 通过 robotId和saveType 到 VectorGroup 查询向量组的升级状态，升级完成返回nil，否则返回err
func (d dao) CheckWorkflowVectorInfoUpgrade(ctx context.Context, tx *gorm.DB, robotID, saveType string) error {
	vectorInfo, err := d.GetWorkflowVectorInfo(ctx, tx, robotID, saveType)
	log.InfoContextf(ctx, "CheckWorkflowVectorInfoUpgrade result:%+v", vectorInfo)
	if err != nil {
		log.WarnContextf(ctx, "CheckWorkflowVectorInfoUpgrade err:%+v", err)
		return err
	}
	for _, v := range vectorInfo {
		if v.IsUpgrading() {
			return errors.ErrVectorGroupUpgrading
		}
	}
	return nil
}

func (d dao) MarkWorkflowVectorUpgrade(ctx context.Context, tx *gorm.DB, robotID, saveType string) error {
	log.InfoContextf(ctx, "MarkWorkflowVectorUpgrade|robotID:%s|saveType:%s", robotID, saveType)
	err := tx.Transaction(func(tx *gorm.DB) error {
		return tx.Model(&entity.WorkflowVectorGroup{}).
			Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?", robotID, saveType).
			Updates(map[string]interface{}{
				"f_upgrade_status": 1,
			}).Error
	})
	if err != nil {
		log.WarnContextf(ctx, "MarkWorkflowVectorUpgrade err:%+v", err)
		return err
	}
	return nil
}

func (d dao) MarkWorkflowVectorUpgradeDone(ctx context.Context, tx *gorm.DB, robotID, saveType string) error {
	log.InfoContextf(ctx, "MarkWorkflowVectorUpgradeDone|robotID:%s|saveType:%s", robotID, saveType)
	err := tx.Transaction(func(tx *gorm.DB) error {
		return tx.Model(&entity.WorkflowVectorGroup{}).
			Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?", robotID, saveType).
			Updates(map[string]interface{}{
				"f_upgrade_status": 0,
			}).Error
	})
	if err != nil {
		log.WarnContextf(ctx, "MarkWorkflowVectorUpgradeDone err:%+v", err)
		return err
	}
	return nil
}

func (d dao) UpdateWorkflowVectorGroupIDAndModelName(ctx context.Context, tx *gorm.DB,
	robotID, saveType, groupType, groupID, modelName string) error {
	log.InfoContextf(ctx, "UpdateWorkflowVectorGroupIDAndModelName|robotID:%s|saveType:%s|groupID:%s", robotID, saveType, groupID)
	// 校验groupID和modelName参数的有效性
	if groupID == "" || modelName == "" {
		return fmt.Errorf("groupID and modelName cannot be empty")
	}
	err := tx.Transaction(func(tx *gorm.DB) error {
		return tx.Model(&entity.WorkflowVectorGroup{}).
			Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ? and f_vector_group_type = ?", robotID, saveType, groupType).
			Updates(map[string]interface{}{
				"f_vector_group_id":     groupID,
				"f_embedding_mode_name": modelName,
			}).Error
	})
	if err != nil {
		log.WarnContextf(ctx, "UpdateWorkflowVectorGroupIDAndModelName err:%+v", err)
		return err
	}
	return nil
}
