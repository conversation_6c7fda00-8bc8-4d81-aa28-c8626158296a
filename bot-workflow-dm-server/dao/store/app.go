package store

import (
	"context"
	"encoding/json"
	"fmt"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/go-redis/redis/v8"
)

var saveAppScript = redis.NewScript(`
	local appKey = KEYS[1]

	local appID = ARGV[1]
	local retrievalWorkflowGroupID = ARGV[2]
	local retrievalWorkflowModel = ARGV[3]

	local appStr = redis.call('GET', appKey)
	local success
	local app = {}
	if appStr then
		success, app = pcall(cjson.decode, appStr)
		if not success then
		  return 'Failed to decode JSON: ' .. appStr
		end
	end

	if appID ~= "" then
		app.AppID = appID
	end
	if retrievalWorkflowGroupID ~= "" then
		app.RetrievalWorkflowGroupID = retrievalWorkflowGroupID
	end
	if retrievalWorkflowModel ~= "" then
		app.RetrievalWorkflowModel = retrievalWorkflowModel
	end

	local newAppStr = cjson.encode(app)
	redis.call('SET', appKey, newAppStr)
	return 'success'
`)

// releaseWorkflowAppScript 发布机器人。1. 从沙箱环境拷贝参数、工作流、自定义变量到正式环境； 2. 正式环境删除参数、工作流、自定义变量； 3. 正式环境新增or更新机器人
var releaseWorkflowAppScript = redis.NewScript(`
	local prodAppKey = KEYS[1]
	local sandboxWorkflowsKey = KEYS[2]
	local prodWorkflowsKey = KEYS[3]
	local sandboxParametersKey = KEYS[4]
	local prodParametersKey = KEYS[5]
	local sandboxVariablesKey = KEYS[6]
	local prodVariablesKey = KEYS[7]
	local prodWorkflowReleaseTimesKey = KEYS[8]

	local updateDataStr = ARGV[1]
	local workflowReleaseTimesStr = ARGV[2]

	-- 数据检查
	local success, updateData = pcall(cjson.decode, updateDataStr)
	if not success then
	  return 'Failed to decode JSON: ' .. updateDataStr
	end
	if updateData.UpsertWorkflowIDs ~= nil and type(updateData.UpsertWorkflowIDs) ~= 'table' then
	  return 'updateData.UpsertWorkflowIDs is not an array'
	end
	if updateData.DeleteWorkflowIDs ~= nil and type(updateData.DeleteWorkflowIDs) ~= 'table' then
	  return 'updateData.DeleteWorkflowIDs is not an array'
	end
	if updateData.UpsertParameterIDs ~= nil and type(updateData.UpsertParameterIDs) ~= 'table' then
	  return 'updateData.UpsertParameterIDs is not an array'
	end
	if updateData.DeleteParameterIDs ~= nil and type(updateData.DeleteParameterIDs) ~= 'table' then
	  return 'updateData.DeleteParameterIDs is not an array'
	end

	-- 解析workflow release times
	local success, workflowReleaseTimes = pcall(cjson.decode, workflowReleaseTimesStr)
	if not success then
	  return 'Failed to decode workflowReleaseTimes JSON: ' .. workflowReleaseTimesStr
	end

	-- 更新应用
	redis.call('SET', prodAppKey, updateData.AppData)

	-- 新增/更新工作流（增量更新）
	if updateData.UpsertWorkflowIDs ~= nil then
		for i, upsertWorkflowID in ipairs(updateData.UpsertWorkflowIDs) do
			local workflowStr = redis.call('HGET', sandboxWorkflowsKey, upsertWorkflowID)
			if not workflowStr then
				return 'upsertWorkflowID ' .. upsertWorkflowID .. ' not found'
			end
			
			-- 更新ReleaseTime到单独的hash
			if workflowReleaseTimes[upsertWorkflowID] then
				redis.call('HSET', prodWorkflowReleaseTimesKey, upsertWorkflowID, workflowReleaseTimes[upsertWorkflowID])
			end
			
			redis.call('HSET', prodWorkflowsKey, upsertWorkflowID, workflowStr)
		end
	end
	-- 删除工作流
	if updateData.DeleteWorkflowIDs ~= nil then
		for j, deleteWorkflowID in ipairs(updateData.DeleteWorkflowIDs) do
			redis.call('HDEL', prodWorkflowsKey, deleteWorkflowID)
			redis.call('HDEL', prodWorkflowReleaseTimesKey, deleteWorkflowID)
		end
	end

	-- 新增/更新参数（增量更新）
	if updateData.UpsertParameterIDs ~= nil then
		for i, upsertParameterID in ipairs(updateData.UpsertParameterIDs) do
			local workflowStr = redis.call('HGET', sandboxParametersKey, upsertParameterID)
			if not workflowStr then
				return 'upsertParameterID ' .. upsertParameterID .. ' not found'
			end
			redis.call('HSET', prodParametersKey, upsertParameterID, workflowStr)
		end
	end
	-- 删除参数
	if updateData.DeleteParameterIDs ~= nil then
		for j, deleteParameterID in ipairs(updateData.DeleteParameterIDs) do
			redis.call('HDEL', prodParametersKey, deleteParameterID)
		end
	end

	-- 更新自定义变量。（全量重新写入）
	local hash = redis.call('HGETALL', sandboxVariablesKey)
	redis.call('DEL', prodVariablesKey)
	for i = 1, #hash, 2 do
		redis.call('HSET', prodVariablesKey, hash[i], hash[i + 1])
	end

	return 'success'
`)

// deleteWorkflowAppScript 删除应用。 分别在沙箱和正式环境中执行：1. 删除意图； 2. 删除对话树； 3. 删除槽位； 4. 删除机器人。
var deleteWorkflowAppScript = redis.NewScript(`
	local sandboxAppKey = KEYS[1]
	local prodAppKey = KEYS[2]
	local sandboxWorkflowsKey = KEYS[3]
	local prodWorkflowsKey = KEYS[4]
	local sandboxParametersKey = KEYS[5]
	local prodParametersKey = KEYS[6]
	local sandboxVariablesKey = KEYS[7]
	local prodVariablesKey = KEYS[8]

	
	redis.call('DEL', sandboxAppKey)
	redis.call('DEL', prodAppKey)
	redis.call('DEL', sandboxWorkflowsKey)
	redis.call('DEL', prodWorkflowsKey)
	redis.call('DEL', sandboxParametersKey)
	redis.call('DEL', prodParametersKey)
	redis.call('DEL', sandboxVariablesKey)
	redis.call('DEL', prodVariablesKey)
	
	return 'success'
`)

// GetAppKey 应用的存储KEY
func GetAppKey(runEnv KEP_WF_DM.RunEnvType, appID string) string {
	return fmt.Sprintf(tconst.AppKeyFormat, getEnvKey(runEnv), appID)
}

// SaveAppInSandbox 保存应用
func (r *RedisStore) SaveAppInSandbox(ctx context.Context, req *KEP_WF_DM.UpsertAppToSandboxRequest) error {
	LogRedis(ctx).Infof("SaveAppInSandbox, req: %v,", util.Pb2String(req))

	appKey := GetAppKey(KEP_WF_DM.RunEnvType_SANDBOX, req.AppID)
	result, err := saveAppScript.Run(ctx, r.client, []string{appKey},
		[]string{req.AppID, req.RetrievalWorkflowGroupID, req.RetrievalWorkflowModel}).Result()
	if err != nil {
		Log(ctx).Errorf("run saveAppScript failed, error: %v", err)
		return err
	}
	if result != "success" {
		Log(ctx).Errorf("saveAppScript failed, error: %v", result)
		return fmt.Errorf("saveAppScript failed, error: %v", result)
	}

	LogRedis(ctx).Infof("SaveAppInSandbox, done")
	return nil
}

// ReleaseWorkflowApp 发布应用
func (r *RedisStore) ReleaseWorkflowApp(ctx context.Context, appInfo *KEP_WF_DM.ReleaseWorkflowAppRequest) error {
	LogRedis(ctx).Infof("ReleaseWorkflowApp, appInfo: %v", util.Pb2String(appInfo))
	prodAppKey := GetAppKey(KEP_WF_DM.RunEnvType_PRODUCT, appInfo.AppID)

	sandboxWorkflowsKey := GetWorkflowsKey(KEP_WF_DM.RunEnvType_SANDBOX, appInfo.AppID)
	prodWorkflowsKey := GetWorkflowsKey(KEP_WF_DM.RunEnvType_PRODUCT, appInfo.AppID)

	sandboxParametersKey := GetParametersKey(KEP_WF_DM.RunEnvType_SANDBOX, appInfo.AppID)
	prodParametersKey := GetParametersKey(KEP_WF_DM.RunEnvType_PRODUCT, appInfo.AppID)

	sandboxVariablesKey := GetVariablesKey(KEP_WF_DM.RunEnvType_SANDBOX, appInfo.AppID)
	prodVariablesKey := GetVariablesKey(KEP_WF_DM.RunEnvType_PRODUCT, appInfo.AppID)

	prodWorkflowReleaseTimesKey := GetWorkflowReleaseTimesKey(KEP_WF_DM.RunEnvType_PRODUCT, appInfo.AppID)

	prodApp := &KEP_WF_DM.UpsertAppToSandboxRequest{
		AppID:                    appInfo.AppID,
		RetrievalWorkflowGroupID: appInfo.RetrievalWorkflowGroupID,
		RetrievalWorkflowModel:   appInfo.RetrievalWorkflowModel,
		// RetrievalEntryGroupID:    appInfo.RetrievalEntryGroupID,
	}
	prodAppData := util.ToJsonString(prodApp)
	updateData := struct {
		AppData            string   `json:"AppData,omitempty"`
		UpsertWorkflowIDs  []string `json:"UpsertWorkflowIDs,omitempty"`
		DeleteWorkflowIDs  []string `json:"DeleteWorkflowIDs,omitempty"`
		UpsertParameterIDs []string `json:"UpsertParameterIDs,omitempty"`
		DeleteParameterIDs []string `json:"DeleteParameterIDs,omitempty"`
	}{
		AppData:            prodAppData,
		UpsertWorkflowIDs:  appInfo.UpsertWorkflowIDs,
		DeleteWorkflowIDs:  appInfo.DeleteWorkflowIDs,
		UpsertParameterIDs: appInfo.UpsertParameterIDs,
		DeleteParameterIDs: appInfo.DeleteParameterIDs,
	}

	keys := []string{
		prodAppKey,
		sandboxWorkflowsKey,
		prodWorkflowsKey,
		sandboxParametersKey,
		prodParametersKey,
		sandboxVariablesKey,
		prodVariablesKey,
		prodWorkflowReleaseTimesKey,
	}
	updateDataStr := util.ToJsonString(updateData)
	workflowReleaseTimesStr := util.ToJsonString(appInfo.WorkflowReleaseTimes)
	if appInfo.WorkflowReleaseTimes == nil {
		workflowReleaseTimesStr = "{}"
	}
	result, err := releaseWorkflowAppScript.Run(ctx, r.client, keys,
		[]string{updateDataStr, workflowReleaseTimesStr}).Result()
	if err != nil {
		Log(ctx).Errorf("run releaseWorkflowAppScript failed, error: %v", err)
		return err
	}
	if result != "success" {
		Log(ctx).Errorf("ReleaseWorkflowApp failed, result: %v", result)
		return fmt.Errorf("ReleaseWorkflowApp failed, result: %v", result)
	}
	return nil
}

// DeleteWorkflowApp 删除应用
func (r *RedisStore) DeleteWorkflowApp(ctx context.Context, appID string) error {
	LogRedis(ctx).Infof("DeleteWorkflowApp, appID: %v,", appID)
	sandboxAppKey := GetAppKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)
	prodAppKey := GetAppKey(KEP_WF_DM.RunEnvType_PRODUCT, appID)

	sandboxWorkflowsKey := GetWorkflowsKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)
	prodWorkflowsKey := GetWorkflowsKey(KEP_WF_DM.RunEnvType_PRODUCT, appID)

	sandboxParametersKey := GetParametersKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)
	prodParametersKey := GetParametersKey(KEP_WF_DM.RunEnvType_PRODUCT, appID)

	sandboxVariablesKey := GetVariablesKey(KEP_WF_DM.RunEnvType_SANDBOX, appID)
	prodVariablesKey := GetVariablesKey(KEP_WF_DM.RunEnvType_PRODUCT, appID)

	keys := []string{
		sandboxAppKey,
		prodAppKey,
		sandboxWorkflowsKey,
		prodWorkflowsKey,
		sandboxParametersKey,
		prodParametersKey,
		sandboxVariablesKey,
		prodVariablesKey,
	}
	result, err := deleteWorkflowAppScript.Run(ctx, r.client, keys).Result()
	if err != nil {
		Log(ctx).Errorf("run deleteWorkflowAppScript failed, error: %v", err)
		return err
	}
	if result != "success" {
		Log(ctx).Errorf("DeleteWorkflowApp failed, result: %v", result)
		return fmt.Errorf("DeleteWorkflowApp failed, result: %v", result)
	}
	return nil
}

// GetApp 获取机器人信息
func (r *RedisStore) GetApp(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID, workflowID string) (*entity.App,
	error) {
	// 获取机器人信息
	appKey := GetAppKey(runEnv, appID)
	appStr, err := r.client.Get(ctx, appKey).Result()
	if err != nil {
		return nil, err
	}
	app := &entity.App{}
	if err = json.Unmarshal([]byte(appStr), app); err != nil {
		Log(ctx).Errorf("invalid appStr: %v", appStr)
		return nil, err
	}

	if workflowID == AllWorkflows {
		app.Workflows, err = r.GetWorkflows(ctx, runEnv, appID)
		if err != nil {
			Log(ctx).Errorf("GetWorkflows failed error: %v", err)
			return nil, err
		}
	} else if workflowID != "" {
		app.Workflows = make(map[string]*KEP_WF.Workflow, 1)
		workflowIDs := []string{workflowID}
		// 递归获取所有关联的workflow
		for len(workflowIDs) > 0 {
			newWorkflowIDs := make([]string, 0)
			for _, wfID := range workflowIDs {
				workflow, err := r.GetWorkflow(ctx, runEnv, appID, wfID)
				if err != nil {
					Log(ctx).Errorf("GetWorkflows failed error: %v", err)
					return nil, err
				}
				app.Workflows[wfID] = workflow

				for _, node := range workflow.Nodes {
					newWorkflowID := util.GetSubWorkflowID(node)
					if newWorkflowID != "" {
						newWorkflowIDs = append(newWorkflowIDs, newWorkflowID)
					}
				}
			}
			workflowIDs = newWorkflowIDs
		}
	} else {
		app.Workflows = make(map[string]*KEP_WF.Workflow, 0)
	}

	app.Parameters, err = r.GetParameters(ctx, runEnv, appID)
	if err != nil {
		Log(ctx).Errorf("GetParameters failed error: %v", err)
		return nil, err
	}

	// 获取自定义参数
	app.Variables, err = r.GetVariables(ctx, runEnv, appID)
	if err != nil {
		Log(ctx).Errorf("GetVariables failed error: %v", err)
		return nil, err
	}
	return app, nil
}
