package agent

import (
	"context"
	"regexp"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
)

// getMainAgentPrompt 获取主AgentPrompt
func (a *ChatAgentImpl) getMainAgentPrompt(ctx context.Context, bs *botsession.BotSession) string {
	// 1. 首先获取当前 agent 使用的模型
	currAgent, ok := a.agentContainer.GetSingleAgentByName(bs.AgentStatus.CurrentAgent)
	if !ok {
		// 预期不可能走到这里
		log.ErrorContextf(ctx, "it's impossible to get here, touch off bugs")
		return ""
	}
	// 2. 如果是 R1 模型单独处理
	if currAgent.Model != nil && strings.HasPrefix(currAgent.Model.ModelName, DeepSeekModelPrefix) {
		return a.getMainAgentPromptR1(ctx, bs)
	}

	// 3. 在 APP 中获取 prompt
	return a.getMainAgentPromptNormal(ctx, bs)
}

// getMainAgentPromptR1 获取主AgentPrompt or deep seek
func (a *ChatAgentImpl) getMainAgentPromptR1(ctx context.Context, bs *botsession.BotSession) string {
	prompt := ""
	// SystemRole 映射到多 agent 的 instructions
	systemRole, _ := a.agentContainer.GetAgentInstructions(bs.AgentStatus.CurrentAgent)
	if bs.App.GetKnowledgeQa().GetWorkflow().GetUsePdl() || config.IsUsePdl(bs.App.GetAppBizId()) { // 启用PDL
		if systemRole != "" { // 带task goal
			prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMainWorkflowTaskGoalR1)
		} else {
			prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMainWorkflowR1)
		}
	} else {
		if systemRole != "" { // 带task goal
			prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMainTaskGoalR1)
		} else {
			prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMainR1)
		}
	}
	log.InfoContextf(ctx, "mainAgentPrompt:%s", prompt)
	return prompt
}

// getMainAgentPromptNormal 获取主AgentPrompt or normal
func (a *ChatAgentImpl) getMainAgentPromptNormal(ctx context.Context, bs *botsession.BotSession) string {
	prompt := bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMain)
	if a.agentContainer.GetEntryAgent() != bs.AgentStatus.CurrentAgent {
		prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentSub)
	}
	// SystemRole 映射到多 agent 的 instructions
	// systemRole, _ := a.agentContainer.GetAgentInstructions(bs.AgentStatus.CurrentAgent)
	// if bs.App.GetKnowledgeQa().GetWorkflow().GetUsePdl() || config.IsUsePdl(bs.App.GetAppBizId()) { // 启用PDL
	//	if systemRole != "" { // 带task goal
	//		prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMainWorkflowTaskGoal)
	//	} else {
	//		prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMainWorkflow)
	//	}
	// } else {
	//	if systemRole != "" { // 带task goal
	//		prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMainTaskGoal)
	//	} else {
	//		prompt = bs.App.GetAgentPrompt(ctx, model.ModelTypeAgentMain)
	//	}
	// }
	currAgent, _ := a.agentContainer.GetSingleAgentByName(bs.AgentStatus.CurrentAgent)
	if strings.HasPrefix(currAgent.GetModel().GetModelName(), DefaultThinkingModel) {
		prompt = strings.ReplaceAll(prompt, "{{ identity_tag }}", " named 神农大语言模型, developed by 腾讯, ")
	} else {
		prompt = strings.ReplaceAll(prompt, "{{ identity_tag }}", " ")
	}
	if strings.HasPrefix(prompt, "You are {{ agent_name }}.") {
		prompt = strings.ReplaceAll(prompt, "{{ agent_name }}", bs.AgentStatus.CurrentAgent)
	}

	log.InfoContextf(ctx, "mainAgentPrompt:%s", prompt)
	return prompt
}

// getWorkflowPrompt 获取工作流Prompt
func (a *ChatAgentImpl) getWorkflowPrompt(ctx context.Context, bs *botsession.BotSession) string {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeAgentWorkflow)
	log.InfoContextf(ctx, "workflowPrompt:%s", m.GetPrompt())
	return m.GetPrompt()
}

// getReachThinkingLimitPrompt 获取达到思考上限时候的Prompt
func (a *ChatAgentImpl) getReachThinkingLimitPrompt(ctx context.Context, bs *botsession.BotSession) string {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeAgentMainReachLimit)
	log.InfoContextf(ctx, "ReachThinkingLimitPrompt:%s", m.GetPrompt())
	return m.GetPrompt()
}

// useWorkflowPrompt 判断当前query是否使用工作流模板
func (a *ChatAgentImpl) useWorkflowPrompt(ctx context.Context, bs *botsession.BotSession) bool {
	defer func() {
		if len(bs.WorkflowID) > 0 {
			bs.AgentStatus.AgentType = model.AgentTypeWorkflow
		}
	}()

	if !bs.AgentStatus.UsePDL && !config.IsUsePdl(bs.App.GetAppBizId()) {
		bs.WorkflowID = "" // 不使用PDL，清空workflowID
		// log.DebugContextf(ctx, "useWorkflowPrompt 1: %v", false)
		return false
	}
	if len(bs.WorkflowID) > 0 {
		// log.DebugContextf(ctx, "useWorkflowPrompt 2: %v", true)
		return true
	}
	log.DebugContextf(ctx, "AgentStatus: %s", helper.Object2String(bs.AgentStatus))
	if bs.AgentStatus != nil && bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
		// log.DebugContextf(ctx, "useWorkflowPrompt 3: %v", true)
		bs.WorkflowID = bs.AgentStatus.WorkflowID // 保存当前的workflowID
		return true
	}
	// log.DebugContextf(ctx, "useWorkflowPrompt 4: %v", false)
	return false
}

// getAllTools 获取所有工具 for LLM
func (a *ChatAgentImpl) getAllTools(ctx context.Context, bs *botsession.BotSession) {

	// 非pdl模式，当前插件列表直接从 agent 配置中拿取
	if len(bs.WorkflowID) == 0 {
		currAgent, ok := a.agentContainer.GetSingleAgentByName(bs.AgentStatus.CurrentAgent)

		if len(bs.Plugins) > 0 {
			toolsID := make([]string, 0)
			pluginID := make([]string, 0)
			currAgent.Tools = make([]*agent_config_server.AgentToolInfo, 0) // 清空
			for _, item := range bs.Plugins {
				toolsID = append(toolsID, item.ToolID)
				pluginID = append(pluginID, item.PluginID)
			}
			plugins := a.dao.ListPlugins(ctx, &plugin.ListPluginsReq{PluginIds: pluginID,
				PageSize: 30, PageNumber: 1, QueryType: plugin.ListPluginsReq_ID})

			for _, pluginTmp := range plugins {
				for _, tool := range pluginTmp.GetTools() {
					if helper.Contains(toolsID, tool.ToolId) {
						currAgent.Tools = append(currAgent.Tools, &agent_config_server.AgentToolInfo{
							PluginId:   tool.PluginId,
							PluginName: tool.PluginId,
							IconUrl:    tool.Url,
							PluginType: 0,
							ToolId:     tool.ToolId,
							ToolName:   tool.Name,
							ToolDesc:   tool.Desc,
							Inputs:     convertRequestParam(tool.Inputs),
							Outputs:    convertResponseParam(tool.Outputs),
						})
					}
				}
			}
		}

		if ok {
			for _, p := range currAgent.Tools {
				bs.ToolsInfo[p.ToolName] = model.AgentTool{
					AgentToolInfo: p,
				}
			}
		}
	}
	// 不管是不是工作流模式，都尝试拉取工作流插件列表
	agentWorkflowInfos := a.getPluginsAndWorkflows(ctx, bs)
	// 遍历PDL
	for _, item := range agentWorkflowInfos {
		cfg := model.GetConfig(item.GetPDLContent())
		bs.ToolsInfo["Agent-"+cfg.Name] = model.AgentTool{
			AgentToolInfo: &agent_config_server.AgentToolInfo{
				ToolId:     item.WorkflowId,
				ToolName:   "Agent-" + cfg.Name,
				ToolDesc:   cfg.Desc,
				PluginType: plugin.PluginTypeEnum(event.PluginTypeWorkflow),
			},
			IsWorkflow:                true,
			WorkflowID:                item.WorkflowId,
			WorkflowName:              cfg.Name,
			PDLContent:                item.GetPDLContent(),
			APIInfo:                   item.GetApiInfo(),
			Parameters:                item.GetParameters(),
			UserAdditionalConstraints: item.GetUserAdditionalConstraints(),
		}
	}
}

// getWorkflowTools 获取工作流中的工具 for LLM。
// 这里有可能是从MainAgent切换到WorkflowAgent，这个时候ToolsInfo有值，也有可能是第二次query进来，这个时候ToolsInfo为空
func (a *ChatAgentImpl) getWorkflowTools(ctx context.Context, bs *botsession.BotSession) []*openapi.Tool {
	openAPITools := model.GetPDLDefaultTools(bs.WorkflowDebug)
	if len(bs.ToolsInfo[bs.AgentStatus.WorkflowName].PDLContent) == 0 {
		agentWorkflowInfos := a.dao.GetAgentWorkflowInfo(ctx, &KEP_WF.GetAgentWorkflowInfoReq{
			AppBizId:   strconv.FormatUint(bs.App.GetAppBizId(), 10),
			WorkflowId: bs.WorkflowID,
			EnvTag:     uint32(bs.Env),
		})
		// 遍历PDL
		for _, item := range agentWorkflowInfos {
			cfg := model.GetConfig(item.GetPDLContent())
			bs.AgentStatus.WorkflowID = item.WorkflowId
			bs.AgentStatus.WorkflowName = cfg.Name
			bs.ToolsInfo[cfg.Name] = model.AgentTool{
				AgentToolInfo: &agent_config_server.AgentToolInfo{
					ToolId:     item.WorkflowId,
					ToolName:   cfg.Name,
					ToolDesc:   cfg.Desc,
					PluginType: plugin.PluginTypeEnum(event.PluginTypeWorkflow),
				},
				IsWorkflow:                true,
				WorkflowID:                item.WorkflowId,
				WorkflowName:              cfg.Name,
				PDLContent:                item.GetPDLContent(),
				APIInfo:                   item.GetApiInfo(),
				Parameters:                item.GetParameters(),
				UserAdditionalConstraints: item.GetUserAdditionalConstraints(),
			}
		}
	}
	apiInfos := model.ConvertAPIInfo(bs.ToolsInfo[bs.AgentStatus.WorkflowName].APIInfo)
	tmp := model.ConvertToOpenAPI(apiInfos)
	openAPITools = append(openAPITools, tmp...)
	log.InfoContextf(ctx, "getWorkflowTools: %s", helper.Object2String(openAPITools))
	return openAPITools
}

// getPluginsAndWorkflows 获取插件和工作流
func (a *ChatAgentImpl) getPluginsAndWorkflows(ctx context.Context,
	bs *botsession.BotSession) []*KEP_WF.GetAgentWorkflowInfoRsp_AgentWorkflowInfo {
	agentWorkflowInfos := make([]*KEP_WF.GetAgentWorkflowInfoRsp_AgentWorkflowInfo, 0)
	if config.IsUsePdl(bs.App.GetAppBizId()) || bs.App.GetKnowledgeQa().GetWorkflow().GetUsePdl() {
		agentWorkflowInfos = a.dao.GetAgentWorkflowInfo(ctx, &KEP_WF.GetAgentWorkflowInfoReq{
			AppBizId: strconv.FormatUint(bs.App.GetAppBizId(), 10),
			// AppBizId:   "1856923315073712128",
			WorkflowId: bs.WorkflowID,
			EnvTag:     uint32(bs.Env),
		})
	}
	return agentWorkflowInfos
}

// getProperties 获取参数
func (a *ChatAgentImpl) getProperties(
	ctx context.Context, bs *botsession.BotSession,
	tool *agent_config_server.AgentToolInfo) (
	map[string]*openapi.Definition, error) {
	properties := make(map[string]*openapi.Definition)
	for _, param := range tool.GetInputs() {
		if param.GetAgentHidden() { // 模型不可见
			continue
		}
		if !param.GetAgentHidden() { // 模型可见
			p, err := a.getSubParams(ctx, bs, param.SubParams)
			if err != nil {
				return nil, err
			}
			properties[param.Name] = &openapi.Definition{
				Type:        model.TypeEnum2DataType[param.Type],
				Description: param.Desc,
				Properties:  p,
				Required:    a.getSubRequired(param.SubParams),
			}
			defaultValue, err := a.getCompatibleDefaultValue(ctx, bs, param)
			if err != nil {
				log.ErrorContextf(ctx, "getCompatibleDefaultValue error: %v", err)
				return properties, err
			}
			if defaultValue != "" { // 且有默认值
				properties[param.Name].Default = defaultValue
			}
		}
	}
	return properties, nil
}

// getSubParams TODO
// getProperties 获取参数
func (a *ChatAgentImpl) getSubParams(
	ctx context.Context, bs *botsession.BotSession,
	subParams []*agent_config_server.AgentToolReqParam) (
	map[string]*openapi.Definition, error) {
	properties := make(map[string]*openapi.Definition)
	for _, param := range subParams {
		if param.GetAgentHidden() { // 模型不可见
			continue
		}
		if !param.GetAgentHidden() { // 模型可见
			p, err := a.getSubParams(ctx, bs, param.SubParams)
			if err != nil {
				return nil, err
			}
			properties[param.Name] = &openapi.Definition{
				Type:        model.TypeEnum2DataType[param.Type],
				Description: param.Desc,
				Properties:  p,
				Required:    a.getSubRequired(param.SubParams),
			}
			defaultValue, err := a.getCompatibleDefaultValue(ctx, bs, param)
			if err != nil {
				log.ErrorContextf(ctx, "getCompatibleDefaultValue error: %v", err)
				return properties, err
			}
			if defaultValue != "" { // 且有默认值
				properties[param.Name].Default = defaultValue
			}
		}
	}
	return properties, nil
}

// getRequired 获取必填参数
func (a *ChatAgentImpl) getRequired(tool *agent_config_server.AgentToolInfo) []string {
	required := make([]string, 0)
	for _, param := range tool.GetInputs() {
		// 参数对模型可见，并且是必填，放到 required 里面
		if !param.GetAgentHidden() && param.GetIsRequired() {
			required = append(required, param.Name)
		}
	}
	return required
}

// getSubRequired 获取必填参数
func (a *ChatAgentImpl) getSubRequired(
	subParams []*agent_config_server.AgentToolReqParam) []string {
	required := make([]string, 0)
	for _, param := range subParams {
		if param.GetIsRequired() {
			required = append(required, param.Name)
		}
	}
	return required
}

// processAgentCheck 处理智能体的安全审核
func (a *ChatAgentImpl) processAgentCheck(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response, throttleCheck helper.Throttle) (isEvil bool) {
	l := len([]rune(rsp.GetMessage().GetContent()))
	if bs.NeedCheck && throttleCheck.Hit(l, rsp.Finished) { // 安全审核一定要过
		checkCode, checkType := a.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(),
			bs.App.GetCorpId(), bs.RecordID, rsp.GetMessage().GetContent(), bs.App.GetInfosecBizType())
		bs.Msg.ResultCode = checkCode
		bs.Msg.ResultType = checkType
		isEvil = checkCode == ispkg.ResultEvil
		if isEvil {
			ctx, cancel := context.WithCancel(ctx)
			rsp.Finished = true
			re := bs.NewReplyEvent(ctx, rsp, isEvil, model.ReplyMethodAgent, bs.StartTime, nil)
			_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)

			// 写对话记录，结束流程
			lastEvil := &infosec.CheckRsp{ResultCode: bs.Msg.ResultCode, ResultType: bs.Msg.ResultType}
			record := bs.NewBotRecord(ctx, rsp.GetMessage().GetContent(),
				nil, model.ReplyMethodAgent, lastEvil, bs.StartTime)
			log.DebugContextf(ctx, "Token Stat: %v", helper.Object2String(bs.TokenStat))
			newRecord, newStat := event.GetMsgRecordAndTokenStat(ctx, record)
			_, _ = a.dao.CreateMsgRecord(ctx, newRecord, newStat) // for answer

		}
	}
	return isEvil
}

// processAgentReplyCheck 处理智能体的安全审核
func (a *ChatAgentImpl) processAgentReplyCheck(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response, throttleCheck helper.Throttle) (isEvil bool) {
	reply := bs.GetAgentReply(ctx, rsp)
	l := len([]rune(reply))
	if bs.NeedCheck && throttleCheck.Hit(l, rsp.Finished) { // 安全审核一定要过
		checkCode, checkType := a.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(),
			bs.App.GetCorpId(), bs.RecordID, reply, bs.App.GetInfosecBizType())
		bs.Msg.ResultCode = checkCode
		bs.Msg.ResultType = checkType
		isEvil = checkCode == ispkg.ResultEvil
		if isEvil {
			ctx, cancel := context.WithCancel(ctx)
			rsp.Finished = true
			re := bs.NewReplyEvent(ctx, rsp, isEvil, model.ReplyMethodAgent, bs.StartTime, nil)
			_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)

			// 写对话记录，结束流程
			lastEvil := &infosec.CheckRsp{ResultCode: bs.Msg.ResultCode, ResultType: bs.Msg.ResultType}
			record := bs.NewBotRecord(ctx, reply,
				nil, model.ReplyMethodAgent, lastEvil, bs.StartTime)
			log.DebugContextf(ctx, "Token Stat: %v", helper.Object2String(bs.TokenStat))
			newRecord, newStat := event.GetMsgRecordAndTokenStat(ctx, record)
			_, _ = a.dao.CreateMsgRecord(ctx, newRecord, newStat) // for answer

		}
	}
	return isEvil
}

// hitHandoff fucntional call 是否是转交
func (a *ChatAgentImpl) hitHandoff(ctx context.Context,
	bs *botsession.BotSession, rsp *llmm.Response) bool {
	if !rsp.Finished {
		return false
	}
	// 已经判断出来存在转交了
	if bs.AgentStatus.AgentSwitch {
		return true
	}
	// 先获取当前的 agent
	currentAgent, ok := a.agentContainer.GetSingleAgentByName(bs.AgentStatus.CurrentAgent)
	if !ok {
		return false
	}
	for _, handoff := range currentAgent.Handoffs {
		// 获取当前 agent 的每一个 handoff agent
		handoffAgent, ok := a.agentContainer.GetSingleAgentByID(handoff)
		if !ok {
			log.WarnContextf(ctx, "GetSingleAgentByID not found, agentId: %s", handoff)
			continue
		}
		for _, toolCall := range rsp.GetMessage().ToolCalls {
			if toolCall == nil {
				continue
			}
			if getHandoffToolName(handoffAgent.Name) == toolCall.Function.Name {
				// 确实命中了 handoff agent
				bs.AgentStatus.Handoff(handoffAgent.Name)
				if handoffAgent.Name == a.agentContainer.EntryAgent {
					bs.AgentStatus.AgentType = model.AgentTypeMain // 转交到 main
				} else {
					bs.AgentStatus.AgentType = model.AgentTypeSubAgent // 转交到子 agent
				}
				log.InfoContextf(ctx, "agent handoff to %s", toolCall.Function.Name)
				return true
			}
		}
	}
	return false
}

// convertRequestParam 转换函数
func convertRequestParam(
	params []*plugin.RequestParam) []*agent_config_server.AgentToolReqParam {
	var result []*agent_config_server.AgentToolReqParam
	for _, param := range params {
		converted := &agent_config_server.AgentToolReqParam{
			Name:         param.GetName(),
			Desc:         param.GetDesc(),
			Type:         param.GetType(),
			IsRequired:   param.GetIsRequired(),
			DefaultValue: param.GetDefaultValue(),
			AgentHidden:  false,
			Input:        nil, // 直接从插件中心获取的插件 input 为空
		}
		if param.GetSubParams() != nil {
			converted.SubParams = convertRequestParam(param.GetSubParams())
		}
		result = append(result, converted)
	}
	return result
}

func convertResponseParam(
	params []*plugin.ResponseParam) []*agent_config_server.AgentToolRspParam {
	var result []*agent_config_server.AgentToolRspParam
	for _, param := range params {
		converted := &agent_config_server.AgentToolRspParam{
			Name:        param.GetName(),
			Desc:        param.GetDesc(),
			Type:        param.GetType(),
			AgentHidden: false,
		}
		if param.GetSubParams() != nil {
			converted.SubParams = convertResponseParam(param.GetSubParams())
		}
		result = append(result, converted)
	}
	return result
}

// findTool 根据工具名称查找工具，支持蛇形命名转换为小驼峰和大驼峰。
func findTool(ctx context.Context, toolMap map[string]model.AgentTool, toolName string) (model.AgentTool, bool) {
	// 直接查找原始工具名
	if tool, ok := toolMap[toolName]; ok {
		return tool, true
	}

	// 尝试将蛇形命名转换为小驼峰命名
	camelName := helper.SnakeToCamelCase(toolName)
	if tool, ok := toolMap[camelName]; ok {
		log.InfoContextf(ctx, "find tool in map origin name: %s, camelName: %s", toolName, camelName)
		return tool, true
	}

	// 尝试将蛇形命名转换为大驼峰命名
	pascalName := helper.SnakeToPascalCase(toolName)
	if tool, ok := toolMap[pascalName]; ok {
		log.InfoContextf(ctx, "find tool in map origin name: %s, pascalName: %s", toolName, pascalName)
		return tool, true
	}

	// 如果上述形式都没找到，则返回错误信息
	return model.AgentTool{}, false
}

// isCodeInterpreter 判断是否是代码解释器
func isCodeInterpreter(toolName string) bool {
	if toolName == ToolNameCode || toolName == "code_interpreter" || toolName == "codeInterpreter" ||
		toolName == ToolNameCharts || toolName == "generate_charts" || toolName == "generateCharts" {
		return true
	}
	return false
}

// isSearchTool 判断是否是DeepSeek搜索、混元搜索
func isSearchTool(toolName string) bool {
	if isDeepSeekSearchTool(toolName) || toolName == ToolNameHunYuanSearch {
		return true
	}
	return false
}

// isDeepSeekSearchTool 判断是否是DeepSeek搜索
func isDeepSeekSearchTool(toolName string) bool {
	if toolName == ToolNameDeepSeekV3 || toolName == ToolNameDeepSeekR1 {
		return true
	}
	return false
}

// extractMarkdownURLs 从 markdown 形式的链接或图片标签中提取所有 URL
func extractMarkdownURLs(s string) []string {
	// !? 忽略是否带惊叹号，\[...\]\(...\) 提取括号内的 URL
	re := regexp.MustCompile(`!?\[[^\]]*\]\(([^)]+)\)`)
	matches := re.FindAllStringSubmatch(s, -1)

	urls := make([]string, 0, len(matches))
	for _, m := range matches {
		if len(m) >= 2 {
			urls = append(urls, m[1])
		}
	}
	return urls
}

// filesContainsURL 检查 a.Files 中是否已有相同的 URL
func (a *ChatAgentImpl) filesContainsURL(url string) bool {
	for _, f := range a.Files {
		if f.FileURL == url {
			return true
		}
	}
	return false
}

// ExtractFileFromOutput 提取 output 中的所有 markdown 链接，按 URL 去重并追加到 a.Files
// 返回第一个匹配到的 URL（若无则返回空串）
func (a *ChatAgentImpl) ExtractFileFromOutput(output string) (has bool) {
	urls := extractMarkdownURLs(output)
	if len(urls) == 0 {
		return
	}

	for _, url := range urls {
		if a.filesContainsURL(url) {
			continue
		}
		fileName, fileType := helper.GetFileNameAndType(url)
		a.Files = append(a.Files, &model.FileInfo{
			FileName:  fileName,
			FileURL:   url,
			FileType:  fileType,
			CreatedAt: time.Now().Unix(),
		})
		has = true
	}
	return has
}

var re = regexp.MustCompile(`\[\^(\d+)\]`)

// ExtractQuotes 接收原始文本，返回移除所有 [^n] 后的文本和对应的 QuoteInfo 列表。
// Position 按 rune 计数，且是针对移除标记之后的新字符串位置。
func ExtractQuotes(s string) (cleaned string, infos []model.QuoteInfo) {
	// 全部匹配的字节索引信息
	matches := re.FindAllStringSubmatchIndex(s, -1)

	// 已经删除的 rune 数（累加所有前面标记的长度）
	removedRunes := 0

	for _, m := range matches {
		byteStart, byteEnd := m[0], m[1]
		numStart, numEnd := m[2], m[3]

		// 解析方括号内的数字
		idx, err := strconv.Atoi(s[numStart:numEnd])
		if err != nil {
			continue
		}

		// 原始字符串中，该标记起始位置的 rune 索引
		origPos := len([]rune(s[:byteStart]))
		// 本次标记的 rune 长度
		markerRuneLen := len([]rune(s[byteStart:byteEnd]))
		// 在清理后字符串中的位置 = 原始位置 - 已删除长度
		cleanedPos := origPos - removedRunes

		infos = append(infos, model.QuoteInfo{
			Position: cleanedPos,
			Index:    idx,
		})
		removedRunes += markerRuneLen
	}

	// 最后统一移除所有标记
	cleaned = re.ReplaceAllString(s, "")
	return
}
