// raven.pb-stub
//
// @(#)go.mod  June 11th, 2021
// Copyright(c) 2021, leyton@Tencent. All rights reserved.

module git.woa.com/dialogue-platform/lke_proto

go 1.20

require (
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4
	git.code.oa.com/trpc-go/trpc v0.1.2
	git.code.oa.com/trpc-go/trpc-go v0.16.0
	google.golang.org/protobuf v1.31.1-0.20231027082548-f4a6c1f6e5c1
)

require (
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	git.woa.com/trpc-go/tnet v0.0.15 // indirect
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/andy<PERSON>holm/brotli v1.0.4 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-playground/form/v4 v4.2.0 // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/lestrrat-go/strftime v1.0.6 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/panjf2000/ants/v2 v2.4.6 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.43.0 // indirect
	go.uber.org/automaxprocs v1.3.0 // indirect
	go.uber.org/zap v1.24.0 // indirect
	golang.org/x/net v0.19.0 // indirect
	golang.org/x/sys v0.15.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

require (
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.2
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/flatbuffers v2.0.8+incompatible // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/klauspost/compress v1.17.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/rogpeppe/go-internal v1.11.0 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/stretchr/testify v1.8.4 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/sync v0.5.0 // indirect
	gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c // indirect
)

replace (
	go.uber.org/atomic => go.uber.org/atomic v1.9.0
	go.uber.org/automaxprocs => go.uber.org/automaxprocs v1.3.0
	go.uber.org/multierr => go.uber.org/multierr v1.8.0
)
