// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.13.0
// source: bot-admin-common.proto

package bot_admin_config_server

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 插件相关的定义复制一份
type TypeEnum int32

const (
	TypeEnum_STRING       TypeEnum = 0 // 默认值是string，如果不填就按string处理
	TypeEnum_INT          TypeEnum = 1
	TypeEnum_FLOAT        TypeEnum = 2
	TypeEnum_BOOL         TypeEnum = 3
	TypeEnum_OBJECT       TypeEnum = 4
	TypeEnum_ARRAY_STRING TypeEnum = 5
	TypeEnum_ARRAY_INT    TypeEnum = 6
	TypeEnum_ARRAY_FLOAT  TypeEnum = 7
	TypeEnum_ARRAY_BOOL   TypeEnum = 8
	TypeEnum_ARRAY_OBJECT TypeEnum = 9
)

// Enum value maps for TypeEnum.
var (
	TypeEnum_name = map[int32]string{
		0: "STRING",
		1: "INT",
		2: "FLOAT",
		3: "BOOL",
		4: "OBJECT",
		5: "ARRAY_STRING",
		6: "ARRAY_INT",
		7: "ARRAY_FLOAT",
		8: "ARRAY_BOOL",
		9: "ARRAY_OBJECT",
	}
	TypeEnum_value = map[string]int32{
		"STRING":       0,
		"INT":          1,
		"FLOAT":        2,
		"BOOL":         3,
		"OBJECT":       4,
		"ARRAY_STRING": 5,
		"ARRAY_INT":    6,
		"ARRAY_FLOAT":  7,
		"ARRAY_BOOL":   8,
		"ARRAY_OBJECT": 9,
	}
)

func (x TypeEnum) Enum() *TypeEnum {
	p := new(TypeEnum)
	*p = x
	return p
}

func (x TypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_bot_admin_common_proto_enumTypes[0].Descriptor()
}

func (TypeEnum) Type() protoreflect.EnumType {
	return &file_bot_admin_common_proto_enumTypes[0]
}

func (x TypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TypeEnum.Descriptor instead.
func (TypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_bot_admin_common_proto_rawDescGZIP(), []int{0}
}

// CheckResultReq 审核结果请求 (仅对内使用)
type CheckResultReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ResultCode uint32 `protobuf:"varint,2,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
	ResultType uint32 `protobuf:"varint,3,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
}

func (x *CheckResultReq) Reset() {
	*x = CheckResultReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResultReq) ProtoMessage() {}

func (x *CheckResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResultReq.ProtoReflect.Descriptor instead.
func (*CheckResultReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_common_proto_rawDescGZIP(), []int{0}
}

func (x *CheckResultReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CheckResultReq) GetResultCode() uint32 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

func (x *CheckResultReq) GetResultType() uint32 {
	if x != nil {
		return x.ResultType
	}
	return 0
}

type CheckResultRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckResultRsp) Reset() {
	*x = CheckResultRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResultRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResultRsp) ProtoMessage() {}

func (x *CheckResultRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResultRsp.ProtoReflect.Descriptor instead.
func (*CheckResultRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_common_proto_rawDescGZIP(), []int{1}
}

// 定义工具的请求信息
type RequestParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string          `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                                 // 参数名称
	Desc         string          `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                 // 参数描述
	Type         TypeEnum        `protobuf:"varint,3,opt,name=Type,proto3,enum=trpc.KEP.bot_admin_config_server.TypeEnum" json:"Type,omitempty"` // 参数类型
	IsRequired   bool            `protobuf:"varint,4,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"`                                    // 是否必选
	DefaultValue string          `protobuf:"bytes,5,opt,name=DefaultValue,proto3" json:"DefaultValue,omitempty"`                                 // 默认值
	SubParams    []*RequestParam `protobuf:"bytes,6,rep,name=SubParams,proto3" json:"SubParams,omitempty"`                                       // 子参数,ParamType 是OBJECT 或 ARRAY<>类型有用
	GlobalHidden bool            `protobuf:"varint,7,opt,name=GlobalHidden,proto3" json:"GlobalHidden,omitempty"`                                //插件参数配置是否隐藏不可见，true-隐藏不可见，false-可见
}

func (x *RequestParam) Reset() {
	*x = RequestParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestParam) ProtoMessage() {}

func (x *RequestParam) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestParam.ProtoReflect.Descriptor instead.
func (*RequestParam) Descriptor() ([]byte, []int) {
	return file_bot_admin_common_proto_rawDescGZIP(), []int{2}
}

func (x *RequestParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RequestParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RequestParam) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *RequestParam) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *RequestParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *RequestParam) GetSubParams() []*RequestParam {
	if x != nil {
		return x.SubParams
	}
	return nil
}

func (x *RequestParam) GetGlobalHidden() bool {
	if x != nil {
		return x.GlobalHidden
	}
	return false
}

var File_bot_admin_common_proto protoreflect.FileDescriptor

var file_bot_admin_common_proto_rawDesc = []byte{
	0x0a, 0x16, 0x62, 0x6f, 0x74, 0x2d, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x62, 0x0a, 0x0e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x10,
	0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x73, 0x70,
	0x22, 0xac, 0x02, 0x0a, 0x0c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x3e, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4c, 0x0a,
	0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x52, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x47,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x2a,
	0x94, 0x01, 0x0a, 0x08, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06,
	0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4e, 0x54, 0x10,
	0x01, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04,
	0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54,
	0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x52, 0x49,
	0x4e, 0x47, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x49, 0x4e,
	0x54, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x46, 0x4c, 0x4f,
	0x41, 0x54, 0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x42, 0x4f,
	0x4f, 0x4c, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x4f, 0x42,
	0x4a, 0x45, 0x43, 0x54, 0x10, 0x09, 0x42, 0x4d, 0x5a, 0x4b, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x62, 0x6f,
	0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bot_admin_common_proto_rawDescOnce sync.Once
	file_bot_admin_common_proto_rawDescData = file_bot_admin_common_proto_rawDesc
)

func file_bot_admin_common_proto_rawDescGZIP() []byte {
	file_bot_admin_common_proto_rawDescOnce.Do(func() {
		file_bot_admin_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_bot_admin_common_proto_rawDescData)
	})
	return file_bot_admin_common_proto_rawDescData
}

var file_bot_admin_common_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_bot_admin_common_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_bot_admin_common_proto_goTypes = []interface{}{
	(TypeEnum)(0),          // 0: trpc.KEP.bot_admin_config_server.TypeEnum
	(*CheckResultReq)(nil), // 1: trpc.KEP.bot_admin_config_server.CheckResultReq
	(*CheckResultRsp)(nil), // 2: trpc.KEP.bot_admin_config_server.CheckResultRsp
	(*RequestParam)(nil),   // 3: trpc.KEP.bot_admin_config_server.RequestParam
}
var file_bot_admin_common_proto_depIdxs = []int32{
	0, // 0: trpc.KEP.bot_admin_config_server.RequestParam.Type:type_name -> trpc.KEP.bot_admin_config_server.TypeEnum
	3, // 1: trpc.KEP.bot_admin_config_server.RequestParam.SubParams:type_name -> trpc.KEP.bot_admin_config_server.RequestParam
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_bot_admin_common_proto_init() }
func file_bot_admin_common_proto_init() {
	if File_bot_admin_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bot_admin_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckResultReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckResultRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bot_admin_common_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_bot_admin_common_proto_goTypes,
		DependencyIndexes: file_bot_admin_common_proto_depIdxs,
		EnumInfos:         file_bot_admin_common_proto_enumTypes,
		MessageInfos:      file_bot_admin_common_proto_msgTypes,
	}.Build()
	File_bot_admin_common_proto = out.File
	file_bot_admin_common_proto_rawDesc = nil
	file_bot_admin_common_proto_goTypes = nil
	file_bot_admin_common_proto_depIdxs = nil
}
