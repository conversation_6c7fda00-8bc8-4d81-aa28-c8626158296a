// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.13.0
// source: bot-admin-file-manager-callback.proto

package bot_admin_config_server

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MiniChunkType int32

const (
	MiniChunkType_MiniChunkTypeText  MiniChunkType = 0 // MiniChunkTypeText 文本类型
	MiniChunkType_MiniChunkTypeTable MiniChunkType = 1 // MiniChunkTypeTable 表格类型
	MiniChunkType_MiniChunkTypeImage MiniChunkType = 2 // MiniChunkTypeImage 图片类型
)

// Enum value maps for MiniChunkType.
var (
	MiniChunkType_name = map[int32]string{
		0: "MiniChunkTypeText",
		1: "MiniChunkTypeTable",
		2: "MiniChunkTypeImage",
	}
	MiniChunkType_value = map[string]int32{
		"MiniChunkTypeText":  0,
		"MiniChunkTypeTable": 1,
		"MiniChunkTypeImage": 2,
	}
)

func (x MiniChunkType) Enum() *MiniChunkType {
	p := new(MiniChunkType)
	*p = x
	return p
}

func (x MiniChunkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MiniChunkType) Descriptor() protoreflect.EnumDescriptor {
	return file_bot_admin_file_manager_callback_proto_enumTypes[0].Descriptor()
}

func (MiniChunkType) Type() protoreflect.EnumType {
	return &file_bot_admin_file_manager_callback_proto_enumTypes[0]
}

func (x MiniChunkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MiniChunkType.Descriptor instead.
func (MiniChunkType) EnumDescriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{0}
}

type FileParserCallbackReq_OpType int32

const (
	FileParserCallbackReq_OPTYPE_UNKNOWN FileParserCallbackReq_OpType = 0
	FileParserCallbackReq_WORDCOUNT      FileParserCallbackReq_OpType = 1 // 统计字数
	FileParserCallbackReq_PARSE          FileParserCallbackReq_OpType = 2
	FileParserCallbackReq_SPLIT          FileParserCallbackReq_OpType = 3
)

// Enum value maps for FileParserCallbackReq_OpType.
var (
	FileParserCallbackReq_OpType_name = map[int32]string{
		0: "OPTYPE_UNKNOWN",
		1: "WORDCOUNT",
		2: "PARSE",
		3: "SPLIT",
	}
	FileParserCallbackReq_OpType_value = map[string]int32{
		"OPTYPE_UNKNOWN": 0,
		"WORDCOUNT":      1,
		"PARSE":          2,
		"SPLIT":          3,
	}
)

func (x FileParserCallbackReq_OpType) Enum() *FileParserCallbackReq_OpType {
	p := new(FileParserCallbackReq_OpType)
	*p = x
	return p
}

func (x FileParserCallbackReq_OpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileParserCallbackReq_OpType) Descriptor() protoreflect.EnumDescriptor {
	return file_bot_admin_file_manager_callback_proto_enumTypes[1].Descriptor()
}

func (FileParserCallbackReq_OpType) Type() protoreflect.EnumType {
	return &file_bot_admin_file_manager_callback_proto_enumTypes[1]
}

func (x FileParserCallbackReq_OpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileParserCallbackReq_OpType.Descriptor instead.
func (FileParserCallbackReq_OpType) EnumDescriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{0, 0}
}

type FileParserCallbackReq_TaskStatus int32

const (
	FileParserCallbackReq_STATUS_UNKNOWN FileParserCallbackReq_TaskStatus = 0
	FileParserCallbackReq_PENDING        FileParserCallbackReq_TaskStatus = 1
	FileParserCallbackReq_RUNNING        FileParserCallbackReq_TaskStatus = 2
	FileParserCallbackReq_FINISH         FileParserCallbackReq_TaskStatus = 3
	FileParserCallbackReq_FAILED         FileParserCallbackReq_TaskStatus = 4
	FileParserCallbackReq_CANCELED       FileParserCallbackReq_TaskStatus = 5
)

// Enum value maps for FileParserCallbackReq_TaskStatus.
var (
	FileParserCallbackReq_TaskStatus_name = map[int32]string{
		0: "STATUS_UNKNOWN",
		1: "PENDING",
		2: "RUNNING",
		3: "FINISH",
		4: "FAILED",
		5: "CANCELED",
	}
	FileParserCallbackReq_TaskStatus_value = map[string]int32{
		"STATUS_UNKNOWN": 0,
		"PENDING":        1,
		"RUNNING":        2,
		"FINISH":         3,
		"FAILED":         4,
		"CANCELED":       5,
	}
)

func (x FileParserCallbackReq_TaskStatus) Enum() *FileParserCallbackReq_TaskStatus {
	p := new(FileParserCallbackReq_TaskStatus)
	*p = x
	return p
}

func (x FileParserCallbackReq_TaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileParserCallbackReq_TaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_bot_admin_file_manager_callback_proto_enumTypes[2].Descriptor()
}

func (FileParserCallbackReq_TaskStatus) Type() protoreflect.EnumType {
	return &file_bot_admin_file_manager_callback_proto_enumTypes[2]
}

func (x FileParserCallbackReq_TaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileParserCallbackReq_TaskStatus.Descriptor instead.
func (FileParserCallbackReq_TaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{0, 1}
}

type FileParserCallbackReq_FileType int32

const (
	FileParserCallbackReq_FILETYPE_UNKNOWN FileParserCallbackReq_FileType = 0
	FileParserCallbackReq_MARKDOWN         FileParserCallbackReq_FileType = 1
	FileParserCallbackReq_PB               FileParserCallbackReq_FileType = 2 // current_op_type等于PARSE和SPLIT时返回
)

// Enum value maps for FileParserCallbackReq_FileType.
var (
	FileParserCallbackReq_FileType_name = map[int32]string{
		0: "FILETYPE_UNKNOWN",
		1: "MARKDOWN",
		2: "PB",
	}
	FileParserCallbackReq_FileType_value = map[string]int32{
		"FILETYPE_UNKNOWN": 0,
		"MARKDOWN":         1,
		"PB":               2,
	}
)

func (x FileParserCallbackReq_FileType) Enum() *FileParserCallbackReq_FileType {
	p := new(FileParserCallbackReq_FileType)
	*p = x
	return p
}

func (x FileParserCallbackReq_FileType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileParserCallbackReq_FileType) Descriptor() protoreflect.EnumDescriptor {
	return file_bot_admin_file_manager_callback_proto_enumTypes[3].Descriptor()
}

func (FileParserCallbackReq_FileType) Type() protoreflect.EnumType {
	return &file_bot_admin_file_manager_callback_proto_enumTypes[3]
}

func (x FileParserCallbackReq_FileType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileParserCallbackReq_FileType.Descriptor instead.
func (FileParserCallbackReq_FileType) EnumDescriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{0, 2}
}

type FileParserCallbackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId        string                           `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                                                            // 任务ID
	CurrentOpType FileParserCallbackReq_OpType     `protobuf:"varint,2,opt,name=current_op_type,json=currentOpType,proto3,enum=trpc.KEP.bot_admin_config_server.FileParserCallbackReq_OpType" json:"current_op_type,omitempty"` // 操作信息
	Status        FileParserCallbackReq_TaskStatus `protobuf:"varint,3,opt,name=status,proto3,enum=trpc.KEP.bot_admin_config_server.FileParserCallbackReq_TaskStatus" json:"status,omitempty"`                                  // 任务状态
	FType         FileParserCallbackReq_FileType   `protobuf:"varint,4,opt,name=f_type,json=fType,proto3,enum=trpc.KEP.bot_admin_config_server.FileParserCallbackReq_FileType" json:"f_type,omitempty"`                         // 结果文件内容的格式
	Progress      int32                            `protobuf:"varint,5,opt,name=progress,proto3" json:"progress,omitempty"`                                                                                                     // 进度，乘以100
	ResultCosUrl  string                           `protobuf:"bytes,6,opt,name=result_cos_url,json=resultCosUrl,proto3" json:"result_cos_url,omitempty"`                                                                        // 结果文件COS链接
	TextLength    int32                            `protobuf:"varint,7,opt,name=text_length,json=textLength,proto3" json:"text_length,omitempty"`                                                                               // 文档字数,用golang的rune来统计。
	Message       string                           `protobuf:"bytes,8,opt,name=message,proto3" json:"message,omitempty"`                                                                                                        // 提示信息
	RequestId     string                           `protobuf:"bytes,9,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`                                                                                   // 将请求的request_id带回去
	ResultMd5     string                           `protobuf:"bytes,10,opt,name=result_md5,json=resultMd5,proto3" json:"result_md5,omitempty"`                                                                                  // result_cos_url对应文件的MD5
	ErrorCode     string                           `protobuf:"bytes,11,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`                                                                                  // 返回错误码
	PageNum       int32                            `protobuf:"varint,12,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`                                                                                       //文档页码
	DebugInfo     *DebugInfo                       `protobuf:"bytes,13,opt,name=debug_info,json=debugInfo,proto3" json:"debug_info,omitempty"`                                                                                  // 调试信息
}

func (x *FileParserCallbackReq) Reset() {
	*x = FileParserCallbackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileParserCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileParserCallbackReq) ProtoMessage() {}

func (x *FileParserCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileParserCallbackReq.ProtoReflect.Descriptor instead.
func (*FileParserCallbackReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{0}
}

func (x *FileParserCallbackReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *FileParserCallbackReq) GetCurrentOpType() FileParserCallbackReq_OpType {
	if x != nil {
		return x.CurrentOpType
	}
	return FileParserCallbackReq_OPTYPE_UNKNOWN
}

func (x *FileParserCallbackReq) GetStatus() FileParserCallbackReq_TaskStatus {
	if x != nil {
		return x.Status
	}
	return FileParserCallbackReq_STATUS_UNKNOWN
}

func (x *FileParserCallbackReq) GetFType() FileParserCallbackReq_FileType {
	if x != nil {
		return x.FType
	}
	return FileParserCallbackReq_FILETYPE_UNKNOWN
}

func (x *FileParserCallbackReq) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *FileParserCallbackReq) GetResultCosUrl() string {
	if x != nil {
		return x.ResultCosUrl
	}
	return ""
}

func (x *FileParserCallbackReq) GetTextLength() int32 {
	if x != nil {
		return x.TextLength
	}
	return 0
}

func (x *FileParserCallbackReq) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *FileParserCallbackReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *FileParserCallbackReq) GetResultMd5() string {
	if x != nil {
		return x.ResultMd5
	}
	return ""
}

func (x *FileParserCallbackReq) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *FileParserCallbackReq) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *FileParserCallbackReq) GetDebugInfo() *DebugInfo {
	if x != nil {
		return x.DebugInfo
	}
	return nil
}

type DebugInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParseResultCosUrl string `protobuf:"bytes,1,opt,name=parse_result_cos_url,json=parseResultCosUrl,proto3" json:"parse_result_cos_url,omitempty"` // 文档解析结果文件COS链接，内容为ParseResult
}

func (x *DebugInfo) Reset() {
	*x = DebugInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugInfo) ProtoMessage() {}

func (x *DebugInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugInfo.ProtoReflect.Descriptor instead.
func (*DebugInfo) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{1}
}

func (x *DebugInfo) GetParseResultCosUrl() string {
	if x != nil {
		return x.ParseResultCosUrl
	}
	return ""
}

type FileParserCallbackRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatusCode int32  `protobuf:"varint,1,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"` // 0：回调成功，1：回调失败
	Message    string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                          // 回调失败信息
}

func (x *FileParserCallbackRes) Reset() {
	*x = FileParserCallbackRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileParserCallbackRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileParserCallbackRes) ProtoMessage() {}

func (x *FileParserCallbackRes) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileParserCallbackRes.ProtoReflect.Descriptor instead.
func (*FileParserCallbackRes) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{2}
}

func (x *FileParserCallbackRes) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *FileParserCallbackRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Table struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rows []*Row `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *Table) Reset() {
	*x = Table{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Table) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Table) ProtoMessage() {}

func (x *Table) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Table.ProtoReflect.Descriptor instead.
func (*Table) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{3}
}

func (x *Table) GetRows() []*Row {
	if x != nil {
		return x.Rows
	}
	return nil
}

type Row struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cells []*Cell `protobuf:"bytes,1,rep,name=cells,proto3" json:"cells,omitempty"`
}

func (x *Row) Reset() {
	*x = Row{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Row) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Row) ProtoMessage() {}

func (x *Row) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Row.ProtoReflect.Descriptor instead.
func (*Row) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{4}
}

func (x *Row) GetCells() []*Cell {
	if x != nil {
		return x.Cells
	}
	return nil
}

type Cell struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []int32 `protobuf:"varint,1,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *Cell) Reset() {
	*x = Cell{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cell) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cell) ProtoMessage() {}

func (x *Cell) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cell.ProtoReflect.Descriptor instead.
func (*Cell) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{5}
}

func (x *Cell) GetValue() []int32 {
	if x != nil {
		return x.Value
	}
	return nil
}

type MiniChunkExtInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrgDataRunes []int32 `protobuf:"varint,1,rep,packed,name=org_data_runes,json=orgDataRunes,proto3" json:"org_data_runes,omitempty"`
	OrgLength    string  `protobuf:"bytes,2,opt,name=org_length,json=orgLength,proto3" json:"org_length,omitempty"`
	RunesLength  string  `protobuf:"bytes,3,opt,name=runes_length,json=runesLength,proto3" json:"runes_length,omitempty"`
	HitPattern   string  `protobuf:"bytes,4,opt,name=hit_pattern,json=hitPattern,proto3" json:"hit_pattern,omitempty"`
	Title        string  `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *MiniChunkExtInfo) Reset() {
	*x = MiniChunkExtInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MiniChunkExtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiniChunkExtInfo) ProtoMessage() {}

func (x *MiniChunkExtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiniChunkExtInfo.ProtoReflect.Descriptor instead.
func (*MiniChunkExtInfo) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{6}
}

func (x *MiniChunkExtInfo) GetOrgDataRunes() []int32 {
	if x != nil {
		return x.OrgDataRunes
	}
	return nil
}

func (x *MiniChunkExtInfo) GetOrgLength() string {
	if x != nil {
		return x.OrgLength
	}
	return ""
}

func (x *MiniChunkExtInfo) GetRunesLength() string {
	if x != nil {
		return x.RunesLength
	}
	return ""
}

func (x *MiniChunkExtInfo) GetHitPattern() string {
	if x != nil {
		return x.HitPattern
	}
	return ""
}

func (x *MiniChunkExtInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type MiniChunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RichContentId int32             `protobuf:"varint,1,opt,name=rich_content_id,json=richContentId,proto3" json:"rich_content_id,omitempty"`
	Id            int32             `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Runes         []int32           `protobuf:"varint,3,rep,packed,name=runes,proto3" json:"runes,omitempty"`
	Type          MiniChunkType     `protobuf:"varint,4,opt,name=type,proto3,enum=trpc.KEP.bot_admin_config_server.MiniChunkType" json:"type,omitempty"`
	Table         *Table            `protobuf:"bytes,5,opt,name=table,proto3" json:"table,omitempty"`
	Image         *Image            `protobuf:"bytes,6,opt,name=image,proto3" json:"image,omitempty"`
	ExtInfo       *MiniChunkExtInfo `protobuf:"bytes,7,opt,name=ext_info,json=extInfo,proto3" json:"ext_info,omitempty"`
}

func (x *MiniChunk) Reset() {
	*x = MiniChunk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MiniChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiniChunk) ProtoMessage() {}

func (x *MiniChunk) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiniChunk.ProtoReflect.Descriptor instead.
func (*MiniChunk) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{7}
}

func (x *MiniChunk) GetRichContentId() int32 {
	if x != nil {
		return x.RichContentId
	}
	return 0
}

func (x *MiniChunk) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MiniChunk) GetRunes() []int32 {
	if x != nil {
		return x.Runes
	}
	return nil
}

func (x *MiniChunk) GetType() MiniChunkType {
	if x != nil {
		return x.Type
	}
	return MiniChunkType_MiniChunkTypeText
}

func (x *MiniChunk) GetTable() *Table {
	if x != nil {
		return x.Table
	}
	return nil
}

func (x *MiniChunk) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *MiniChunk) GetExtInfo() *MiniChunkExtInfo {
	if x != nil {
		return x.ExtInfo
	}
	return nil
}

type PageContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prefix               []int32      `protobuf:"varint,1,rep,packed,name=prefix,proto3" json:"prefix,omitempty"`
	RichContentId        int32        `protobuf:"varint,2,opt,name=rich_content_id,json=richContentId,proto3" json:"rich_content_id,omitempty"`
	Id                   int32        `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	Body                 []*MiniChunk `protobuf:"bytes,4,rep,name=body,proto3" json:"body,omitempty"`
	Head                 []*MiniChunk `protobuf:"bytes,5,rep,name=head,proto3" json:"head,omitempty"`
	Tail                 []*MiniChunk `protobuf:"bytes,6,rep,name=tail,proto3" json:"tail,omitempty"`
	OrgStart             int32        `protobuf:"varint,7,opt,name=org_start,json=orgStart,proto3" json:"org_start,omitempty"`
	OrgEnd               int32        `protobuf:"varint,8,opt,name=org_end,json=orgEnd,proto3" json:"org_end,omitempty"`
	LinkerKeep           bool         `protobuf:"varint,9,opt,name=linker_keep,json=linkerKeep,proto3" json:"linker_keep,omitempty"`
	PageContentOrgString string       `protobuf:"bytes,10,opt,name=page_content_org_string,json=pageContentOrgString,proto3" json:"page_content_org_string,omitempty"`
	BigStart             int32        `protobuf:"varint,11,opt,name=big_start,json=bigStart,proto3" json:"big_start,omitempty"`
	BigEnd               int32        `protobuf:"varint,12,opt,name=big_end,json=bigEnd,proto3" json:"big_end,omitempty"`
	PageContentBigString string       `protobuf:"bytes,13,opt,name=page_content_big_string,json=pageContentBigString,proto3" json:"page_content_big_string,omitempty"`
}

func (x *PageContent) Reset() {
	*x = PageContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageContent) ProtoMessage() {}

func (x *PageContent) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageContent.ProtoReflect.Descriptor instead.
func (*PageContent) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{8}
}

func (x *PageContent) GetPrefix() []int32 {
	if x != nil {
		return x.Prefix
	}
	return nil
}

func (x *PageContent) GetRichContentId() int32 {
	if x != nil {
		return x.RichContentId
	}
	return 0
}

func (x *PageContent) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PageContent) GetBody() []*MiniChunk {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *PageContent) GetHead() []*MiniChunk {
	if x != nil {
		return x.Head
	}
	return nil
}

func (x *PageContent) GetTail() []*MiniChunk {
	if x != nil {
		return x.Tail
	}
	return nil
}

func (x *PageContent) GetOrgStart() int32 {
	if x != nil {
		return x.OrgStart
	}
	return 0
}

func (x *PageContent) GetOrgEnd() int32 {
	if x != nil {
		return x.OrgEnd
	}
	return 0
}

func (x *PageContent) GetLinkerKeep() bool {
	if x != nil {
		return x.LinkerKeep
	}
	return false
}

func (x *PageContent) GetPageContentOrgString() string {
	if x != nil {
		return x.PageContentOrgString
	}
	return ""
}

func (x *PageContent) GetBigStart() int32 {
	if x != nil {
		return x.BigStart
	}
	return 0
}

func (x *PageContent) GetBigEnd() int32 {
	if x != nil {
		return x.BigEnd
	}
	return 0
}

func (x *PageContent) GetPageContentBigString() string {
	if x != nil {
		return x.PageContentBigString
	}
	return ""
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name    string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Content []byte `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Url     string `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{9}
}

func (x *Image) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Image) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Image) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type RichContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MiniChunks   []*MiniChunk   `protobuf:"bytes,2,rep,name=mini_chunks,json=miniChunks,proto3" json:"mini_chunks,omitempty"`
	PageContents []*PageContent `protobuf:"bytes,3,rep,name=page_contents,json=pageContents,proto3" json:"page_contents,omitempty"`
}

func (x *RichContent) Reset() {
	*x = RichContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RichContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RichContent) ProtoMessage() {}

func (x *RichContent) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RichContent.ProtoReflect.Descriptor instead.
func (*RichContent) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{10}
}

func (x *RichContent) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RichContent) GetMiniChunks() []*MiniChunk {
	if x != nil {
		return x.MiniChunks
	}
	return nil
}

func (x *RichContent) GetPageContents() []*PageContent {
	if x != nil {
		return x.PageContents
	}
	return nil
}

// current_op_type == SPLIT 时的返回
type RichContents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RichContents []*RichContent `protobuf:"bytes,1,rep,name=rich_contents,json=richContents,proto3" json:"rich_contents,omitempty"`
	Images       []string       `protobuf:"bytes,2,rep,name=images,proto3" json:"images,omitempty"`
}

func (x *RichContents) Reset() {
	*x = RichContents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RichContents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RichContents) ProtoMessage() {}

func (x *RichContents) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RichContents.ProtoReflect.Descriptor instead.
func (*RichContents) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{11}
}

func (x *RichContents) GetRichContents() []*RichContent {
	if x != nil {
		return x.RichContents
	}
	return nil
}

func (x *RichContents) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

// current_op_type == PARSE 时的返回
type ParseResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result string   `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Images []string `protobuf:"bytes,2,rep,name=images,proto3" json:"images,omitempty"`
}

func (x *ParseResult) Reset() {
	*x = ParseResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_file_manager_callback_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParseResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseResult) ProtoMessage() {}

func (x *ParseResult) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_file_manager_callback_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseResult.ProtoReflect.Descriptor instead.
func (*ParseResult) Descriptor() ([]byte, []int) {
	return file_bot_admin_file_manager_callback_proto_rawDescGZIP(), []int{12}
}

func (x *ParseResult) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *ParseResult) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

var File_bot_admin_file_manager_callback_proto protoreflect.FileDescriptor

var file_bot_admin_file_manager_callback_proto_rawDesc = []byte{
	0x0a, 0x25, 0x62, 0x6f, 0x74, 0x2d, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2d, 0x66, 0x69, 0x6c, 0x65,
	0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0xeb, 0x06, 0x0a, 0x15, 0x46, 0x69,
	0x6c, 0x65, 0x50, 0x61, 0x72, 0x73, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x66, 0x0a, 0x0f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72,
	0x73, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x2e, 0x4f,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4f, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x5a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x73,
	0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x57, 0x0a, 0x06, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x40, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x73, 0x65, 0x72, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x05, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f,
	0x63, 0x6f, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x78, 0x74, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x74, 0x65, 0x78, 0x74, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f,
	0x6d, 0x64, 0x35, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x4d, 0x64, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x4a,
	0x0a, 0x0a, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x64, 0x65, 0x62, 0x75, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x41, 0x0a, 0x06, 0x4f, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x50, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x57, 0x4f, 0x52, 0x44,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x41, 0x52, 0x53, 0x45,
	0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x50, 0x4c, 0x49, 0x54, 0x10, 0x03, 0x22, 0x60, 0x0a,
	0x0a, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x0e, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x49, 0x4e,
	0x49, 0x53, 0x48, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x22,
	0x36, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x46,
	0x49, 0x4c, 0x45, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x41, 0x52, 0x4b, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12,
	0x06, 0x0a, 0x02, 0x50, 0x42, 0x10, 0x02, 0x22, 0x3c, 0x0a, 0x09, 0x44, 0x65, 0x62, 0x75, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x70, 0x61, 0x72, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43,
	0x6f, 0x73, 0x55, 0x72, 0x6c, 0x22, 0x52, 0x0a, 0x15, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72,
	0x73, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x42, 0x0a, 0x05, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x77, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x22, 0x43, 0x0a,
	0x03, 0x52, 0x6f, 0x77, 0x12, 0x3c, 0x0a, 0x05, 0x63, 0x65, 0x6c, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x65, 0x6c, 0x6c, 0x52, 0x05, 0x63, 0x65, 0x6c,
	0x6c, 0x73, 0x22, 0x1c, 0x0a, 0x04, 0x43, 0x65, 0x6c, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0xb1, 0x01, 0x0a, 0x10, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x45, 0x78,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x72, 0x75, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x6f,
	0x72, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x75, 0x6e, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f,
	0x72, 0x67, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6f, 0x72, 0x67, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x75,
	0x6e, 0x65, 0x73, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x75, 0x6e, 0x65, 0x73, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1f, 0x0a,
	0x0b, 0x68, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x68, 0x69, 0x74, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x22, 0xeb, 0x02, 0x0a, 0x09, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x69, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x69, 0x63,
	0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x75,
	0x6e, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x65, 0x73,
	0x12, 0x43, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x05, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x65, 0x78, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x9b, 0x04, 0x0a, 0x0b, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x69,
	0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x69, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x3f, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x04, 0x62,
	0x6f, 0x64, 0x79, 0x12, 0x3f, 0x0a, 0x04, 0x68, 0x65, 0x61, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x04,
	0x68, 0x65, 0x61, 0x64, 0x12, 0x3f, 0x0a, 0x04, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52,
	0x04, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x67, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x72, 0x67, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x45, 0x6e, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c,
	0x69, 0x6e, 0x6b, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x65, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x72, 0x4b, 0x65, 0x65, 0x70, 0x12, 0x35, 0x0a, 0x17,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x72, 0x67,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x67, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x67, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x67, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x62, 0x69, 0x67, 0x45, 0x6e, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x69, 0x67, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x70, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x22, 0x57, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xbf, 0x01, 0x0a, 0x0b, 0x52, 0x69,
	0x63, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4c, 0x0a, 0x0b, 0x6d, 0x69, 0x6e,
	0x69, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x0a, 0x6d, 0x69, 0x6e,
	0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x7a, 0x0a, 0x0c, 0x52,
	0x69, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x72,
	0x69, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x69, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x52, 0x0c, 0x72, 0x69, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x22, 0x3d, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x2a, 0x56, 0x0a, 0x0d, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x69, 0x6e, 0x69, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x78, 0x74, 0x10, 0x00, 0x12, 0x16,
	0x0a, 0x12, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x69, 0x6e, 0x69, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x02, 0x42, 0x4d,
	0x5a, 0x4b, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69,
	0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bot_admin_file_manager_callback_proto_rawDescOnce sync.Once
	file_bot_admin_file_manager_callback_proto_rawDescData = file_bot_admin_file_manager_callback_proto_rawDesc
)

func file_bot_admin_file_manager_callback_proto_rawDescGZIP() []byte {
	file_bot_admin_file_manager_callback_proto_rawDescOnce.Do(func() {
		file_bot_admin_file_manager_callback_proto_rawDescData = protoimpl.X.CompressGZIP(file_bot_admin_file_manager_callback_proto_rawDescData)
	})
	return file_bot_admin_file_manager_callback_proto_rawDescData
}

var file_bot_admin_file_manager_callback_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_bot_admin_file_manager_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_bot_admin_file_manager_callback_proto_goTypes = []interface{}{
	(MiniChunkType)(0),                    // 0: trpc.KEP.bot_admin_config_server.MiniChunkType
	(FileParserCallbackReq_OpType)(0),     // 1: trpc.KEP.bot_admin_config_server.FileParserCallbackReq.OpType
	(FileParserCallbackReq_TaskStatus)(0), // 2: trpc.KEP.bot_admin_config_server.FileParserCallbackReq.TaskStatus
	(FileParserCallbackReq_FileType)(0),   // 3: trpc.KEP.bot_admin_config_server.FileParserCallbackReq.FileType
	(*FileParserCallbackReq)(nil),         // 4: trpc.KEP.bot_admin_config_server.FileParserCallbackReq
	(*DebugInfo)(nil),                     // 5: trpc.KEP.bot_admin_config_server.DebugInfo
	(*FileParserCallbackRes)(nil),         // 6: trpc.KEP.bot_admin_config_server.FileParserCallbackRes
	(*Table)(nil),                         // 7: trpc.KEP.bot_admin_config_server.Table
	(*Row)(nil),                           // 8: trpc.KEP.bot_admin_config_server.Row
	(*Cell)(nil),                          // 9: trpc.KEP.bot_admin_config_server.Cell
	(*MiniChunkExtInfo)(nil),              // 10: trpc.KEP.bot_admin_config_server.MiniChunkExtInfo
	(*MiniChunk)(nil),                     // 11: trpc.KEP.bot_admin_config_server.MiniChunk
	(*PageContent)(nil),                   // 12: trpc.KEP.bot_admin_config_server.PageContent
	(*Image)(nil),                         // 13: trpc.KEP.bot_admin_config_server.Image
	(*RichContent)(nil),                   // 14: trpc.KEP.bot_admin_config_server.RichContent
	(*RichContents)(nil),                  // 15: trpc.KEP.bot_admin_config_server.RichContents
	(*ParseResult)(nil),                   // 16: trpc.KEP.bot_admin_config_server.ParseResult
}
var file_bot_admin_file_manager_callback_proto_depIdxs = []int32{
	1,  // 0: trpc.KEP.bot_admin_config_server.FileParserCallbackReq.current_op_type:type_name -> trpc.KEP.bot_admin_config_server.FileParserCallbackReq.OpType
	2,  // 1: trpc.KEP.bot_admin_config_server.FileParserCallbackReq.status:type_name -> trpc.KEP.bot_admin_config_server.FileParserCallbackReq.TaskStatus
	3,  // 2: trpc.KEP.bot_admin_config_server.FileParserCallbackReq.f_type:type_name -> trpc.KEP.bot_admin_config_server.FileParserCallbackReq.FileType
	5,  // 3: trpc.KEP.bot_admin_config_server.FileParserCallbackReq.debug_info:type_name -> trpc.KEP.bot_admin_config_server.DebugInfo
	8,  // 4: trpc.KEP.bot_admin_config_server.Table.rows:type_name -> trpc.KEP.bot_admin_config_server.Row
	9,  // 5: trpc.KEP.bot_admin_config_server.Row.cells:type_name -> trpc.KEP.bot_admin_config_server.Cell
	0,  // 6: trpc.KEP.bot_admin_config_server.MiniChunk.type:type_name -> trpc.KEP.bot_admin_config_server.MiniChunkType
	7,  // 7: trpc.KEP.bot_admin_config_server.MiniChunk.table:type_name -> trpc.KEP.bot_admin_config_server.Table
	13, // 8: trpc.KEP.bot_admin_config_server.MiniChunk.image:type_name -> trpc.KEP.bot_admin_config_server.Image
	10, // 9: trpc.KEP.bot_admin_config_server.MiniChunk.ext_info:type_name -> trpc.KEP.bot_admin_config_server.MiniChunkExtInfo
	11, // 10: trpc.KEP.bot_admin_config_server.PageContent.body:type_name -> trpc.KEP.bot_admin_config_server.MiniChunk
	11, // 11: trpc.KEP.bot_admin_config_server.PageContent.head:type_name -> trpc.KEP.bot_admin_config_server.MiniChunk
	11, // 12: trpc.KEP.bot_admin_config_server.PageContent.tail:type_name -> trpc.KEP.bot_admin_config_server.MiniChunk
	11, // 13: trpc.KEP.bot_admin_config_server.RichContent.mini_chunks:type_name -> trpc.KEP.bot_admin_config_server.MiniChunk
	12, // 14: trpc.KEP.bot_admin_config_server.RichContent.page_contents:type_name -> trpc.KEP.bot_admin_config_server.PageContent
	14, // 15: trpc.KEP.bot_admin_config_server.RichContents.rich_contents:type_name -> trpc.KEP.bot_admin_config_server.RichContent
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_bot_admin_file_manager_callback_proto_init() }
func file_bot_admin_file_manager_callback_proto_init() {
	if File_bot_admin_file_manager_callback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bot_admin_file_manager_callback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileParserCallbackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileParserCallbackRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Table); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Row); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cell); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MiniChunkExtInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MiniChunk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RichContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RichContents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_file_manager_callback_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParseResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bot_admin_file_manager_callback_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_bot_admin_file_manager_callback_proto_goTypes,
		DependencyIndexes: file_bot_admin_file_manager_callback_proto_depIdxs,
		EnumInfos:         file_bot_admin_file_manager_callback_proto_enumTypes,
		MessageInfos:      file_bot_admin_file_manager_callback_proto_msgTypes,
	}.Build()
	File_bot_admin_file_manager_callback_proto = out.File
	file_bot_admin_file_manager_callback_proto_rawDesc = nil
	file_bot_admin_file_manager_callback_proto_goTypes = nil
	file_bot_admin_file_manager_callback_proto_depIdxs = nil
}
