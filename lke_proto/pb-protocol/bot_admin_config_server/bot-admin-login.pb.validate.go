// Code generated by protoc-gen-secv. DO NOT EDIT.
// source: bot-admin-login.proto

package bot_admin_config_server

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
	_ = unicode.IsUpper
	_ = json.Valid([]byte(""))
)

// Validate checks the field values on LoginReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginReqMultiError, or nil
// if none found.
func (m *LoginReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Telephone

	// no validation rules for Code

	// no validation rules for IsAgreed

	if _, ok := _LoginReq_LoginType_InLookup[m.GetLoginType()]; !ok {
		err := LoginReqValidationError{
			field:  "LoginType",
			reason: "value must be in list [0 1 2]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAccount()) > 20 {
		err := LoginReqValidationError{
			field:  "Account",
			reason: "value length must be at most 20 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPassword()) > 32 {
		err := LoginReqValidationError{
			field:  "Password",
			reason: "value length must be at most 32 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UserType

	// no validation rules for Email

	if len(errors) > 0 {
		return LoginReqMultiError(errors)
	}

	return nil
}

// LoginReqMultiError is an error wrapping multiple validation errors returned
// by LoginReq.ValidateAll() if the designated constraints aren't met.
type LoginReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginReqMultiError) AllErrors() []error { return m }

// LoginReqValidationError is the validation error returned by
// LoginReq.Validate if the designated constraints aren't met.
type LoginReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginReqValidationError) ErrorName() string { return "LoginReqValidationError" }

// Error satisfies the builtin error interface
func (e LoginReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginReqValidationError{}

var _LoginReq_LoginType_InLookup = map[uint32]struct{}{
	0: {},
	1: {},
	2: {},
}

// Validate checks the field values on LoginRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginRspMultiError, or nil
// if none found.
func (m *LoginRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for UserName

	// no validation rules for UserAvatar

	// no validation rules for Telephone

	// no validation rules for StaffBizId

	// no validation rules for Email

	if len(errors) > 0 {
		return LoginRspMultiError(errors)
	}

	return nil
}

// LoginRspMultiError is an error wrapping multiple validation errors returned
// by LoginRsp.ValidateAll() if the designated constraints aren't met.
type LoginRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginRspMultiError) AllErrors() []error { return m }

// LoginRspValidationError is the validation error returned by
// LoginRsp.Validate if the designated constraints aren't met.
type LoginRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginRspValidationError) ErrorName() string { return "LoginRspValidationError" }

// Error satisfies the builtin error interface
func (e LoginRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginRspValidationError{}

// Validate checks the field values on LogoutReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogoutReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogoutReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogoutReqMultiError, or nil
// if none found.
func (m *LogoutReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LogoutReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LogoutReqMultiError(errors)
	}

	return nil
}

// LogoutReqMultiError is an error wrapping multiple validation errors returned
// by LogoutReq.ValidateAll() if the designated constraints aren't met.
type LogoutReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogoutReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogoutReqMultiError) AllErrors() []error { return m }

// LogoutReqValidationError is the validation error returned by
// LogoutReq.Validate if the designated constraints aren't met.
type LogoutReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogoutReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogoutReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogoutReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogoutReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogoutReqValidationError) ErrorName() string { return "LogoutReqValidationError" }

// Error satisfies the builtin error interface
func (e LogoutReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogoutReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogoutReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogoutReqValidationError{}

// Validate checks the field values on LogoutRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogoutRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogoutRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogoutRspMultiError, or nil
// if none found.
func (m *LogoutRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *LogoutRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LogoutRspMultiError(errors)
	}

	return nil
}

// LogoutRspMultiError is an error wrapping multiple validation errors returned
// by LogoutRsp.ValidateAll() if the designated constraints aren't met.
type LogoutRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogoutRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogoutRspMultiError) AllErrors() []error { return m }

// LogoutRspValidationError is the validation error returned by
// LogoutRsp.Validate if the designated constraints aren't met.
type LogoutRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogoutRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogoutRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogoutRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogoutRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogoutRspValidationError) ErrorName() string { return "LogoutRspValidationError" }

// Error satisfies the builtin error interface
func (e LogoutRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogoutRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogoutRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogoutRspValidationError{}

// Validate checks the field values on CheckSessionReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckSessionReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckSessionReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckSessionReqMultiError, or nil if none found.
func (m *CheckSessionReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckSessionReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CheckSessionReqMultiError(errors)
	}

	return nil
}

// CheckSessionReqMultiError is an error wrapping multiple validation errors
// returned by CheckSessionReq.ValidateAll() if the designated constraints
// aren't met.
type CheckSessionReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckSessionReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckSessionReqMultiError) AllErrors() []error { return m }

// CheckSessionReqValidationError is the validation error returned by
// CheckSessionReq.Validate if the designated constraints aren't met.
type CheckSessionReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckSessionReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckSessionReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckSessionReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckSessionReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckSessionReqValidationError) ErrorName() string { return "CheckSessionReqValidationError" }

// Error satisfies the builtin error interface
func (e CheckSessionReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckSessionReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckSessionReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckSessionReqValidationError{}

// Validate checks the field values on CheckSessionRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckSessionRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckSessionRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckSessionRspMultiError, or nil if none found.
func (m *CheckSessionRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckSessionRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StaffId

	// no validation rules for StaffBizId

	// no validation rules for CorpId

	// no validation rules for SId

	// no validation rules for UserType

	// no validation rules for CorpBizId

	if len(errors) > 0 {
		return CheckSessionRspMultiError(errors)
	}

	return nil
}

// CheckSessionRspMultiError is an error wrapping multiple validation errors
// returned by CheckSessionRsp.ValidateAll() if the designated constraints
// aren't met.
type CheckSessionRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckSessionRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckSessionRspMultiError) AllErrors() []error { return m }

// CheckSessionRspValidationError is the validation error returned by
// CheckSessionRsp.Validate if the designated constraints aren't met.
type CheckSessionRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckSessionRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckSessionRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckSessionRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckSessionRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckSessionRspValidationError) ErrorName() string { return "CheckSessionRspValidationError" }

// Error satisfies the builtin error interface
func (e CheckSessionRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckSessionRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckSessionRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckSessionRspValidationError{}

// Validate checks the field values on CheckPermissionReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckPermissionReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckPermissionReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckPermissionReqMultiError, or nil if none found.
func (m *CheckPermissionReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckPermissionReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Action

	// no validation rules for AppType

	if len(errors) > 0 {
		return CheckPermissionReqMultiError(errors)
	}

	return nil
}

// CheckPermissionReqMultiError is an error wrapping multiple validation errors
// returned by CheckPermissionReq.ValidateAll() if the designated constraints
// aren't met.
type CheckPermissionReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckPermissionReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckPermissionReqMultiError) AllErrors() []error { return m }

// CheckPermissionReqValidationError is the validation error returned by
// CheckPermissionReq.Validate if the designated constraints aren't met.
type CheckPermissionReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckPermissionReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckPermissionReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckPermissionReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckPermissionReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckPermissionReqValidationError) ErrorName() string {
	return "CheckPermissionReqValidationError"
}

// Error satisfies the builtin error interface
func (e CheckPermissionReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckPermissionReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckPermissionReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckPermissionReqValidationError{}

// Validate checks the field values on CheckPermissionRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckPermissionRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckPermissionRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckPermissionRspMultiError, or nil if none found.
func (m *CheckPermissionRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckPermissionRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HasPermission

	if len(errors) > 0 {
		return CheckPermissionRspMultiError(errors)
	}

	return nil
}

// CheckPermissionRspMultiError is an error wrapping multiple validation errors
// returned by CheckPermissionRsp.ValidateAll() if the designated constraints
// aren't met.
type CheckPermissionRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckPermissionRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckPermissionRspMultiError) AllErrors() []error { return m }

// CheckPermissionRspValidationError is the validation error returned by
// CheckPermissionRsp.Validate if the designated constraints aren't met.
type CheckPermissionRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckPermissionRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckPermissionRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckPermissionRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckPermissionRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckPermissionRspValidationError) ErrorName() string {
	return "CheckPermissionRspValidationError"
}

// Error satisfies the builtin error interface
func (e CheckPermissionRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckPermissionRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckPermissionRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckPermissionRspValidationError{}

// Validate checks the field values on SendVerifyCodeReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SendVerifyCodeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendVerifyCodeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendVerifyCodeReqMultiError, or nil if none found.
func (m *SendVerifyCodeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SendVerifyCodeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTelephone()) < 1 {
		err := SendVerifyCodeReqValidationError{
			field:  "Telephone",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SendVerifyCodeReqMultiError(errors)
	}

	return nil
}

// SendVerifyCodeReqMultiError is an error wrapping multiple validation errors
// returned by SendVerifyCodeReq.ValidateAll() if the designated constraints
// aren't met.
type SendVerifyCodeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendVerifyCodeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendVerifyCodeReqMultiError) AllErrors() []error { return m }

// SendVerifyCodeReqValidationError is the validation error returned by
// SendVerifyCodeReq.Validate if the designated constraints aren't met.
type SendVerifyCodeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendVerifyCodeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendVerifyCodeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendVerifyCodeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendVerifyCodeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendVerifyCodeReqValidationError) ErrorName() string {
	return "SendVerifyCodeReqValidationError"
}

// Error satisfies the builtin error interface
func (e SendVerifyCodeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendVerifyCodeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendVerifyCodeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendVerifyCodeReqValidationError{}

// Validate checks the field values on SendVerifyCodeRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SendVerifyCodeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendVerifyCodeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendVerifyCodeRspMultiError, or nil if none found.
func (m *SendVerifyCodeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SendVerifyCodeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SendVerifyCodeRspMultiError(errors)
	}

	return nil
}

// SendVerifyCodeRspMultiError is an error wrapping multiple validation errors
// returned by SendVerifyCodeRsp.ValidateAll() if the designated constraints
// aren't met.
type SendVerifyCodeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendVerifyCodeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendVerifyCodeRspMultiError) AllErrors() []error { return m }

// SendVerifyCodeRspValidationError is the validation error returned by
// SendVerifyCodeRsp.Validate if the designated constraints aren't met.
type SendVerifyCodeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendVerifyCodeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendVerifyCodeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendVerifyCodeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendVerifyCodeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendVerifyCodeRspValidationError) ErrorName() string {
	return "SendVerifyCodeRspValidationError"
}

// Error satisfies the builtin error interface
func (e SendVerifyCodeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendVerifyCodeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendVerifyCodeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendVerifyCodeRspValidationError{}

// Validate checks the field values on SendVerifyCodeNewReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendVerifyCodeNewReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendVerifyCodeNewReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendVerifyCodeNewReqMultiError, or nil if none found.
func (m *SendVerifyCodeNewReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SendVerifyCodeNewReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Telephone

	// no validation rules for SmsType

	if all {
		switch v := interface{}(m.GetCaptchaTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendVerifyCodeNewReqValidationError{
					field:  "CaptchaTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendVerifyCodeNewReqValidationError{
					field:  "CaptchaTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaptchaTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendVerifyCodeNewReqValidationError{
				field:  "CaptchaTicket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MessageType

	// no validation rules for Email

	if len(errors) > 0 {
		return SendVerifyCodeNewReqMultiError(errors)
	}

	return nil
}

// SendVerifyCodeNewReqMultiError is an error wrapping multiple validation
// errors returned by SendVerifyCodeNewReq.ValidateAll() if the designated
// constraints aren't met.
type SendVerifyCodeNewReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendVerifyCodeNewReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendVerifyCodeNewReqMultiError) AllErrors() []error { return m }

// SendVerifyCodeNewReqValidationError is the validation error returned by
// SendVerifyCodeNewReq.Validate if the designated constraints aren't met.
type SendVerifyCodeNewReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendVerifyCodeNewReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendVerifyCodeNewReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendVerifyCodeNewReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendVerifyCodeNewReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendVerifyCodeNewReqValidationError) ErrorName() string {
	return "SendVerifyCodeNewReqValidationError"
}

// Error satisfies the builtin error interface
func (e SendVerifyCodeNewReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendVerifyCodeNewReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendVerifyCodeNewReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendVerifyCodeNewReqValidationError{}

// Validate checks the field values on VerifyCaptchaTicket with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyCaptchaTicket) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyCaptchaTicket with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyCaptchaTicketMultiError, or nil if none found.
func (m *VerifyCaptchaTicket) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyCaptchaTicket) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ticket

	// no validation rules for RandStr

	if len(errors) > 0 {
		return VerifyCaptchaTicketMultiError(errors)
	}

	return nil
}

// VerifyCaptchaTicketMultiError is an error wrapping multiple validation
// errors returned by VerifyCaptchaTicket.ValidateAll() if the designated
// constraints aren't met.
type VerifyCaptchaTicketMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyCaptchaTicketMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyCaptchaTicketMultiError) AllErrors() []error { return m }

// VerifyCaptchaTicketValidationError is the validation error returned by
// VerifyCaptchaTicket.Validate if the designated constraints aren't met.
type VerifyCaptchaTicketValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyCaptchaTicketValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyCaptchaTicketValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyCaptchaTicketValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyCaptchaTicketValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyCaptchaTicketValidationError) ErrorName() string {
	return "VerifyCaptchaTicketValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyCaptchaTicketValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyCaptchaTicket.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyCaptchaTicketValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyCaptchaTicketValidationError{}

// Validate checks the field values on SendVerifyCodeNewRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendVerifyCodeNewRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendVerifyCodeNewRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendVerifyCodeNewRspMultiError, or nil if none found.
func (m *SendVerifyCodeNewRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SendVerifyCodeNewRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SendVerifyCodeNewRspMultiError(errors)
	}

	return nil
}

// SendVerifyCodeNewRspMultiError is an error wrapping multiple validation
// errors returned by SendVerifyCodeNewRsp.ValidateAll() if the designated
// constraints aren't met.
type SendVerifyCodeNewRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendVerifyCodeNewRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendVerifyCodeNewRspMultiError) AllErrors() []error { return m }

// SendVerifyCodeNewRspValidationError is the validation error returned by
// SendVerifyCodeNewRsp.Validate if the designated constraints aren't met.
type SendVerifyCodeNewRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendVerifyCodeNewRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendVerifyCodeNewRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendVerifyCodeNewRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendVerifyCodeNewRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendVerifyCodeNewRspValidationError) ErrorName() string {
	return "SendVerifyCodeNewRspValidationError"
}

// Error satisfies the builtin error interface
func (e SendVerifyCodeNewRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendVerifyCodeNewRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendVerifyCodeNewRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendVerifyCodeNewRspValidationError{}

// Validate checks the field values on RegisterCorpReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RegisterCorpReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterCorpReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterCorpReqMultiError, or nil if none found.
func (m *RegisterCorpReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterCorpReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCorpFullName()) < 1 {
		err := RegisterCorpReqValidationError{
			field:  "CorpFullName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetContactName()) < 1 {
		err := RegisterCorpReqValidationError{
			field:  "ContactName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetEmail()) < 1 {
		err := RegisterCorpReqValidationError{
			field:  "Email",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTelephone()) < 1 {
		err := RegisterCorpReqValidationError{
			field:  "Telephone",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCode()) < 1 {
		err := RegisterCorpReqValidationError{
			field:  "Code",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RegisterCorpReqMultiError(errors)
	}

	return nil
}

// RegisterCorpReqMultiError is an error wrapping multiple validation errors
// returned by RegisterCorpReq.ValidateAll() if the designated constraints
// aren't met.
type RegisterCorpReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterCorpReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterCorpReqMultiError) AllErrors() []error { return m }

// RegisterCorpReqValidationError is the validation error returned by
// RegisterCorpReq.Validate if the designated constraints aren't met.
type RegisterCorpReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterCorpReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterCorpReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterCorpReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterCorpReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterCorpReqValidationError) ErrorName() string { return "RegisterCorpReqValidationError" }

// Error satisfies the builtin error interface
func (e RegisterCorpReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterCorpReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterCorpReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterCorpReqValidationError{}

// Validate checks the field values on RegisterCorpRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RegisterCorpRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterCorpRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterCorpRspMultiError, or nil if none found.
func (m *RegisterCorpRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterCorpRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RegisterCorpRspMultiError(errors)
	}

	return nil
}

// RegisterCorpRspMultiError is an error wrapping multiple validation errors
// returned by RegisterCorpRsp.ValidateAll() if the designated constraints
// aren't met.
type RegisterCorpRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterCorpRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterCorpRspMultiError) AllErrors() []error { return m }

// RegisterCorpRspValidationError is the validation error returned by
// RegisterCorpRsp.Validate if the designated constraints aren't met.
type RegisterCorpRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterCorpRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterCorpRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterCorpRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterCorpRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterCorpRspValidationError) ErrorName() string { return "RegisterCorpRspValidationError" }

// Error satisfies the builtin error interface
func (e RegisterCorpRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterCorpRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterCorpRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterCorpRspValidationError{}

// Validate checks the field values on CheckCorpAndStaffReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckCorpAndStaffReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckCorpAndStaffReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckCorpAndStaffReqMultiError, or nil if none found.
func (m *CheckCorpAndStaffReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCorpAndStaffReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCorpBizId() <= 0 {
		err := CheckCorpAndStaffReqValidationError{
			field:  "CorpBizId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetStaffBizId() <= 0 {
		err := CheckCorpAndStaffReqValidationError{
			field:  "StaffBizId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckCorpAndStaffReqMultiError(errors)
	}

	return nil
}

// CheckCorpAndStaffReqMultiError is an error wrapping multiple validation
// errors returned by CheckCorpAndStaffReq.ValidateAll() if the designated
// constraints aren't met.
type CheckCorpAndStaffReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCorpAndStaffReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCorpAndStaffReqMultiError) AllErrors() []error { return m }

// CheckCorpAndStaffReqValidationError is the validation error returned by
// CheckCorpAndStaffReq.Validate if the designated constraints aren't met.
type CheckCorpAndStaffReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCorpAndStaffReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCorpAndStaffReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCorpAndStaffReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCorpAndStaffReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCorpAndStaffReqValidationError) ErrorName() string {
	return "CheckCorpAndStaffReqValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCorpAndStaffReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCorpAndStaffReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCorpAndStaffReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCorpAndStaffReqValidationError{}

// Validate checks the field values on CheckCorpAndStaffRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckCorpAndStaffRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckCorpAndStaffRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckCorpAndStaffRspMultiError, or nil if none found.
func (m *CheckCorpAndStaffRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCorpAndStaffRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CheckCorpAndStaffRspMultiError(errors)
	}

	return nil
}

// CheckCorpAndStaffRspMultiError is an error wrapping multiple validation
// errors returned by CheckCorpAndStaffRsp.ValidateAll() if the designated
// constraints aren't met.
type CheckCorpAndStaffRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCorpAndStaffRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCorpAndStaffRspMultiError) AllErrors() []error { return m }

// CheckCorpAndStaffRspValidationError is the validation error returned by
// CheckCorpAndStaffRsp.Validate if the designated constraints aren't met.
type CheckCorpAndStaffRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCorpAndStaffRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCorpAndStaffRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCorpAndStaffRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCorpAndStaffRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCorpAndStaffRspValidationError) ErrorName() string {
	return "CheckCorpAndStaffRspValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCorpAndStaffRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCorpAndStaffRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCorpAndStaffRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCorpAndStaffRspValidationError{}

// Validate checks the field values on JoinCorpReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JoinCorpReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JoinCorpReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in JoinCorpReqMultiError, or
// nil if none found.
func (m *JoinCorpReq) ValidateAll() error {
	return m.validate(true)
}

func (m *JoinCorpReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCorpBizId() <= 0 {
		err := JoinCorpReqValidationError{
			field:  "CorpBizId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetStaffBizId() <= 0 {
		err := JoinCorpReqValidationError{
			field:  "StaffBizId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := JoinCorpReqValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTelephone()) < 1 {
		err := JoinCorpReqValidationError{
			field:  "Telephone",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCode()) < 1 {
		err := JoinCorpReqValidationError{
			field:  "Code",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return JoinCorpReqMultiError(errors)
	}

	return nil
}

// JoinCorpReqMultiError is an error wrapping multiple validation errors
// returned by JoinCorpReq.ValidateAll() if the designated constraints aren't met.
type JoinCorpReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JoinCorpReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JoinCorpReqMultiError) AllErrors() []error { return m }

// JoinCorpReqValidationError is the validation error returned by
// JoinCorpReq.Validate if the designated constraints aren't met.
type JoinCorpReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JoinCorpReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JoinCorpReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JoinCorpReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JoinCorpReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JoinCorpReqValidationError) ErrorName() string { return "JoinCorpReqValidationError" }

// Error satisfies the builtin error interface
func (e JoinCorpReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJoinCorpReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JoinCorpReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JoinCorpReqValidationError{}

// Validate checks the field values on JoinCorpRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JoinCorpRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JoinCorpRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in JoinCorpRspMultiError, or
// nil if none found.
func (m *JoinCorpRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *JoinCorpRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for UserName

	// no validation rules for UserAvatar

	// no validation rules for Telephone

	if len(errors) > 0 {
		return JoinCorpRspMultiError(errors)
	}

	return nil
}

// JoinCorpRspMultiError is an error wrapping multiple validation errors
// returned by JoinCorpRsp.ValidateAll() if the designated constraints aren't met.
type JoinCorpRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JoinCorpRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JoinCorpRspMultiError) AllErrors() []error { return m }

// JoinCorpRspValidationError is the validation error returned by
// JoinCorpRsp.Validate if the designated constraints aren't met.
type JoinCorpRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JoinCorpRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JoinCorpRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JoinCorpRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JoinCorpRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JoinCorpRspValidationError) ErrorName() string { return "JoinCorpRspValidationError" }

// Error satisfies the builtin error interface
func (e JoinCorpRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJoinCorpRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JoinCorpRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JoinCorpRspValidationError{}

// Validate checks the field values on ExitCorpReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExitCorpReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExitCorpReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExitCorpReqMultiError, or
// nil if none found.
func (m *ExitCorpReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ExitCorpReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ExitCorpReqMultiError(errors)
	}

	return nil
}

// ExitCorpReqMultiError is an error wrapping multiple validation errors
// returned by ExitCorpReq.ValidateAll() if the designated constraints aren't met.
type ExitCorpReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExitCorpReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExitCorpReqMultiError) AllErrors() []error { return m }

// ExitCorpReqValidationError is the validation error returned by
// ExitCorpReq.Validate if the designated constraints aren't met.
type ExitCorpReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExitCorpReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExitCorpReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExitCorpReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExitCorpReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExitCorpReqValidationError) ErrorName() string { return "ExitCorpReqValidationError" }

// Error satisfies the builtin error interface
func (e ExitCorpReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExitCorpReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExitCorpReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExitCorpReqValidationError{}

// Validate checks the field values on ExitCorpRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExitCorpRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExitCorpRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExitCorpRspMultiError, or
// nil if none found.
func (m *ExitCorpRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ExitCorpRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ExitCorpRspMultiError(errors)
	}

	return nil
}

// ExitCorpRspMultiError is an error wrapping multiple validation errors
// returned by ExitCorpRsp.ValidateAll() if the designated constraints aren't met.
type ExitCorpRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExitCorpRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExitCorpRspMultiError) AllErrors() []error { return m }

// ExitCorpRspValidationError is the validation error returned by
// ExitCorpRsp.Validate if the designated constraints aren't met.
type ExitCorpRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExitCorpRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExitCorpRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExitCorpRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExitCorpRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExitCorpRspValidationError) ErrorName() string { return "ExitCorpRspValidationError" }

// Error satisfies the builtin error interface
func (e ExitCorpRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExitCorpRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExitCorpRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExitCorpRspValidationError{}
