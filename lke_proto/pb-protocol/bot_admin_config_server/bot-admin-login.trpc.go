// Code generated by trpc-go/trpc-go-cmdline v2.4.0. DO NOT EDIT.
// source: bot-admin-login.proto

package bot_admin_config_server

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

// START ======================================= Server Service Definition ======================================= START

// LoginService defines service.
type LoginService interface {
	// Login 登录操作
	Login(ctx context.Context, req *LoginReq) (*LoginRsp, error)
	// Logout 登出操作
	Logout(ctx context.Context, req *LogoutReq) (*LogoutRsp, error)
	// CheckSession 检查session
	CheckSession(ctx context.Context, req *CheckSessionReq) (*CheckSessionRsp, error)
	// CheckPermission 检查permission
	CheckPermission(ctx context.Context, req *CheckPermissionReq) (*CheckPermissionRsp, error)
	// SendVerifyCode Deprecated 以下接口待删除
	//  获取验证码
	SendVerifyCode(ctx context.Context, req *SendVerifyCodeReq) (*SendVerifyCodeRsp, error)
	// SendVerifyCodeNew 获取验证码
	SendVerifyCodeNew(ctx context.Context, req *SendVerifyCodeNewReq) (*SendVerifyCodeNewRsp, error)
	// RegisterCorp 注册企业
	RegisterCorp(ctx context.Context, req *RegisterCorpReq) (*RegisterCorpRsp, error)
	// CheckCorpAndStaff 校验企业与员工是否有效
	CheckCorpAndStaff(ctx context.Context, req *CheckCorpAndStaffReq) (*CheckCorpAndStaffRsp, error)
	// JoinCorp 加入企业
	JoinCorp(ctx context.Context, req *JoinCorpReq) (*JoinCorpRsp, error)
	// ExitCorp 退出企业
	ExitCorp(ctx context.Context, req *ExitCorpReq) (*ExitCorpRsp, error)
}

func LoginService_Login_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &LoginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).Login(ctx, reqbody.(*LoginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_Logout_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &LogoutReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).Logout(ctx, reqbody.(*LogoutReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_CheckSession_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckSessionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).CheckSession(ctx, reqbody.(*CheckSessionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_CheckPermission_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckPermissionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).CheckPermission(ctx, reqbody.(*CheckPermissionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_SendVerifyCode_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SendVerifyCodeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).SendVerifyCode(ctx, reqbody.(*SendVerifyCodeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_SendVerifyCodeNew_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SendVerifyCodeNewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).SendVerifyCodeNew(ctx, reqbody.(*SendVerifyCodeNewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_RegisterCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RegisterCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).RegisterCorp(ctx, reqbody.(*RegisterCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_CheckCorpAndStaff_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckCorpAndStaffReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).CheckCorpAndStaff(ctx, reqbody.(*CheckCorpAndStaffReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_JoinCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &JoinCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).JoinCorp(ctx, reqbody.(*JoinCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func LoginService_ExitCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExitCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(LoginService).ExitCorp(ctx, reqbody.(*ExitCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// LoginServer_ServiceDesc descriptor for server.RegisterService.
var LoginServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.bot_admin_config_server.Login",
	HandlerType: ((*LoginService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/Login",
			Func: LoginService_Login_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/Logout",
			Func: LoginService_Logout_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/CheckSession",
			Func: LoginService_CheckSession_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/CheckPermission",
			Func: LoginService_CheckPermission_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/SendVerifyCode",
			Func: LoginService_SendVerifyCode_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/SendVerifyCodeNew",
			Func: LoginService_SendVerifyCodeNew_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/RegisterCorp",
			Func: LoginService_RegisterCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/CheckCorpAndStaff",
			Func: LoginService_CheckCorpAndStaff_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/JoinCorp",
			Func: LoginService_JoinCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Login/ExitCorp",
			Func: LoginService_ExitCorp_Handler,
		},
	},
}

// RegisterLoginService registers service.
func RegisterLoginService(s server.Service, svr LoginService) {
	if err := s.Register(&LoginServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("Login register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedLogin struct{}

// Login 登录操作
func (s *UnimplementedLogin) Login(ctx context.Context, req *LoginReq) (*LoginRsp, error) {
	return nil, errors.New("rpc Login of service Login is not implemented")
}

// Logout 登出操作
func (s *UnimplementedLogin) Logout(ctx context.Context, req *LogoutReq) (*LogoutRsp, error) {
	return nil, errors.New("rpc Logout of service Login is not implemented")
}

// CheckSession 检查session
func (s *UnimplementedLogin) CheckSession(ctx context.Context, req *CheckSessionReq) (*CheckSessionRsp, error) {
	return nil, errors.New("rpc CheckSession of service Login is not implemented")
}

// CheckPermission 检查permission
func (s *UnimplementedLogin) CheckPermission(ctx context.Context, req *CheckPermissionReq) (*CheckPermissionRsp, error) {
	return nil, errors.New("rpc CheckPermission of service Login is not implemented")
}

// SendVerifyCode Deprecated 以下接口待删除
//
//	获取验证码
func (s *UnimplementedLogin) SendVerifyCode(ctx context.Context, req *SendVerifyCodeReq) (*SendVerifyCodeRsp, error) {
	return nil, errors.New("rpc SendVerifyCode of service Login is not implemented")
}

// SendVerifyCodeNew 获取验证码
func (s *UnimplementedLogin) SendVerifyCodeNew(ctx context.Context, req *SendVerifyCodeNewReq) (*SendVerifyCodeNewRsp, error) {
	return nil, errors.New("rpc SendVerifyCodeNew of service Login is not implemented")
}

// RegisterCorp 注册企业
func (s *UnimplementedLogin) RegisterCorp(ctx context.Context, req *RegisterCorpReq) (*RegisterCorpRsp, error) {
	return nil, errors.New("rpc RegisterCorp of service Login is not implemented")
}

// CheckCorpAndStaff 校验企业与员工是否有效
func (s *UnimplementedLogin) CheckCorpAndStaff(ctx context.Context, req *CheckCorpAndStaffReq) (*CheckCorpAndStaffRsp, error) {
	return nil, errors.New("rpc CheckCorpAndStaff of service Login is not implemented")
}

// JoinCorp 加入企业
func (s *UnimplementedLogin) JoinCorp(ctx context.Context, req *JoinCorpReq) (*JoinCorpRsp, error) {
	return nil, errors.New("rpc JoinCorp of service Login is not implemented")
}

// ExitCorp 退出企业
func (s *UnimplementedLogin) ExitCorp(ctx context.Context, req *ExitCorpReq) (*ExitCorpRsp, error) {
	return nil, errors.New("rpc ExitCorp of service Login is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// LoginClientProxy defines service client proxy
type LoginClientProxy interface {
	// Login 登录操作
	Login(ctx context.Context, req *LoginReq, opts ...client.Option) (rsp *LoginRsp, err error)
	// Logout 登出操作
	Logout(ctx context.Context, req *LogoutReq, opts ...client.Option) (rsp *LogoutRsp, err error)
	// CheckSession 检查session
	CheckSession(ctx context.Context, req *CheckSessionReq, opts ...client.Option) (rsp *CheckSessionRsp, err error)
	// CheckPermission 检查permission
	CheckPermission(ctx context.Context, req *CheckPermissionReq, opts ...client.Option) (rsp *CheckPermissionRsp, err error)
	// SendVerifyCode Deprecated 以下接口待删除
	//  获取验证码
	SendVerifyCode(ctx context.Context, req *SendVerifyCodeReq, opts ...client.Option) (rsp *SendVerifyCodeRsp, err error)
	// SendVerifyCodeNew 获取验证码
	SendVerifyCodeNew(ctx context.Context, req *SendVerifyCodeNewReq, opts ...client.Option) (rsp *SendVerifyCodeNewRsp, err error)
	// RegisterCorp 注册企业
	RegisterCorp(ctx context.Context, req *RegisterCorpReq, opts ...client.Option) (rsp *RegisterCorpRsp, err error)
	// CheckCorpAndStaff 校验企业与员工是否有效
	CheckCorpAndStaff(ctx context.Context, req *CheckCorpAndStaffReq, opts ...client.Option) (rsp *CheckCorpAndStaffRsp, err error)
	// JoinCorp 加入企业
	JoinCorp(ctx context.Context, req *JoinCorpReq, opts ...client.Option) (rsp *JoinCorpRsp, err error)
	// ExitCorp 退出企业
	ExitCorp(ctx context.Context, req *ExitCorpReq, opts ...client.Option) (rsp *ExitCorpRsp, err error)
}

type LoginClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewLoginClientProxy = func(opts ...client.Option) LoginClientProxy {
	return &LoginClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *LoginClientProxyImpl) Login(ctx context.Context, req *LoginReq, opts ...client.Option) (*LoginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/Login")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("Login")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &LoginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) Logout(ctx context.Context, req *LogoutReq, opts ...client.Option) (*LogoutRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/Logout")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("Logout")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &LogoutRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) CheckSession(ctx context.Context, req *CheckSessionReq, opts ...client.Option) (*CheckSessionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/CheckSession")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("CheckSession")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckSessionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) CheckPermission(ctx context.Context, req *CheckPermissionReq, opts ...client.Option) (*CheckPermissionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/CheckPermission")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("CheckPermission")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckPermissionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) SendVerifyCode(ctx context.Context, req *SendVerifyCodeReq, opts ...client.Option) (*SendVerifyCodeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/SendVerifyCode")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("SendVerifyCode")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SendVerifyCodeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) SendVerifyCodeNew(ctx context.Context, req *SendVerifyCodeNewReq, opts ...client.Option) (*SendVerifyCodeNewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/SendVerifyCodeNew")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("SendVerifyCodeNew")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SendVerifyCodeNewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) RegisterCorp(ctx context.Context, req *RegisterCorpReq, opts ...client.Option) (*RegisterCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/RegisterCorp")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("RegisterCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RegisterCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) CheckCorpAndStaff(ctx context.Context, req *CheckCorpAndStaffReq, opts ...client.Option) (*CheckCorpAndStaffRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/CheckCorpAndStaff")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("CheckCorpAndStaff")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckCorpAndStaffRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) JoinCorp(ctx context.Context, req *JoinCorpReq, opts ...client.Option) (*JoinCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/JoinCorp")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("JoinCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &JoinCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *LoginClientProxyImpl) ExitCorp(ctx context.Context, req *ExitCorpReq, opts ...client.Option) (*ExitCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Login/ExitCorp")
	msg.WithCalleeServiceName(LoginServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Login")
	msg.WithCalleeMethod("ExitCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExitCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
