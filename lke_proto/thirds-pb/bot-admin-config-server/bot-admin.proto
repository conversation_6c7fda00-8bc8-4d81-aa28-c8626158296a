syntax = "proto3";

package trpc.KEP.bot_admin_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server";

import "validate.proto";
import "bot-admin-common.proto";
import "bot-admin-custom-resource.proto";
import "bot-admin-file-manager-callback.proto";

// 管理后台 需要鉴权
service Admin {
  // Deprecated 企业消息
  rpc CorpInfo(CorpInfoReq) returns (CorpInfoRsp);
  // 企业消息
  rpc DescribeCorp(DescribeCorpReq) returns (DescribeCorpRsp);
  // 员工信息列表
  rpc ListCorpStaff(ListCorpStaffReq) returns (ListCorpStaffRsp);
  // 获取对象存储的临时密钥
  rpc DescribeStorageCredential(DescribeStorageCredentialReq) returns (DescribeStorageCredentialRsp);
  // Deprecated 获取对象存储的临时密钥
  rpc GetCredential(GetCredentialReq) returns (GetCredentialRsp);
  // Deprecated 获取审核开关
  rpc GetAuditSwitch(GetAuditSwitchReq) returns (GetAuditSwitchRsp);
  // 获取审核开关
  rpc DescribeAuditSwitch(DescribeAuditSwitchReq) returns (DescribeAuditSwitchRsp);
  // 文档列表
  rpc ListDoc(ListDocReq) returns (ListDocRsp);
  // 文档列表
  rpc ListDocV1(ListDocV1Req) returns (ListDocV1Rsp);
  // 保存文档
  rpc SaveDocV1(SaveDocV1Req) returns (SaveDocV1Rsp);
  // 修改文档
  rpc ModifyDoc(ModifyDocReq) returns (ModifyDocRsp);
  // 批量编辑文档适用范围
  rpc ModifyDocAttrRange(ModifyDocAttrRangeReq) returns (ModifyDocAttrRangeRsp);
  // 修改文档
  rpc ModifyDocV1(ModifyDocV1Req) returns (ModifyDocV1Rsp);
  // 删除文档
  rpc DeleteDoc(DeleteDocReq) returns (DeleteDocRsp);
  // 获取文档详情
  rpc DescribeDoc(DescribeDocReq) returns (DescribeDocRsp);
  // 答案中是否引用
  rpc ReferDoc(ReferDocReq) returns (ReferDocRsp);
  // Deprecated 重新生成QA对
  rpc StartCreateQA(StartCreateQAReq) returns (StartCreateQARsp);
  // 获取文档预览链接
  rpc GetDocPreview(GetDocPreviewReq) returns (GetDocPreviewRsp);
  // 生成QA对
  rpc GenerateQA(GenerateQAReq) returns (GenerateQARsp);
  // Deprecated 获取文档下拉列表
  rpc GetSelectDoc(GetSelectDocReq) returns (GetSelectDocRsp);
  // 获取文档下拉列表
  rpc ListSelectDoc(ListSelectDocReq) returns (ListSelectDocRsp);
  // 中止文档解析
  rpc StopDocParse(StopDocParseReq) returns (StopDocParseRsp);
  // 重试文档解析
  rpc RetryDocParse(RetryDocParseReq) returns (RetryDocParseRsp);
  // 重试文档审核
  rpc RetryDocAudit(RetryDocAuditReq) returns (RetryDocAuditRsp);

  // 新建机器人
  rpc CreateRobot(CreateRobotReq) returns (CreateRobotRsp);
  // 删除机器人
  rpc DeleteRobot(DeleteRobotReq) returns (DeleteRobotRsp);
  // Deprecated 更新机器人属性
  rpc ModifyRobot(ModifyRobotReq) returns (ModifyRobotRsp);
  // 更新机器人基础属性
  rpc ModifyRobotBasicConfig(ModifyRobotBasicConfigReq) returns (ModifyRobotBasicConfigRsp);
  // Deprecated 更新机器人属性
  rpc UpdateRobot(UpdateRobotReq) returns (UpdateRobotRsp);
  // Deprecated 获取机器人
  rpc RobotInfo(RobotInfoReq) returns (RobotInfoRsp);
  // 获取机器人
  rpc DescribeRobot(DescribeRobotReq) returns (DescribeRobotRsp);
  // 获取机器人列表
  rpc ListRobot(ListRobotReq) returns (ListRobotRsp);
  // 获取机器人列表
  rpc ListRobotV1(ListRobotV1Req) returns (ListRobotV1Rsp);
  // Deprecated 获取账户信息
  rpc AccountInfo(AccountInfoReq) returns (AccountInfoRsp);
  // 获取账户信息
  rpc DescribeAccount(DescribeAccountReq) returns (DescribeAccountRsp);
  // Deprecated 获取机器人角色配置
  rpc ListRole(ListRoleReq) returns (ListRoleRsp);
  // 获取机器人角色配置
  rpc ListRobotRole(ListRobotRoleReq) returns (ListRobotRoleRsp);
  // 获取QA分类
  rpc ListQACate(ListQACateReq) returns (ListQACateRsp);
  // Deprecated 获取QA分类V1
  rpc ListQACateV1(ListQACateV1Req) returns (ListQACateV1Rsp);
  // 创建QA分类
  rpc CreateQACate(CreateQACateReq) returns (CreateQACateRsp);
  // Deprecated 创建QA分类
  rpc CreateQACateV1(CreateQACateV1Req) returns (CreateQACateV1Rsp);
  // Deprecated QA分类修改
  rpc UpdateQACate(UpdateQACateReq) returns (UpdateQACateRsp);
  // QA分类修改
  rpc ModifyQACate(ModifyQACateReq) returns (ModifyQACateRsp);
  // QA分类删除
  rpc DeleteQACate(DeleteQACateReq) returns (DeleteQACateRsp);
  // Deprecated 获取QA列表
  rpc GetQAList(GetQAListReq) returns (GetQAListRsp);
  // 获取QA列表
  rpc ListQA(ListQAReq) returns (ListQARsp);
  // Deprecated 获取QA详情
  rpc GetQADetail(GetQADetailReq) returns (GetQADetailRsp);
  // 获取QA详情
  rpc DescribeQA(DescribeQAReq) returns (DescribeQARsp);
  // 新建QA
  rpc CreateQA(CreateQAReq) returns (CreateQARsp);
  // 新建QA
  rpc CreateQAV1(CreateQAV1Req) returns (CreateQAV1Rsp);
  // Deprecated 编辑QA
  rpc UpdateQA(UpdateQAReq) returns (UpdateQARsp);
  // 编辑QA
  rpc ModifyQA(ModifyQAReq) returns (ModifyQARsp);
  // 删除QA
  rpc DeleteQA(DeleteQAReq) returns (DeleteQARsp);
  // 删除QA
  rpc DeleteQAV1(DeleteQAV1Req) returns (DeleteQAV1Rsp);
  // 校验QA
  rpc VerifyQA(VerifyQAReq) returns (VerifyQARsp);
  // 校验QA
  rpc VerifyQAV1(VerifyQAV1Req) returns (VerifyQAV1Rsp);
  // QA分组
  rpc GroupQA(GroupQAReq) returns (GroupQARsp);
  // 编辑QA适用范围
  rpc ModifyQAAttrRange(ModifyQAAttrRangeReq) returns (ModifyQAAttrRangeRsp);
  // 导出QA列表
  rpc ExportQAList(ExportQAListReq) returns (ExportQAListRsp);
  // Deprecated 导出QA列表
  rpc ExportQAListV1(ExportQAListReqV1) returns (ExportQAListRspV1);
  // Deprecated 拉取发布按钮状态、最后发布时间
  rpc GetReleaseInfo(GetReleaseInfoReq) returns (GetReleaseInfoRsp);
  // 拉取发布按钮状态、最后发布时间
  rpc DescribeReleaseInfo(DescribeReleaseInfoReq) returns (DescribeReleaseInfoRsp);
  // 发布文档预览
  rpc ListReleaseDocPreview(ListReleaseDocPreviewReq) returns (ListReleaseDocPreviewRsp);
  // 发布问答预览
  rpc ListReleaseQAPreview(ListReleaseQAPreviewReq) returns (ListReleaseQAPreviewRsp);
  // 发布拒答问题预览
  rpc ListRejectedQuestionPreview(ListRejectedQuestionPreviewReq) returns (ListRejectedQuestionPreviewRsp);
  // 待发布配置预览
  rpc ListReleaseConfigPreview(ListReleaseConfigPreviewReq) returns (ListReleaseConfigPreviewRsp);
  // Deprecated 发布文档预览
  rpc GetReleaseDocPreview(GetReleaseDocPreviewReq) returns (GetReleaseDocPreviewRsp);
  // Deprecated 发布问答预览
  rpc GetReleaseQAPreview(GetReleaseQAPreviewReq) returns (GetReleaseQAPreviewRsp);
  // Deprecated 发布拒答问题预览
  rpc PreviewRejectedQuestion(PreviewRejectedQuestionReq) returns (PreviewRejectedQuestionRsp);
  // 新增发布任务
  rpc CreateRelease(CreateReleaseReq) returns (CreateReleaseRsp);
  // 发布记录列表
  rpc ListRelease(ListReleaseReq) returns (ListReleaseRsp);
  // Deprecated 发布记录列表
  rpc GetReleaseRecordList(GetReleaseRecordListReq) returns (GetReleaseRecordListRsp);
  // 是否存在未确认问答
  rpc CheckUnconfirmedQa(CheckUnconfirmedQaReq) returns (CheckUnconfirmedQaRsp);
  // 查询发布任务
  rpc DescribeRelease(DescribeReleaseReq) returns (DescribeReleaseRsp);
  // 发布暂停之后再次重新发布
  rpc RetryRelease(RetryReleaseReq) returns (RetryReleaseRsp);
  // Deprecated 拉取相似问答对
  rpc GetQaSimilar(GetQaSimilarReq) returns (GetQaSimilarRsp);
  // 拉取相似问答对
  rpc ListQaSimilar(ListQaSimilarReq) returns (ListQaSimilarRsp);
  // Deprecated 拉取相似问答对详情
  rpc GetQaSimilarDetail(GetQaSimilarDetailReq) returns (GetQaSimilarDetailRsp);
  // 拉取相似问答对详情
  rpc DescribeQaSimilar(DescribeQaSimilarReq) returns (DescribeQaSimilarRsp);
  // 提交相似问答选择结果
  rpc SubmitQaSimilar(SubmitQaSimilarReq) returns (SubmitQaSimilarRsp);
  // 获取最新的消息
  rpc ListNotify(ListNotifyReq) returns (ListNotifyRsp);
  // Deprecated 获取最新的消息
  rpc GetNotify(GetNotifyReq) returns (GetNotifyRsp);
  // Deprecated 获取历史消息
  rpc GetHistoryNotify(GetHistoryNotifyReq) returns (GetHistoryNotifyRsp);
  // 获取历史消息
  rpc ListHistoryNotify(ListHistoryNotifyReq) returns (ListHistoryNotifyRsp);
  // 通知已读
  rpc ReadNotify(ReadNotifyReq) returns (ReadNotifyRsp);
  // 通知关闭
  rpc CloseNotify(CloseNotifyReq) returns (CloseNotifyRsp);
  // Deprecated 获取来源详情
  rpc GetReferDetail(GetReferDetailReq) returns (GetReferDetailRsp);
  // 获取来源详情
  rpc DescribeRefer(DescribeReferReq) returns (DescribeReferRsp);
  // 来源打标
  rpc MarkRefer(MarkReferReq) returns (MarkReferRsp);
  // Deprecated 上传样本集合
  rpc UploadSampleFile(UploadSampleReq) returns (UploadSampleRsp);
  // 上传样本集合
  rpc UploadSampleSet(UploadSampleSetReq) returns (UploadSampleSetRsp);
  // 带校验上传样本集合
  rpc UploadSampleSetWithCheck(UploadSampleSetWithCheckReq) returns (UploadSampleSetWithCheckRsp);
  // Deprecated 查询样本集列表
  rpc QuerySampleSetList(QuerySampleReq) returns (QuerySampleRsp);
  // Deprecated 批量删除样本集
  rpc DeleteSampleFiles(DeleteSampleReq) returns (DeleteSampleRsp);
  // 查询样本集
  rpc ListSampleSet(ListSampleSetReq) returns (ListSampleSetRsp);
  // 删除样本集
  rpc DeleteSampleSet(DeleteSampleSetReq) returns (DeleteSampleSetRsp);
  // Deprecated 创建评测任务
  rpc CreateTest(CreateTestReq) returns (CreateTestRsp);
  // 创建评测任务
  rpc CreateEvaluateTest(CreateEvaluateTestReq) returns (CreateEvaluateTestRsp);
  // Deprecated 条件查询任务列表
  rpc QueryTestList(QueryTestReq) returns (QueryTestRsp);
  // 查询任务列表
  rpc ListEvaluateTest(ListEvaluateTestReq) returns (ListEvaluateTestRsp);
  // Deprecated 任务删除
  rpc DeleteTest(DeleteTestReq) returns (DeleteTestRsp);
  // Deprecated 任务停止
  rpc StopTest(StopTestReq) returns (StopTestRsp);
  // Deprecated 任务重试
  rpc RetryTest(RetryTestReq) returns (RetryTestRsp);
  // 任务删除
  rpc DeleteEvaluateTest(DeleteEvaluateTestReq) returns (DeleteEvaluateTestRsp);
  // 任务停止
  rpc StopEvaluateTest(StopEvaluateTestReq) returns (StopEvaluateTestRsp);
  // 任务重试
  rpc RetryEvaluateTest(RetryEvaluateTestReq) returns (RetryEvaluateTestRsp);
  // Deprecated 待标注测试记录详情
  rpc GetOneWaitJudging(GetOneJudgingReq) returns (GetOneJudgingRsp);
  // 待标注测试记录详情
  rpc DescribeWaitJudgeRecord(DescribeWaitJudgeRecordReq) returns (DescribeWaitJudgeRecordRsp);
  // Deprecated 查询标注记录详情
  rpc GetRecord(GetRecordReq) returns (GetRecordRsp);
  // 查询标注记录详情
  rpc DescribeRecord(DescribeRecordReq) returns (DescribeRecordRsp);
  // 标注会话
  rpc JudgeRecord(JudgeReq) returns (JudgeRsp);
  // Deprecated 获取拒答问题列表
  rpc GetRejectedQuestionList(GetRejectedQuestionListReq) returns (GetRejectedQuestionListRsp);
  // 获取拒答问题列表
  rpc ListRejectedQuestion(ListRejectedQuestionReq) returns (ListRejectedQuestionRsp);
  // 创建拒答问题
  rpc CreateRejectedQuestion(CreateRejectedQuestionReq) returns (CreateRejectedQuestionRsp);
  // Deprecated 修改拒答问题
  rpc UpdateRejectedQuestion(UpdateRejectedQuestionReq) returns (UpdateRejectedQuestionRsp);
  // 修改拒答问题
  rpc ModifyRejectedQuestion(ModifyRejectedQuestionReq) returns (ModifyRejectedQuestionRsp);
  // 删除拒答问题
  rpc DeleteRejectedQuestion(DeleteRejectedQuestionReq) returns (DeleteRejectedQuestionRsp);
  // 导出拒答问题
  rpc ExportRejectedQuestion(ExportRejectedQuestionReq) returns (ExportRejectedQuestionRsp);
  // Deprecated 获取不满意回复
  rpc GetUnsatisfiedReply(GetUnsatisfiedReplyReq) returns (GetUnsatisfiedReplyRsp);
  // 获取不满意回复
  rpc ListUnsatisfiedReply(ListUnsatisfiedReplyReq) returns (ListUnsatisfiedReplyRsp);
  // 忽略不满意回复
  rpc IgnoreUnsatisfiedReply(IgnoreUnsatisfiedReplyReq) returns (IgnoreUnsatisfiedReplyRsp);
  // 导出不满意回复
  rpc ExportUnsatisfiedReply(ExportUnsatisfiedReplyReq) returns (ExportUnsatisfiedReplyRsp);
  // Deprecated 获取不满意回复上下文
  rpc GetUnsatisfiedReplyContext(GetUnsatisfiedReplyContextReq) returns (GetUnsatisfiedReplyContextRsp);
  // 获取不满意回复上下文
  rpc DescribeUnsatisfiedReplyContext(DescribeUnsatisfiedReplyReq) returns (DescribeUnsatisfiedReplyRsp);
  // 记录操作首次生成问答标记
  rpc RecordUserFirstGenQA(RecordUserFirstGenQAReq) returns (RecordUserFirstGenQARsp);
  // 记录访问未检验问答时间
  rpc RecordUserAccessUnCheckQATime(RecordUserAccessUnCheckQATimeReq) returns (RecordUserAccessUnCheckQATimeRsp);
  // 创建属性标签
  rpc CreateAttributeLabelV1(CreateAttributeLabelV1Req) returns (CreateAttributeLabelV1Rsp);
  // 创建属性标签
  rpc CreateAttributeLabel(CreateAttributeLabelReq) returns (CreateAttributeLabelRsp);
  // 删除属性标签
  rpc DeleteAttributeLabel(DeleteAttributeLabelReq) returns (DeleteAttributeLabelRsp);
  // Deprecated 编辑属性标签
  rpc UpdateAttributeLabel(UpdateAttributeLabelReq) returns (UpdateAttributeLabelRsp);
  // 编辑属性标签
  rpc ModifyAttributeLabel(ModifyAttributeLabelReq) returns (ModifyAttributeLabelRsp);
  // Deprecated 查询属性标签列表
  rpc GetAttributeLabelList(GetAttributeLabelListReq) returns (GetAttributeLabelListRsp);
  // 查询属性标签列表
  rpc ListAttributeLabel(ListAttributeLabelReq) returns (ListAttributeLabelRsp);
  // Deprecated 查询属性标签详情
  rpc GetAttributeLabelDetail(GetAttributeLabelDetailReq) returns (GetAttributeLabelDetailRsp);
  // 查询属性标签详情
  rpc DescribeAttributeLabel(DescribeAttributeLabelReq) returns (DescribeAttributeLabelRsp);
  // 导入属性标签
  rpc UploadAttributeLabel(UploadAttributeLabelReq) returns (UploadAttributeLabelRsp);
  // 导出属性标签
  rpc ExportAttributeLabel(ExportAttributeLabelReq) returns (ExportAttributeLabelRsp);
  // 检查属性下标签是否引用
  rpc CheckAttributeLabelRefer(CheckAttributeLabelReferReq) returns (CheckAttributeLabelReferRsp);
  // 检查属性下的标签名是否存在请求
  rpc CheckAttributeLabelExist(CheckAttributeLabelExistReq) returns (CheckAttributeLabelExistRsp);
  // 提交申诉请求申请人工审核
  rpc CreateAppeal(CreateAppealReq) returns (CreateAppealRsp);

  // 应用
  // 创建应用
  rpc CreateApp(CreateAppReq) returns (CreateAppRsp);
  // 修改应用
  rpc ModifyApp(ModifyAppReq) returns (ModifyAppRsp);
  // 获取企业下应用列表
  rpc ListApp(ListAppReq) returns (ListAppRsp);
  // 获取企业下应用详情
  rpc DescribeApp(DescribeAppReq) returns (DescribeAppRsp);
  // 删除应用
  rpc DeleteApp(DeleteAppReq) returns (DeleteAppRsp);
  // 获取模型列表
  rpc ListModel(ListModelReq) returns (ListModelRsp);
  // 应用类型列表
  rpc ListAppCategory(ListAppCategoryReq) returns (ListAppCategoryRsp);
  // 获取应用密钥
  rpc GetAppSecret(GetAppSecretReq) returns (GetAppSecretRsp);
  // 获取知识库知识个数
  rpc GetAppKnowledgeCount(GetAppKnowledgeCountReq) returns (GetAppKnowledgeCountRsp);
  // 获取应用License
  rpc DescribeLicense(DescribeLicenseReq) returns (DescribeLicenseRsp);
  // 获取任务状态
  rpc GetTaskStatus(GetTaskStatusReq) returns (GetTaskStatusRsp);
  // 模型详情
  rpc DescribeModel(DescribeModelReq) returns (DescribeModelRsp);
  // 标签列表
  rpc ListClassifyLabel(ListClassifyLabelReq) returns (ListClassifyLabelRsp);
  // 摘要Prompt列表
  rpc ListSummaryPrompt(ListSummaryPromptReq) returns (ListSummaryPromptRsp);


  // 添加Agent反馈
  rpc AddAgentFeedback(AddAgentFeedbackReq) returns (AddAgentFeedbackRsp);
  // 获取Agent详情
  rpc DescribeAgentFeedback(DescribeAgentFeedbackReq) returns (DescribeAgentFeedbackRsp);
  // 删除Agent反馈
  rpc DeleteAgentFeedback(DeleteAgentFeedbackReq) returns (DeleteAgentFeedbackRsp);
  // 查询Agent反馈信息列表
  rpc ListAgentFeedback(ListAgentFeedbackReq) returns (ListAgentFeedbackRsp);

  // 添加反馈
  rpc AddFeedback(AddFeedbackReq) returns (AddFeedbackRsp);
  // 查询反馈信息列表
  rpc ListFeedback(ListFeedbackReq) returns (ListFeedbackRsp);
  // 查询反馈信息详情
  rpc DescribeFeedback(DescribeFeedbackReq) returns (DescribeFeedbackRsp);
  // 删除反馈记录详情
  rpc DeleteFeedback(DeleteFeedbackReq) returns (DeleteFeedbackRsp);

  // ListExperienceApp 体验中心-获取体验应用列表
  rpc ListExperienceCenterApp(ListExperienceCenterAppReq) returns (ListExperienceCenterAppRsp);
  // CreateAppByExperienceApp 基于体验应用，创建app
  rpc CreateAppByExperienceApp(CreateAppByExperienceAppReq) returns (CreateAppByExperienceAppRsp);
  // 获取体验应用对外显示的文档列表
  rpc GetDisplayDocs(GetDisplayDocsReq) returns (GetDisplayDocsRsp);
  // 资源包列表
  rpc ListPackage(ListPackageReq) returns (ListPackageRsp);
  // 并发扩展
  rpc ListConcurrency(ListConcurrencyReq) returns (ListConcurrencyRsp);
  // 知识库容量扩展
  rpc ListKnowledgeCapacity(ListKnowledgeCapacityReq) returns (ListKnowledgeCapacityRsp);
  // 接口调用token
  rpc DescribeTokenUsage(DescribeTokenUsageReq) returns (DescribeTokenUsageRsp);
  // 接口调用token折线图
  rpc DescribeTokenUsageGraph(DescribeTokenUsageGraphReq) returns (DescribeTokenUsageGraphRsp);
  // 获取产品列表
  rpc ListProducts(ListProductReq) returns (ListProductRsp);
  // 获取账户列表
  rpc ListAccount(ListAccountReq) returns (ListAccountRsp);
  // 导出计费详情
  rpc ExportBillingInfo(ExportBillingInfoReq) returns (ExportBillingInfoRsp);
  // 接口调用折线图
  rpc DescribeCallStatsGraph(DescribeCallStatsGraphReq) returns (DescribeCallStatsGraphRsp);
  // 获取搜索引擎资源状态
  rpc GetSearchResourceStatus(GetSearchResourceStatusReq) returns (GetSearchResourceStatusRsp);
  // 获取后付费产品列表
  rpc ListPostpaidProduct(ListPostpaidProductReq) returns (ListPostpaidProductRsp);
  // 修改后付费开关状态
  rpc ModifyPostpaidSwitch(ModifyPostpaidSwitchReq) returns (ModifyPostpaidSwitchRsp);
  // 查询账户未生效资源包
  rpc ListInvalidResource(ListInvalidResourceReq) returns (ListInvalidResourceRsp);
  // 创建单个后付费资源
  rpc CreatePostPayResource(CreatePostPayResourceReq) returns (CreatePostPayResourceRsp);
  // 查询后付费开关状态
  rpc DescribePostpaidSwitch(DescribePostpaidSwitchReq) returns (DescribePostpaidSwitchRsp);
  // 查询搜索服务调用折线图
  rpc DescribeSearchStatsGraph(DescribeSearchStatsGraphReq) returns (DescribeSearchStatsGraphRsp);
  // 统计上报-事件上报
  rpc AddEventReport(AddEventReportReq) returns (AddEventReportRsp);
  // 统计上报-批量事件上报
  rpc BatchEventReport(BatchEventReportReq) returns (BatchEventReportRsp);
  // 知识库容量统计
  rpc DescribeKnowledgeUsage(DescribeKnowledgeUsageReq) returns (DescribeKnowledgeUsageRsp);
  // 知识库容量统计饼图
  rpc DescribeKnowledgeUsagePieGraph(DescribeKnowledgeUsagePieGraphReq) returns (DescribeKnowledgeUsagePieGraphRsp);
  // 获取应用知识库调用明细列表
  rpc ListAppKnowledgeDetail(ListAppKnowledgeDetailReq) returns (ListAppKnowledgeDetailRsp);
  // 获取单次调用详情
  rpc ListUsageCallDetail(ListUsageCallDetailReq) returns (ListUsageCallDetailRsp);
  // 并发调用
  rpc DescribeConcurrencyUsage(DescribeConcurrencyUsageReq) returns (DescribeConcurrencyUsageRsp);
  // 并发调用明细
  rpc ListConcurrencyDetail(ListConcurrencyDetailReq) returns (ListConcurrencyDetailRsp);
  // 并发调用折线图
  rpc DescribeConcurrencyUsageGraph(DescribeConcurrencyUsageGraphReq) returns (DescribeConcurrencyUsageGraphRsp);
  // 导出并发详情
  rpc ExportConcurrencyInfo(ExportConcurrencyInfoReq) returns (ExportConcurrencyInfoRsp);
  // 导出知识库容量详情
  rpc ExportKnowledgeInfo(ExportKnowledgeInfoReq) returns (ExportKnowledgeInfoRsp);
  // 发布标签预览请求
  rpc ListReleaseLabelPreview(ListReleaseLabelPreviewReq) returns (ListReleaseLabelPreviewRsp);
  // 发布同义词预览请求
  rpc ListReleaseSynonymsPreview(ListReleaseSynonymsPreviewReq) returns (ListReleaseSynonymsPreviewRsp);
  // 修改应用基础信息
  rpc ModifyAppBase(ModifyAppBaseReq) returns (ModifyAppBaseRsp);
  // 创建自定义模型
  rpc CreateCustomModel(CreateCustomModelReq) returns (CreateCustomModelRsp);
  // 编辑自定义模型
  rpc ModifyCustomModel(ModifyCustomModelReq) returns (ModifyCustomModelRsp);
  // 删除自定义模型 
  rpc DeleteCustomModel(DeleteCustomModelReq) returns (DeleteCustomModelRsp);
  // 查询自定义模型详情
  rpc DescribeCustomModel(DescribeCustomModelReq) returns(DescribeCustomModelRsp);
  // 获取模型关联的应用
  rpc DescribeModelApps(DescribeModelAppsReq) returns(DescribeModelAppsRsp);

  // 查看用户引导是否观看信息
  rpc GetUserGuideViewInfos(GetUserGuideViewInfosReq) returns (GetUserGuideViewInfosRsp);
  // 设置用户引导已观看
  rpc SetUserGuideViewInfo(SetUserGuideViewInfoReq) returns (SetUserGuideViewInfoRsp);

  // 导出调用统计并发QPM、TPM分钟用量信息
  rpc ExportMinuteDosage(ExportMinuteDosageReq) returns (ExportMinuteDosageRsp);

  // 图片预览请求,私有读
  rpc GetImagePreview(GetImagePreviewReq) returns(GetImagePreviewRsp);
  // 查询音色列表
  rpc ListVoice(ListVoiceReq) returns (ListVoiceRsp);
  // 查询数智人列表
  rpc ListDigitalHuman(ListDigitalHumanReq) returns(ListDigitalHumanRsp);

  // GetPromptTemplateList 模板中心列表
  rpc GetPromptTemplateList(GetPromptTemplateListReq) returns(GetPromptTemplateListRsp);

  // 获取消息日志列表
  rpc GetMsgLogList(GetMsgLogListReq) returns (GetMsgLogListRsp);

  // 导出消息日志
  rpc ExportMsgLog(ExportMsgLogReq) returns (ExportMsgLogRsp);

  // 修改并发规则
  rpc ModifyConcurrencyRule(ModifyConcurrencyRuleReq) returns(ModifyConcurrencyRuleRsp);
  // 查看并发规则
  rpc DescribeConcurrencyRule(DescribeConcurrencyRuleReq)returns (DescribeConcurrencyRuleRsp);

  // 检查是否命中白名单
  rpc CheckWhitelist(CheckWhitelistReq) returns (CheckWhitelistRsp);

  // 获取消息日志概览
  rpc GetMsgLogOverview(GetMsgLogOverviewReq) returns (GetMsgLogOverviewRsp);
  // 获取消息数趋势
  rpc GetMsgLogCountTrend(GetMsgLogCountTrendReq) returns (GetMsgLogCountTrendRsp);
  // 获取消息互动用户数趋势
  rpc GetMsgLogUserCountTrend(GetMsgLogUserCountTrendReq) returns (GetMsgLogUserCountTrendRsp);
  // 获取消息数反馈数趋势
  rpc GetMsgLogFeedbackCountTrend(GetMsgLogFeedbackCountTrendReq) returns (GetMsgLogFeedbackCountTrendRsp);
  // 导出消息统计
  rpc ExportMsgLogStatistical(ExportMsgLogStatisticalReq) returns (ExportMsgLogStatisticalRsp);

  // 创建审批单
  rpc CreateApproval(CreateApprovalReq) returns(CreateApprovalRsp);
  // 更新审批单状态
  rpc UpdateApprovalStatus(UpdateApprovalStatusReq) returns(UpdateApprovalStatusRsp);
  // 获取最新一条审批
  rpc GetLastApproval(GetLastApprovalReq) returns(GetLastApprovalRsp);

  // ListChannel 渠道列表
  rpc ListChannel(ListChannelReq) returns (ListChannelRsp);
  // ChannelInfo 渠道详情
  rpc DescribeChannelInfo(DescribeChannelInfoReq) returns (DescribeChannelInfoRsp);
  // UpdateChannelStatus 渠道状态变更 上线 下线
  rpc UpdateChannelStatus(UpdateChannelStatusReq) returns (UpdateChannelStatusRsp);
  // UnbindChannel 解绑
  rpc UnbindChannel(UnbindChannelReq) returns (UnbindChannelRsp);
  // CreateChannel 创建渠道
  rpc CreateChannel(CreateChannelReq) returns (CreateChannelRsp);
  // WecomDefaultInfo 企微应用渠道拉取默认回调配置
  rpc WecomDefaultInfo(WecomDefaultInfoReq) returns (WecomDefaultInfoRsp);
  // UpdateChannel 编辑渠道 当前只支持备注字段更新 后续可能会追加
  rpc UpdateChannel(UpdateChannelReq) returns (UpdateChannelRsp);
}


// 获取对象存储临时密钥请求
message DescribeStorageCredentialReq {
  // 机器人ID
  uint64 bot_biz_id = 1;
  // 文件类型
  string file_type = 2;
  // 区分场景，是否公有场景
  bool is_public = 3;
  // 区分类型: offline:离线文件，realtime:实时文件；为空默认为offline
  string type_key = 4;
}

// 获取对象存储临时密钥响应
message DescribeStorageCredentialRsp {
  message Credentials {
    // token
    string token = 1;
    // 临时证书密钥 ID
    string tmp_secret_id = 2;
    // 临时证书密钥 Key
    string tmp_secret_key = 3;
  }
  // 密钥信息
  Credentials credentials = 1;
  // 失效时间
  uint32 expired_time = 2;
  // 起始时间
  uint32 start_time = 3;
  // 对象存储 桶
  string bucket = 4;
  // 对象存储 可用区
  string region = 5;
  // 文件目录
  string file_path = 6;
  // 主号
  uint64 corp_uin = 7;
  // 存储类型
  string type = 8;
  // 图片目录
  string image_path = 9;
  // 上传路径
  string upload_path = 10;
}

// 获取对象存储临时密钥请求
message GetCredentialReq {}

// 获取对象存储临时密钥响应
message GetCredentialRsp {
  message Credentials {
    // token
    string token = 1;
    // 临时证书密钥 ID
    string tmp_secret_id = 2;
    // 临时证书密钥 Key
    string tmp_secret_key = 3;
  }
  // 密钥信息
  Credentials credentials = 1;
  // 失效时间
  uint32 expired_time = 2;
  // 起始时间
  uint32 start_time = 3;
  // 对象存储 桶
  string bucket = 4;
  // 对象存储 可用区
  string region = 5;
  // 目录
  string file_path = 6;
  // 主号
  uint64 corp_uin = 7;
  // 存储类型
  string type = 8;
}

// 机器人信息请求
message DescribeRobotReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 机器人信息响应
message DescribeRobotRsp {
  // 机器人ID
  uint64 bot_biz_id = 1;
  // 机器人昵称
  string name = 2;
  // 机器人头像
  string avatar = 3;
  // 是否启用转人工
  bool can_transfer_keyword = 4;
  // 转人工关键词
  repeated string transfer_keywords = 5;
  // 是否使用行业通用知识库
  bool use_general_knowledge = 6;
  // 未知问题回复语
  string bare_answer = 7;
  // 机器人昵称是否在审核中
  bool is_name_in_audit = 8;
  // 未知问题回复是否在审核中
  bool is_bare_answer_in_audit = 9;
  // 是否开启搜索增强
  bool use_search_engine = 10;
  // 头像是否在审核中
  bool is_avatar_in_audit = 11;
  // 欢迎语是否在审核中
  bool is_greeting_in_audit = 12;
  // 是否开启根据用户语义转人工
  bool can_transfer_intent = 13;
  // 是否开启机器人答案评价不满意转人工
  bool can_transfer_unsatisfied = 14;
  // 机器人答案评价不满意转人工，设定的触发次数
  uint32 transfer_unsatisfied_count = 15;
  // 机器人描述(prompt 场景使用)
  string role_description = 17;
  // 欢迎语
  string greeting = 18;
  // 机器人回复灵活度
  uint32 reply_flexibility = 19;
  // app_key
  string app_key = 20;
  // 是否开启搜索增强
  bool show_search_engine = 21;
}

// 获取审核开关 请求
message GetAuditSwitchReq {}

// 获取审核开关 响应
message GetAuditSwitchRsp {
  bool switch = 1;
}

// 获取审核开关 请求
message DescribeAuditSwitchReq {}

// 获取审核开关 响应
message DescribeAuditSwitchRsp {
  bool switch = 1;
}

// 员工列表 请求
message ListCorpStaffReq {
  // 机器人ID
  uint64 bot_biz_id = 1;
  // 查询内容
  string query = 2;
  // 页码
  uint32 page = 3;
  // 分页数量
  uint32 page_size = 4;
  // 页码
  uint32 page_number = 5;
}

// 员工列表 响应
message ListCorpStaffRsp {
  message Staff {
    uint64 staff_biz_id = 1;
    string nick_name = 2;
    string telephone = 3;
    uint32 status = 4;
    string status_desc = 5;
    int64 join_time = 7;
    int64 create_time = 8;
    string avatar = 9;
  }
  uint64 total = 1;
  repeated Staff list = 2;
}

// 企业消息 请求
message CorpInfoReq {}

// 企业消息 响应
message CorpInfoRsp {
  // 企业ID
  uint64 corp_biz_id = 1;
  // 机器人配额
  uint32 robot_quota = 2;
  // 企业全称
  string full_name = 3;
}

// 企业消息 请求
message DescribeCorpReq {}

// 企业消息 响应
message DescribeCorpRsp {
  // 企业ID
  uint64 corp_biz_id = 1;
  // 机器人配额
  uint32 robot_quota = 2;
  // 企业全称
  string full_name = 3;
  // 是否试用
  bool is_trial = 4;
  // 是否试用到期
  bool is_trial_expired = 5;
  // 可用App的数量
  uint32 available_app_quota = 6;
  // 是否支持自定义模型
  bool is_support_custom_model = 7;
}

// 文档列表请求
message ListDocReq {
  // 查询内容
  string query = 1;
  // 页码
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 4 [(validate.rules).uint64.gt = 0];
  // 发布状态:7 审核中、8 审核失败、10 待发布、11 发布中、12 已发布、13 学习中、14 学习失败 17 已过期
  repeated uint32 status = 5;
}

// Deprecated 属性标签详情信息
message AttrLabelV1 {
  message Label {
    // 标签ID
    uint64 label_id = 1;
    // 标签名称
    string label_name = 2;
  }
  // 属性标签来源，1：属性标签
  uint32 source = 1;
  // 属性ID
  uint64 attr_id = 2;
  // 属性标识
  string attr_key = 3;
  // 属性名称
  string attr_name = 4;
  // 标签ID
  repeated Label labels = 5;
}

// 属性标签详情信息
message AttrLabel {
  message Label {
    // 标签ID
    uint64 label_biz_id = 1;
    // 标签名称
    string label_name = 2;
  }
  // 属性标签来源，1：属性标签
  uint32 source = 1;
  // 属性ID
  uint64 attr_biz_id = 2;
  // 属性标识
  string attr_key = 3;
  // 属性名称
  string attr_name = 4;
  // 标签ID
  repeated Label labels = 5;
}

// 属性标签引用信息
message AttrLabelReferV1 {
  // 属性标签来源，1：属性标签
  uint32 source = 1 [(validate.rules).uint32.gt = 0];
  // 属性ID
  uint64 attr_id = 2 [(validate.rules).uint64.gt = 0];
  ;
  // 标签ID
  repeated uint64 label_ids = 3 [(validate.rules).repeated .min_items = 1];
}

// 属性标签引用信息
message AttrLabelRefer {
  // 属性标签来源，1：属性标签
  uint32 source = 1 [(validate.rules).uint32.gt = 0];
  // 属性ID
  uint64 attribute_biz_id = 2 [(validate.rules).uint64.gt = 0];
  ;
  // 标签ID
  repeated uint64 label_biz_ids = 3 [(validate.rules).repeated .min_items = 1];
}

// 文档列表响应
message ListDocRsp {
  message Doc {
    // 文档ID
    uint64 doc_biz_id = 1;
    // 文件名
    string file_name = 2;
    // cos路径
    string cos_url = 3;
    // 更新时间
    int64 update_time = 4;
    // 状态值
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 文件类型
    string file_type = 7;
    // 生成失败原因
    string reason = 8;
    // 答案中是否引用
    bool is_refer = 9;
    // qa对数量
    uint32 qa_num = 10;
    // 是否删除
    bool is_deleted = 11;
    // 文档来源
    uint32 source = 12;
    // 来源描述
    string source_desc = 13;
    // 是否允许重新生成
    bool is_allow_restart = 14;
    // qa是否已删除
    bool is_deleted_qa = 15;
    // 问答是否生成中
    bool is_creating_qa = 16;
    // 是否允许删除
    bool is_allow_delete = 17;
    // 是否允许操作引用开关
    bool is_allow_refer = 18;
    // 问答是否生成过
    bool is_created_qa = 19;
    // 文档字符量
    uint64 doc_char_size = 20;
    // 是否允许编辑
    bool is_allow_edit = 21;
    // 属性标签适用范围 1：全部，2：按条件范围
    uint32 attr_range = 22;
    // 属性标签
    repeated AttrLabel attr_labels = 23;
    // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
    uint32 refer_url_type = 24;
    // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
    string web_url = 25;
    // 知识库有效开始时间，unix时间戳
    uint64 expire_start = 26;
    // 知识库有效结束时间，unix时间戳，0代表永久有效
    uint64 expire_end = 27;
    // 允许重试 0:否 1:是
    bool is_allow_retry = 28;
  }
  uint64 total = 1;
  repeated Doc list = 2;
}

// 文档列表请求
message ListDocV1Req {
  // 查询内容
  string query = 1;
  // 页码
  uint32 page = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 4 [(validate.rules).uint64.gt = 0];
  // 发布状态:7 审核中、8 审核失败、10 待发布、11 发布中、12 已发布、13 学习中、14 学习失败 17 已过期
  repeated uint32 status = 5;
}

// 文档列表响应
message ListDocV1Rsp {
  message Doc {
    // 文档ID
    uint64 id = 1;
    // 文件名
    string file_name = 2;
    // cos路径
    string cos_url = 3;
    // 更新时间
    int64 update_time = 4;
    // 状态值
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 文件类型
    string file_type = 7;
    // 生成失败原因
    string reason = 8;
    // 答案中是否引用
    bool is_refer = 9;
    // qa对数量
    uint32 qa_num = 10;
    // 是否删除
    bool is_deleted = 11;
    // 文档来源
    uint32 source = 12;
    // 来源描述
    string source_desc = 13;
    // 是否允许重新生成
    bool is_allow_restart = 14;
    // qa是否已删除
    bool is_deleted_qa = 15;
    // 问答是否生成中
    bool is_creating_qa = 16;
    // 是否允许删除
    bool is_allow_delete = 17;
    // 是否允许操作引用开关
    bool is_allow_refer = 18;
    // 问答是否生成过
    bool is_created_qa = 19;
    // 文档字符量
    uint64 doc_char_size = 20;
    // 是否允许编辑
    bool is_allow_edit = 21;
    // 属性标签适用范围 1：全部，2：按条件范围
    uint32 attr_range = 22;
    // 属性标签
    repeated AttrLabelV1 attr_labels = 23;
    // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
    uint32 refer_url_type = 24;
    // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
    string web_url = 25;
    // 知识库有效开始时间，unix时间戳
    uint64 expire_start = 26;
    // 知识库有效结束时间，unix时间戳，0代表永久有效
    uint64 expire_end = 27;
    // 允许重试 0:否 1:是
    bool is_allow_retry = 28;
  }
  uint64 total = 1;
  repeated Doc docs = 2;
}

// 保存文档请求
message SaveDocReq {
  // 文件名
  string file_name = 1 [(validate.rules).string = {min_len: 1, max_len: 255}];
  // cos路径
  string cos_url = 2 [(validate.rules).string.min_len = 1];
  // 文件类型
  string file_type = 3 [(validate.rules).string.min_len = 1];
  // 机器人ID
  uint64 bot_biz_id = 4 [(validate.rules).uint64.gt = 0];
  // ETag 全称为 Entity Tag，是对象被创建时标识对象内容的信息标签，可用于检查对象的内容是否发生变化
  string e_tag = 5 [(validate.rules).string.min_len = 1];
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 6 [(validate.rules).string.min_len = 1];
  // 文件大小
  uint64 size = 7 [(validate.rules).uint64.gt = 0];
  // 是否开始生成QA
  bool is_start_create = 8;
  // 来源(0 源文件导入 1 网页导入)
  uint32 source = 9;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 10 [(validate.rules).string.max_len = 2000];
  // 是否引用链接
  bool is_refer = 11;
  // 属性标签适用范围 1：全部，2：按条件范围
  uint32 attr_range = 12 [(validate.rules).uint32.gt = 0];
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 13;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 14;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 15;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 16;
  // 文档操作类型；1-批量导入；2-文档导入
  uint32 opt = 17;
}

// 保存文档响应
message SaveDocRsp {
  // 文档 ID
  uint64 doc_biz_id = 1;
  // 导入是否错误
  string error_msg = 2;
  // 错误链接
  string error_link = 3;
  // 错误链接文本
  string error_link_text = 4;
}

// 保存文档请求
message SaveDocV1Req {
  // 文件名
  string file_name = 1 [(validate.rules).string = {min_len: 1, max_len: 255}];
  // cos路径
  string cos_url = 2 [(validate.rules).string.min_len = 1];
  // 文件类型
  string file_type = 3 [(validate.rules).string.min_len = 1];
  // 机器人ID
  uint64 bot_biz_id = 4 [(validate.rules).uint64.gt = 0];
  // ETag 全称为 Entity Tag，是对象被创建时标识对象内容的信息标签，可用于检查对象的内容是否发生变化
  string e_tag = 5 [(validate.rules).string.min_len = 1];
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 6 [(validate.rules).string.min_len = 1];
  // 文件大小
  uint64 size = 7 [(validate.rules).uint64.gt = 0];
  // 是否开始生成QA
  bool is_start_create = 8;
  // 来源(0 源文件导入 1 网页导入)
  uint32 source = 9;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 10 [(validate.rules).string.max_len = 2000];
  // 是否引用链接
  bool is_refer = 11;
  // 属性标签适用范围 1：全部，2：按条件范围
  uint32 attr_range = 12 [(validate.rules).uint32.gt = 0];
  // 属性标签引用
  repeated AttrLabelReferV1 attr_labels = 13;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 14;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 15;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 16;
}

// 保存文档响应
message SaveDocV1Rsp {
  // 文档 ID
  uint64 id = 1;
  // 导入是否错误
  string error_msg = 2;
  // 错误链接
  string error_link = 3;
  // 错误链接文本
  string error_link_text = 4;
}

// 修改文档请求
message ModifyDocV1Req {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文档id
  uint64 doc_id = 2;
  // 是否引用链接
  bool is_refer = 3;
  // 属性标签适用范围，1：全部，2：按条件范围
  uint32 attr_range = 4 [(validate.rules).uint32.gt = 0];
  // 属性标签
  repeated AttrLabelReferV1 attr_labels = 5;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 6;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 7 [(validate.rules).string.max_len = 2000];
  // 知识库有效开始时间，unix时间戳,
  uint64 expire_start = 8;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 9;
}

message ModifyDocV1Rsp {}

// 修改文档请求
message ModifyDocReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文档id
  uint64 doc_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 是否引用链接
  bool is_refer = 3;
  // 属性标签适用范围，1：全部，2：按条件范围
  uint32 attr_range = 4 [(validate.rules).uint32.gt = 0];
  // 属性标签
  repeated AttrLabelRefer attr_labels = 5;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 6;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 8 [(validate.rules).string.max_len = 2000];
  // 知识库有效开始时间，unix时间戳,
  uint64 expire_start = 9;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 10;
}

message ModifyDocRsp {}

// 删除文档请求【加校验】
message DeleteDocReq {
  // 文档ID
  repeated uint64 ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 文档业务ID
  repeated uint64 doc_biz_ids = 3;
}

// 删除文档响应
message DeleteDocRsp {}

// 文档详情请求
message DescribeDocReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文档ID
  uint64 doc_biz_id = 2;
}

// 文档详情响应
message DescribeDocRsp {
  // 文档ID
  uint64 doc_biz_id = 1;
  // 文件名
  string file_name = 2;
  // cos路径
  string cos_url = 3;
  // 更新时间
  int64 update_time = 4;
  // 状态值(5审核通过 7审核中 8审核不通过 9审核通过 10待发布 11发布中 12发布成功 13学习中 14学习失败)
  uint32 status = 5;
  // 状态描述
  string status_desc = 6;
  // 文件类型
  string file_type = 7;
  // 生成失败原因
  string reason = 8;
  // 答案中是否引用
  bool is_refer = 9;
  // qa对数量
  uint32 qa_num = 10;
  // 是否删除
  bool is_deleted = 11;
  // 文档来源
  uint32 source = 12;
  // 来源描述
  string source_desc = 13;
  // 是否允许重新生成
  bool is_allow_restart = 14;
  // qa是否已删除
  bool is_deleted_qa = 15;
  // 问答是否生成中
  bool is_creating_qa = 16;
  // 是否允许删除
  bool is_allow_delete = 17;
  // 是否允许操作引用开关
  bool is_allow_refer = 18;
  // 问答是否生成过
  bool is_created_qa = 19;
  // 文档字符量
  uint64 doc_char_size = 20;
  // 是否允许编辑
  bool is_allow_edit = 21;
  // 属性标签适用范围 1：全部，2：按条件范围
  uint32 attr_range = 22;
  // 属性标签
  repeated AttrLabel attr_labels = 23;
}

// 答案中是否引用请求
message ReferDocReq {
  // 文档ID
  uint64 doc_id = 1;
  // 答案中是否引用
  bool is_refer = 2;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
  // 文档业务ID
  uint64 doc_biz_id = 4;
}

// 答案中是否引用响应
message ReferDocRsp {}

// 重新生成QA请求
message StartCreateQAReq {
  // 文档ID
  repeated uint64 ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 生成QA响应
message StartCreateQARsp {}

// 文档预览请求
message GetDocPreviewReq {
  // 文档ID
  uint64 doc_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 文档预览响应
message GetDocPreviewRsp {
  // 文件名
  string file_name = 1;
  // 文件类型
  string file_type = 2;
  // cos路径
  string cos_url = 3;
  // cos临时地址
  string url = 4;
  // cos桶
  string bucket = 5;
}

// 重新生成QA请求
message GenerateQAReq {
  // 文档ID
  repeated uint64 doc_biz_ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 生成QA响应
message GenerateQARsp {}

// 文档下拉列表请求
message GetSelectDocReq {
  // 文档名称
  string file_name = 1;
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 发布状态:7 审核中、8 审核失败、10 待发布、11 发布中、12 已发布、13 学习中、14 学习失败 20 已过期
  repeated uint32 status = 3;
}

// 文档下拉列表响应
message GetSelectDocRsp {
  message Option {
    string text = 1;
    string value = 2;
    uint64 char_size = 3;
    string file_type = 4;
  }
  repeated Option list = 1;
}

// 终止文档解析
message StopDocParseReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文档ID
  uint64 doc_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 终止文档解析响应
message StopDocParseRsp {}

// 重试文档解析
message RetryDocParseReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文档ID
  uint64 doc_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 重试文档解析响应
message RetryDocParseRsp {}

// 重试文档审核
message RetryDocAuditReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文档ID
  uint64 doc_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 重试文档审核响应
message RetryDocAuditRsp {}

message ListSelectDocReq {
  // 文档名称
  string file_name = 1;
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 发布状态:7 审核中、8 审核失败、10 待发布、11 发布中、12 已发布、13 学习中、14 学习失败 20 已过期
  repeated uint32 status = 3;
}

// 文档下拉列表响应
message ListSelectDocRsp {
  message Option {
    string text = 1;
    string value = 2;
    uint64 char_size = 3;
    string file_type = 4;
  }
  repeated Option list = 1;
}

// 新建机器人请求
message CreateRobotReq {
  // 机器人昵称
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 10}];
  // 机器人头像
  string avatar = 2 [(validate.rules).string.min_len = 1];
  // 机器人描述
  string desc = 3;
  // 机器人描述(prompt 场景使用)
  string role_description = 4 [(validate.rules).string = {min_len: 0, max_len: 1000}];
}

// 新建机器人响应
message CreateRobotRsp {
  uint64 bot_biz_id = 1;
}

// 删除机器人请求
message DeleteRobotReq {
  // 机器人编号
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 删除机器人响应
message DeleteRobotRsp {}

// 更新机器人属性请求
message ModifyRobotReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人名称
  string name = 2 [(validate.rules).string = {min_len: 0, max_len: 10}];
  // 机器人头像
  string avatar = 3;
  // 是否启用转人工
  bool can_transfer_keyword = 4;
  // 转人工关键词
  repeated string transfer_keywords = 5 [(validate.rules).repeated = {
    max_items: 20,
    items: {string: {max_len: 10}}
  }];
  // 是否使用行业通用知识库
  bool use_general_knowledge = 6;
  // 未知问题回复语
  string bare_answer = 7;
  // 更新类型(1角色设置 2转人工设置 3对话策略设置)
  uint32 type = 8 [(validate.rules).uint32 = {in: [1, 2, 3]}];
  // 是否开启搜索增强
  bool use_search_engine = 9;
  // 欢迎语
  string greeting = 10 [(validate.rules).string = {min_len: 0, max_len: 200}];
  // 根据用户语义转人工
  bool can_transfer_intent = 11;
  // 机器人答案评价不满意转人工
  bool can_transfer_unsatisfied = 12;
  // 机器人答案评价不满意转人工，设定的触发次数
  uint32 transfer_unsatisfied_count = 13;
  // 机器人描述(prompt 场景使用)
  string role_description = 15 [(validate.rules).string = {min_len: 0, max_len: 1000}];
  // 机器人回复灵活度(1:直接回复,2:润色回复)
  uint32 reply_flexibility = 16;
  // 是否开启搜索增强
  bool show_search_engine = 17;
}

// 更新机器人属性响应
message ModifyRobotRsp {}

// 更新机器人基础属性请求
message ModifyRobotBasicConfigReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人名称
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 10}];
  // 机器人头像
  string avatar = 3;
  // 欢迎语
  string greeting = 4 [(validate.rules).string = {min_len: 0, max_len: 200}];
  // 机器人描述(prompt 场景使用)
  string role_description = 5 [(validate.rules).string = {min_len: 0, max_len: 1000}];
}

// 更新机器人基础属性响应
message ModifyRobotBasicConfigRsp {}

// 更新机器人属性请求
message UpdateRobotReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人名称
  string name = 2 [(validate.rules).string = {max_len: 10}];
  // 机器人头像
  string avatar = 3;
  // 是否启用转人工
  bool can_transfer_keyword = 4;
  // 转人工关键词
  repeated string transfer_keywords = 5 [(validate.rules).repeated = {
    max_items: 20,
    items: {string: {max_len: 10}}
  }];
  // 是否使用行业通用知识库
  bool use_general_knowledge = 6;
  // 未知问题回复语
  string bare_answer = 7;
  // 更新类型
  uint32 type = 8 [(validate.rules).uint32 = {in: [1, 2, 3]}];
  // 是否开启搜索增强
  bool use_search_engine = 9;
  // 欢迎语
  string greeting = 10 [(validate.rules).string = {min_len: 0, max_len: 200}];
  // 根据用户语义转人工
  bool can_transfer_intent = 11;
  // 机器人答案评价不满意转人工
  bool can_transfer_unsatisfied = 12;
  // 机器人答案评价不满意转人工，设定的触发次数
  uint32 transfer_unsatisfied_count = 13;
  // 机器人描述(prompt 场景使用)
  string role_description = 15 [(validate.rules).string = {min_len: 0, max_len: 1000}];
  // 机器人回复灵活度
  uint32 reply_flexibility = 16;
}

// 更新机器人属性响应
message UpdateRobotRsp {}

// 机器人信息请求
message RobotInfoReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 机器人信息响应
message RobotInfoRsp {
  // 机器人ID
  uint64 bot_biz_id = 1;
  // 机器人昵称
  string name = 2;
  // 机器人头像
  string avatar = 3;
  // 是否启用转人工
  bool can_transfer_keyword = 4;
  // 转人工关键词
  repeated string transfer_keywords = 5;
  // 是否使用行业通用知识库
  bool use_general_knowledge = 6;
  // 未知问题回复语
  string bare_answer = 7;
  // 机器人昵称是否在审核中
  bool is_name_in_audit = 8;
  // 未知问题回复是否在审核中
  bool is_bare_answer_in_audit = 9;
  // 是否开启搜索增强
  bool use_search_engine = 10;
  // 头像是否在审核中
  bool is_avatar_in_audit = 11;
  // 欢迎语是否在审核中
  bool is_greeting_in_audit = 12;
  // 是否开启根据用户语义转人工
  bool can_transfer_intent = 13;
  // 是否开启机器人答案评价不满意转人工
  bool can_transfer_unsatisfied = 14;
  // 机器人答案评价不满意转人工，设定的触发次数
  uint32 transfer_unsatisfied_count = 15;
  // 机器人描述(prompt 场景使用)
  string role_description = 17;
  // 欢迎语
  string greeting = 18;
  // 机器人回复灵活度
  uint32 reply_flexibility = 19;
}

// 获取机器人预制角色描述信息
message ListRoleReq {
  // 机器人ID
  uint64 bot_biz_id = 1;
}

message ListRoleRsp {
  message Role {
    // 预制角色描述id
    uint64 id = 1;
    // 角色icon
    string icon = 2;
    // 角色名
    string name = 3;
    // 描述(prompt 场景使用)
    string description = 4;
  }
  repeated Role list = 1;
}

// 获取机器人预制角色描述信息请求
message ListRobotRoleReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 获取机器人预制角色描述信息响应
message ListRobotRoleRsp {
  message Role {
    // 预制角色描述id
    uint64 role_biz_id = 1;
    // 角色icon
    string icon = 2;
    // 角色名
    string name = 3;
    // 描述(prompt 场景使用)
    string description = 4;
  }
  repeated Role list = 1;
}

// 机器人信息请求
message AccountInfoReq {
  // 机器人ID
  uint64 bot_biz_id = 1;
}

// 机器人信息响应
message AccountInfoRsp {
  message Permission {
    string permission_id = 1;
    string permission_name = 2;
  }
  // 员工ID
  uint64 staff_biz_id = 1;
  // 名称
  string nick_name = 2;
  // 是否操作生成过QA对
  bool is_gen_qa = 3;
  // 权限列表
  repeated Permission permission_list = 4;
}

// 账户信息请求
message DescribeAccountReq {
  // 机器人ID
  uint64 bot_biz_id = 1;
}

// 账户信息响应
message DescribeAccountRsp {
  message Permission {
    string permission_id = 1;
    string permission_name = 2;
  }
  // 员工ID
  uint64 staff_biz_id = 1;
  // 名称
  string nick_name = 2;
  // 是否操作生成过QA对
  bool is_gen_qa = 3;
  // 权限列表
  repeated Permission permission_list = 4;
}

// 获取机器人列表请求
message ListRobotReq {}

// 获取机器人列表响应
message ListRobotRsp {
  message Robot {
    // 机器人ID
    uint64 bot_biz_id = 1;
    // 机器人名称
    string name = 2;
    // 机器人头像
    string avatar = 3;
    // 描述
    string desc = 4;
    // 机器人审核中昵称
    string name_in_audit = 5;
    // 机器人昵称是否在审核中
    bool is_name_in_audit = 6;
    // 机器人描述(prompt 场景使用)
    string role_description = 7;
    // 欢迎语
    string greeting = 8;
  }
  uint64 total = 1;
  repeated Robot list = 2;
}

// 获取机器人列表请求
message ListRobotV1Req {}

// 获取机器人列表响应
message ListRobotV1Rsp {
  message Robot {
    // 机器人ID
    uint64 bot_biz_id = 1;
    // 机器人名称
    string name = 2;
    // 机器人头像
    string avatar = 3;
    // 描述
    string desc = 4;
    // 机器人审核中昵称
    string name_in_audit = 5;
    // 机器人昵称是否在审核中
    bool is_name_in_audit = 6;
    // 机器人描述(prompt 场景使用)
    string role_description = 7;
    // 欢迎语
    string greeting = 8;
  }
  uint64 total = 1;
  repeated Robot robots = 2;
}

// 获取QA分类分组请求
message ListQACateReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 获取QA分类分组响应
message ListQACateRsp {
  message Cate {
    // QA分类的业务ID
    uint64 cate_biz_id = 1;
    // 分类名称
    string name = 2;
    // 分类下QA数量
    uint32 total = 3;
    // 是否可新增
    bool can_add = 4;
    // 是否可编辑
    bool can_edit = 5;
    // 是否可删除
    bool can_delete = 6;
    // 子分类
    repeated Cate children = 7;
  }
  repeated Cate list = 1;
}

// 获取QA分类分组请求
message ListQACateV1Req {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 获取QA分类分组响应
message ListQACateV1Rsp {
  message Cate {
    // 分类ID
    int64 id = 1;
    // 分类名称
    string name = 2;
    // 分类下QA数量
    uint32 total = 3;
    // 是否可新增
    bool can_add = 4;
    // 是否可编辑
    bool can_edit = 5;
    // 是否可删除
    bool can_delete = 6;
    // 子分类
    repeated Cate children = 7;
  }
  repeated Cate records = 1;
}

// 创建问答分类请求
message CreateQACateReq {
  // 分类名称
  string name = 1 [(validate.rules).string.min_len = 1];
  // 父分类业务ID,0为未分类
  uint64 parent_biz_id = 2;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 创建问答分类响应
message CreateQACateRsp {
  // 分类业务ID
  uint64 cate_biz_id = 1;
  // 是否可新增
  bool can_add = 2;
  // 是否可编辑
  bool can_edit = 3;
  // 是否可删除
  bool can_delete = 4;
}

// 创建问答分类请求
message CreateQACateV1Req {
  // 分类名称
  string name = 1 [(validate.rules).string.min_len = 1];
  int64 parent_id = 2;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 创建问答分类响应
message CreateQACateV1Rsp {
  // 分类 ID
  uint64 id = 1;
  // 是否可新增
  bool can_add = 2;
  // 是否可编辑
  bool can_edit = 3;
  // 是否可删除
  bool can_delete = 4;
}

// 分类修改请求
message UpdateQACateReq {
  // 分类ID
  uint64 id = 1;
  // 分类名称
  string name = 2 [(validate.rules).string.min_len = 1];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
  // 分类业务ID
  uint64 cate_biz_id = 4;
}

// 分类修改响应
message UpdateQACateRsp {}

// 分类修改请求
message ModifyQACateReq {
  // 分类ID
  uint64 id = 1;
  // 分类名称
  string name = 2 [(validate.rules).string.min_len = 1];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
  // 分类业务ID
  uint64 cate_biz_id = 4;
}

// 分类修改响应
message ModifyQACateRsp {}

// 分类删除请求
message DeleteQACateReq {
  // 分类ID
  uint64 id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 分类业务ID
  uint64 cate_biz_id = 3;
}

// 分类删除响应
message DeleteQACateRsp {}

// 获取QA列表请求
message GetQAListReq {
  // 查询内容
  string query = 1;
  // 分类ID -1代表不过滤
  int64 cate_id = 2;
  // 校验状态 1未校验2采纳3不采纳
  repeated uint32 accept_status = 3;
  // 发布状态(2 待发布 3 发布中 4 已发布 5 发布失败)
  repeated uint32 release_status = 4;
  // 文档ID
  uint64 doc_id = 5;
  // QAID
  uint64 qa_id = 6;
  // 页码
  uint32 page = 7 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
  // 来源(1 文档生成 2 批量导入 3 手动添加)
  uint32 source = 9;
  // 机器人ID
  uint64 bot_biz_id = 10 [(validate.rules).uint64.gt = 0];
}

// 获取QA列表响应
message GetQAListRsp {
  // QA详情
  message QA {
    // QA ID
    uint64 id = 1;
    // 问题
    string question = 2;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 3;
    // 来源描述
    string source_desc = 4;
    // 更新时间
    int64 update_time = 5;
    // 状态 1未校验2未发布3发布中4已发布5发布失败6不采纳
    uint32 status = 6;
    // 状态描述
    string status_desc = 7;
    // 文档id
    uint64 doc_id = 8;
    // 创建时间
    int64 create_time = 9;
    // 是否允许编辑
    bool is_allow_edit = 10;
    // 是否允许删除
    bool is_allow_delete = 11;
    // 是否允许校验
    bool is_allow_accept = 12;
    // 文档名称
    string file_name = 13;
    // 文档类型
    string file_type = 14;
    // 答案
    string answer = 15;
    // 问答字符数
    uint64 qa_char_size = 16;
    // 知识库有效开始时间，unix时间戳
    uint64 expire_start = 17;
    // 知识库有效结束时间，unix时间戳，0代表永久有效
    uint64 expire_end = 18;
  }
  uint64 total = 1;
  // 待校验
  uint64 wait_verify_total = 2;
  // 未采纳
  uint64 no_accepted_total = 3;
  // 已校验
  uint64 accepted_total = 4;
  // 页码
  uint32 page = 5 [(validate.rules).uint32.gt = 0];
  // 列表
  repeated QA list = 6;
}

// 获取QA列表请求
message ListQAReq {
  // 查询内容
  string query = 1;
  // 分类ID -1代表不过滤
  int64 cate_biz_id = 2;
  // 校验状态 1未校验2采纳3不采纳
  repeated uint32 accept_status = 3;
  // 发布状态(2待发布 3发布中 4已发布 7审核中 8审核失败 9人工申述中 11人工申述失败)
  repeated uint32 release_status = 4;
  // 文档ID
  uint64 doc_biz_id = 5;
  // QAID
  uint64 qa_biz_id = 6;
  // 页码
  uint32 page_number = 7 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
  // 来源(1 文档生成 2 批量导入 3 手动添加)
  uint32 source = 9;
  // 机器人ID
  uint64 bot_biz_id = 10 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  string query_answer = 11;
  // 根据多个QA业务ID查询列表
  repeated uint64 qa_biz_ids = 12;
}

// 获取QA列表响应
message ListQARsp {
  // QA详情
  message QA {
    // QA ID
    uint64 qa_biz_id = 1;
    // 问题
    string question = 2;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 3;
    // 来源描述
    string source_desc = 4;
    // 更新时间
    int64 update_time = 5;
    // 状态 1未校验2未发布3发布中4已发布5发布失败6不采纳
    uint32 status = 6;
    // 状态描述
    string status_desc = 7;
    // 文档id
    uint64 doc_biz_id = 8;
    // 创建时间
    int64 create_time = 9;
    // 是否允许编辑
    bool is_allow_edit = 10;
    // 是否允许删除
    bool is_allow_delete = 11;
    // 是否允许校验
    bool is_allow_accept = 12;
    // 文档名称
    string file_name = 13;
    // 文档类型
    string file_type = 14;
    // 答案
    string answer = 15;
    // 问答字符数
    uint64 qa_char_size = 16;
    // 知识库有效开始时间，unix时间戳
    uint64 expire_start = 17;
    // 知识库有效结束时间，unix时间戳，0代表永久有效
    uint64 expire_end = 18;
    // 属性标签适用范围 1：全部，2：按条件
    uint32 attr_range = 19;
    // 属性标签
    repeated AttrLabel attr_labels = 20;
  }
  uint64 total = 1;
  // 待校验
  uint64 wait_verify_total = 2;
  // 未采纳
  uint64 not_accepted_total = 3;
  // 已校验
  uint64 accepted_total = 4;
  // 页码
  uint32 page_number = 5 [(validate.rules).uint32.gt = 0];
  // 列表
  repeated QA list = 6;
}

// 获取QA详情请求
message GetQADetailReq {
  // QA ID
  uint64 id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 获取QA详情响应
message GetQADetailRsp {
  // QA ID
  uint64 id = 1;
  // question
  string question = 2;
  // answer
  string answer = 3;
  // 来源
  uint32 source = 4;
  // 来源描述
  string source_desc = 5;
  // 更新时间
  int64 update_time = 6;
  // 状态
  uint32 status = 7;
  // 状态描述
  string status_desc = 8;
  // 分类ID
  uint64 cate_id = 9;
  // 是否允许校验
  bool is_allow_accept = 10;
  // 是否允许编辑
  bool is_allow_edit = 11;
  // 是否允许删除
  bool is_allow_delete = 12;
  // 文档id
  uint64 doc_id = 13;
  // 文档名称
  string file_name = 14;
  // 文档类型
  string file_type = 15;
  // 分片ID
  uint64 segment_id = 16;
  // 分片内容, 待下个版本前端修改后可删除 @sinutelu
  // /qbot/admin/getQADetail
  //   page_content -> org_data
  string page_content = 17 [deprecated = true];
  // 分片高亮内容
  repeated Highlight highlights = 18;
  // 分片内容
  string org_data = 19;
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 20;
  // 属性标签
  repeated AttrLabelV1 attr_labels = 21;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 22;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 23;
}

// 获取QA详情请求
message DescribeQAReq {
  // QA业务ID
  uint64 qa_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 获取QA详情响应
message DescribeQARsp {
  // QA业务ID
  uint64 qa_biz_id = 1;
  // 问题
  string question = 2;
  // 答案
  string answer = 3;
  // 来源
  uint32 source = 4;
  // 来源描述
  string source_desc = 5;
  // 更新时间
  int64 update_time = 6;
  // 状态
  uint32 status = 7;
  // 状态描述
  string status_desc = 8;
  // 分类ID
  uint64 cate_biz_id = 9;
  // 是否允许校验
  bool is_allow_accept = 10;
  // 是否允许编辑
  bool is_allow_edit = 11;
  // 是否允许删除
  bool is_allow_delete = 12;
  // 文档id
  uint64 doc_biz_id = 13;
  // 文档名称
  string file_name = 14;
  // 文档类型
  string file_type = 15;
  // 分片ID
  uint64 segment_biz_id = 16;
  // 分片内容, 待下个版本前端修改后可删除 @sinutelu
  // /qbot/admin/getQADetail
  //   page_content -> org_data
  string page_content = 17 [deprecated = true];
  // 分片高亮内容
  repeated Highlight highlights = 18;
  // 分片内容
  string org_data = 19;
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 20;
  // 属性标签
  repeated AttrLabel attr_labels = 21;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 22;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 23;
  // 自定义参数
  string custom_param = 24;
}

// 分片高亮内容
message Highlight {
  // 高亮启始位置
  uint64 start_pos = 1;
  // 高亮结束位置
  uint64 end_pos = 2;
  // 高亮子文本
  string text = 3;
}

// 新建QA请求
message CreateQAReq {
  // 分组ID
  uint64 cate_biz_id = 1;
  // question
  string question = 2 [(validate.rules).string.min_len = 1];
  // answer
  string answer = 3 [(validate.rules).string.min_len = 1];
  // 文档ID
  uint64 doc_biz_id = 4;
  // 机器人ID
  uint64 bot_biz_id = 5 [(validate.rules).uint64.gt = 0];
  // 业务来源
  uint32 business_source = 6;
  // 来源的业务ID
  uint64 business_id = 7;
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 8 [(validate.rules).uint32.gt = 0];
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 9;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 10;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 11;
  // 自定义参数
  string custom_param = 12;
}

// 新建QA响应
message CreateQARsp {
  // QA ID
  uint64 qa_biz_id = 1;
}

// 新建QA请求
message CreateQAV1Req {
  // 分组ID
  uint64 cate_id = 1;
  // question
  string question = 2 [(validate.rules).string.min_len = 1];
  // answer
  string answer = 3 [(validate.rules).string.min_len = 1];
  // 文档ID
  uint64 doc_id = 4;
  // 机器人ID
  uint64 bot_biz_id = 5 [(validate.rules).uint64.gt = 0];
  // 业务来源
  uint32 business_source = 6;
  // 来源的业务ID
  uint64 business_id = 7;
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 8 [(validate.rules).uint32.gt = 0];
  // 属性标签引用
  repeated AttrLabelReferV1 attr_labels = 9;
  // 知识库有效开始时间，unix时间戳,
  uint64 expire_start = 10;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 11;
}

// 新建QA响应
message CreateQAV1Rsp {
  // QA ID
  uint64 id = 1;
}

// 编辑QA请求
message UpdateQAReq {
  // QA ID
  uint64 id = 1 [(validate.rules).uint64.gt = 0];
  // question
  string question = 2 [(validate.rules).string.min_len = 1];
  // answer
  string answer = 3 [(validate.rules).string.min_len = 1];
  // 分组ID
  uint64 cate_id = 4;
  // 文档id
  uint64 doc_id = 5;
  // 机器人ID
  uint64 bot_biz_id = 6 [(validate.rules).uint64.gt = 0];
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 7;
  // 属性标签引用
  repeated AttrLabelReferV1 attr_labels = 8;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 9;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 10;
}

// 编辑QA响应
message UpdateQARsp {}

// 编辑QA请求
message ModifyQAReq {
  // QA ID
  uint64 qa_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // question
  string question = 2 [(validate.rules).string.min_len = 1];
  // answer
  string answer = 3 [(validate.rules).string.min_len = 1];
  // 分组ID
  uint64 cate_biz_id = 4;
  // 文档id
  uint64 doc_biz_id = 5;
  // 机器人ID
  uint64 bot_biz_id = 6 [(validate.rules).uint64.gt = 0];
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 7;
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 8;
  // 知识库有效开始时间，unix时间戳,
  uint64 expire_start = 9;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 10;
  // 自定义参数
  string custom_param = 11;
}

// 编辑QA响应
message ModifyQARsp {}

// 删除QA请求
message DeleteQAReq {
  // QA ID 列表
  repeated uint64 qa_biz_ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 删除QA响应
message DeleteQARsp {}

// 删除QA请求
message DeleteQAV1Req {
  // QA ID 列表
  repeated uint64 ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 删除QA响应
message DeleteQAV1Rsp {}

// 校验QA请求
message VerifyQAReq {
  // 问答信息
  message QAList {
    // QAID
    uint64 qa_biz_id = 1 [(validate.rules).uint64.gt = 0];
    // 是否采纳
    bool is_accepted = 2;
    // 分组ID
    uint64 cate_biz_id = 3;
    // 问题
    string question = 4;
    // 答案
    string answer = 5;
  }
  // QA
  repeated QAList list = 1;
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 校验QA响应
message VerifyQARsp {}

// 校验QA请求
message VerifyQAV1Req {
  // 问答信息
  message QAList {
    // QA ID
    uint64 id = 1;
    // 是否采纳
    bool is_accepted = 2;
    // 分组ID(待删除)
    uint64 cate_id = 3;
    // 问题
    string question = 4;
    // 答案
    string answer = 5;
    // QA业务ID(待补充规则必传且大于0)
    uint64 qa_biz_id = 6;
    // 分组业务ID
    uint64 cate_biz_id = 7;
  }
  // QA
  repeated QAList list = 1;
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 校验QA响应
message VerifyQAV1Rsp {}

// QA分组请求
message GroupQAReq {
  // QA ID
  repeated uint64 ids = 1;
  // 分组 ID
  uint64 cate_id = 2;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
  // QA业务ID列表【ids】
  repeated uint64 qa_biz_ids = 4;
  // 分组 ID【cate_id】
  uint64 cate_biz_id = 5;
}

// QA分组响应
message GroupQARsp {}

// 编辑QA适用范围请求
message ModifyQAAttrRangeReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // QA业务ID列表【ids】
  repeated uint64 qa_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 3;
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 4;
}

// 编辑QA适用范围响应
message ModifyQAAttrRangeRsp {}

// 批量编辑文档适用范围请求
message ModifyDocAttrRangeReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文档业务ID【ids】
  repeated uint64 doc_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 3;
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 4;
}

// 批量编辑文档适用范围响应
message ModifyDocAttrRangeRsp {}






// 获取评测版本ID请求
message GetReviewVersionReq {}

// 获取评测版本ID响应
message GetReviewVersionRsp {
  // 版本ID
  repeated uint64 version_id = 1;
}

// 新增发布任务请求
message CreateReleaseReq {
  // 发布描述
  string desc = 1 [(validate.rules).string = {min_len: 0, max_len: 200}];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 新增发布任务响应
message CreateReleaseRsp {
  // 发布业务ID
  uint64 release_biz_id = 1;
}

// 新增发布任务请求
message GetReleaseInfoReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 新增发布任务响应
message GetReleaseInfoRsp {
  // Deprecated 最后发布时间 前端已废弃待删除
  int64 last_time = 1;
  // 发布状态 1:发布中,2:已发布
  uint32 status = 2;
  // is_updated 是否编辑过
  bool is_updated = 3;
  // Deprecated msg 失败原因 前端已废弃待删除
  string msg = 4;
}

// 新增发布任务请求
message DescribeReleaseInfoReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 新增发布任务响应
message DescribeReleaseInfoRsp {
  // Deprecated 最后发布时间 前端已废弃待删除
  int64 last_time = 1;
  // 发布状态 1:发布中,2:已发布
  uint32 status = 2;
  // is_updated 是否编辑过
  bool is_updated = 3;
  // Deprecated msg 失败原因 前端已废弃待删除
  string msg = 4;
}

// 发布文档预览请求
message ListReleaseDocPreviewReq {
  // 查询内容
  string query = 1;
  // 版本ID
  uint64 release_biz_id = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 行为 (1新增2修改3删除)
  repeated uint32 actions = 5;
  // 页码
  uint32 page_number = 6 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 7 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 8 [(validate.rules).uint64.gt = 0];
}

// 发布文档预览响应
message ListReleaseDocPreviewRsp {
  message Doc {
    // 文档名称
    string file_name = 1;
    // 文档类型
    string file_type = 2;
    // 更新时间
    int64 update_time = 3;
    // 状态
    uint32 action = 4;
    // 状态描述
    string action_desc = 5;
    // 失败原因
    string message = 9;
    // 文档业务ID
    uint64 doc_biz_id = 10;
  }
  uint64 total = 1;
  repeated Doc list = 2;
}

message ListReleaseQAPreviewReq {
  // 查询内容
  string query = 1;
  // 版本ID
  uint64 release_biz_id = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 状态 (1新增2修改3删除)
  repeated uint32 actions = 5;
  // 页码
  uint32 page_number = 6 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 7 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 8 [(validate.rules).uint64.gt = 0];
  // 发布状态
  repeated uint32 release_status = 9;
}

message ListReleaseQAPreviewRsp {
  message QA {
    // 问题
    string question = 1;
    // 更新时间
    int64 update_time = 2;
    // 状态
    uint32 action = 3;
    // 状态描述
    string action_desc = 4;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 5;
    // 来源描述
    string source_desc = 6;
    // 文档名称
    string file_name = 7;
    // 文档类型
    string file_type = 8;
    // 失败原因
    string message = 9;
    // 发布状态
    uint32 release_status = 10;
    // QAID
    uint64 qa_biz_id = 11;
    // 文档业务ID
    uint64 doc_biz_id = 12;
  }
  uint64 total = 1;
  repeated QA list = 2;
}

// 发布拒答问题预览请求
message ListRejectedQuestionPreviewReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询内容
  string query = 2;
  // 版本ID
  uint64 release_biz_id = 3;
  // 状态 (1新增2修改3删除)
  repeated uint32 actions = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
  // 页码
  uint32 page_number = 7 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
}

// 发布拒答问题预览响应
message ListRejectedQuestionPreviewRsp {
  // 发布拒答问题预览列表
  message RejectedQuestions {
    // 被拒答的问题
    string question = 1;
    // 更新时间
    int64 update_time = 2;
    // 状态
    uint32 action = 3;
    // 状态描述
    string action_desc = 4;
    // 失败原因
    string message = 5;
  }
  // 拒答问题预览总数
  uint64 total = 1;
  // 拒答问题列表
  repeated RejectedQuestions list = 2;
}

// 发布配置项预览请求
message ListReleaseConfigPreviewReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询内容
  string query = 2;
  // 版本ID
  uint64 release_biz_id = 3;
  // 状态 (1新增2修改3删除)
  repeated uint32 actions = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
  // 页码
  uint32 page_number = 7 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
  // 发布状态
  repeated uint32 release_status = 9;
}

// 发布配置项预览响应
message ListReleaseConfigPreviewRsp {
  // 发布配置项预览列表
  message ReleaseConfigs {
    // 配置项描述
    string config_item = 1;
    // 更新时间
    int64 update_time = 2;
    // 状态
    uint32 action = 3;
    // 变更前的内容
    string last_value = 4;
    // 变更后的内容
    string value = 5;
    // 变更内容(优先级展示content内容,content为空取value内容)
    string content = 6;
    // 失败原因
    string message = 7;
  }
  // 预览总数
  uint64 total = 1;
  // 列表
  repeated ReleaseConfigs list = 2;
}

// 发布记录列表请求
message ListReleaseReq {
  // 页码
  uint32 page_number = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 发布记录列表请求
message GetReleaseRecordListReq {
  // 页码
  uint32 page = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 发布记录列表响应
message GetReleaseRecordListRsp {
  message Release {
    // 版本ID
    uint64 id = 1;
    // 发布人
    string operator = 2;
    // 发布描述
    string desc = 3;
    // 更新时间
    int64 update_time = 4;
    // 发布状态
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 失败原因
    string reason = 7;
    // 发布成功数
    uint32 success_count = 8;
    // 发布失败数
    uint32 fail_count = 9;
  }
  uint64 total = 1;
  repeated Release list = 2;
}

// 发布记录列表响应
message ListReleaseRsp {
  message Release {
    // 版本ID
    uint64 release_biz_id = 1;
    // 发布人
    string operator = 2;
    // 发布描述
    string desc = 3;
    // 更新时间
    int64 update_time = 4;
    // 发布状态
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 失败原因
    string reason = 7;
    // 发布成功数
    uint32 success_count = 8;
    // 发布失败数
    uint32 fail_count = 9;
  }
  uint64 total = 1;
  repeated Release list = 2;
}

// 是否存在未确认问答
message CheckUnconfirmedQaReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 是否存在未确认问答
message CheckUnconfirmedQaRsp {
  // 是否存在
  bool exist = 1;
}

// 查询发布详情请求
message DescribeReleaseReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 发布ID
  uint64 release_biz_id = 2;
}

// 查询发布详情响应
message DescribeReleaseRsp {
  // 状态(1待发布 2发布中 3发布成功 4发布失败 5发布中 6发布中 7发布失败 9发布暂停)
  uint32 status = 1;
  // 状态描述
  string status_desc = 2;
  // 发布描述
  string description = 3;
  // 创建时间
  int64 create_time = 4;
}

// 拉取相似问答对
message GetQaSimilarReq {
  // 页码
  uint32 page = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 拉取相似问答对
message GetQaSimilarRsp {
  // 相似问答对数量
  uint64 total = 1;
  // 相似问答对数量
  repeated uint64 similar_ids = 2;
}

// 拉取相似问答对
message ListQaSimilarReq {
  // 页码
  uint32 page_number = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 拉取相似问答对
message ListQaSimilarRsp {
  // 相似问答对数量
  uint64 total = 1;
  // QA相似问业务ID
  repeated uint64 similar_biz_ids = 2;
}

// 拉取相似问答对详情
message GetQaSimilarDetailReq {
  // 相似问答对ID
  uint64 similar_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 拉取相似问答对详情
message GetQaSimilarDetailRsp {
  message QA {
    // QA ID
    uint64 id = 1;
    // 问题
    string question = 2;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 3;
    // 来源描述
    string source_desc = 4;
    // 答案
    string answer = 5;
    // 文档名称
    string file_name = 6;
    // 文档类型
    string file_type = 7;
    // 更新时间
    int64 update_time = 8;
    // 文档id
    uint64 doc_id = 9;
  }
  repeated QA list = 1;
}

// 拉取相似问答对详情
message DescribeQaSimilarReq {
  // 相似问答对业务ID
  uint64 similar_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 拉取相似问答对详情
message DescribeQaSimilarRsp {
  message QA {
    // QA业务 ID
    uint64 qa_biz_id = 1;
    // 问题
    string question = 2;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 3;
    // 来源描述
    string source_desc = 4;
    // 答案
    string answer = 5;
    // 文档名称
    string file_name = 6;
    // 文档类型
    string file_type = 7;
    // 更新时间
    int64 update_time = 8;
    // 文档业务ID
    uint64 doc_biz_id = 9;
  }
  repeated QA list = 1;
}

// 提交相似问答对选项
message SubmitQaSimilarReq {
  message QaSimilarInfo {
    uint64 similar_id = 1;
    uint64 del_qa_id = 2;
    uint64 save_qa_id = 3;
    // 是否忽略问答对
    bool is_ignore = 4;
    uint64 similar_biz_id = 5;
    uint64 del_qa_biz_id = 6;
    uint64 save_qa_biz_id = 7;
  }
  repeated QaSimilarInfo qa_similar_info = 1;
  // 是否忽略全部
  bool is_ignore_all = 2;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 提交相似问答对选项
message SubmitQaSimilarRsp {}

// 获取最新的消息 请求
message ListNotifyReq {
  // 业务ID
  uint32 page_id = 1;
  // 游标
  uint64 cursor = 2;
  // 最新ID
  uint64 last_id = 3;
  // 机器人ID
  uint64 bot_biz_id = 4 [(validate.rules).uint64.gte = 0];
}

// 获取最新的消息 请求
message GetNotifyReq {
  // 业务ID
  uint32 page_id = 1;
  // 游标
  uint64 cursor = 2;
  // 最新ID
  uint64 last_id = 3;
  // 机器人ID
  uint64 bot_biz_id = 4 [(validate.rules).uint64.gt = 0];
}
// OpParams 操作参数
message OpParams {
  // cos路径
  string cos_path = 1;
  // 发布ID
  uint64 version_id = 2;
  uint32 appeal_type = 3;
  uint64 doc_biz_id = 4;
  uint64 feedback_biz_id = 5;
  string extra_json_data = 6;
  string qa_biz_id = 7;
}

// OP 操作
message OP {
  // 操作类型 1跳转到文档库 2跳转到发布页详情 3跳转到问答库
  uint32 type = 1;
  // 操作参数
  OpParams params = 2;
}

// 获取最新的消息 响应
message ListNotifyRsp {
  message PageNotify {
    uint64 id = 1;
    string subject = 2;
    string content = 3;
    bool allow_close = 4;
    string level = 5;
    repeated OP ops = 6;
  }
  message CursorNotify {
    uint64 id = 1;
    string subject = 2;
    string content = 3;
    bool allow_close = 4;
    string level = 5;
    repeated OP ops = 6;
  }
  message CenterNotify {
    uint64 id = 1;
    string subject = 2;
    string content = 3;
    bool allow_close = 4;
    bool is_read = 5;
    int64 create_time = 6;
    string level = 7;
    repeated OP ops = 8;
  }
  // 页面通知
  repeated PageNotify page_notify_list = 1;
  // 全局通知
  repeated CursorNotify cursor_notify_list = 2;
  // 通知中心通知
  repeated CenterNotify center_notify_list = 3;
  // 未读消息数量
  uint64 unread_num = 4;
  // 未校验问答数量
  uint64 unconfirmed_qa_num = 5;
  // 是否有待校验问答
  bool has_unconfirmed_qa = 6;
  // 生成中问答任务数量
  uint64 qa_running_task_num = 7;
}


// TaskType 任务类型枚举
enum TaskType {
  ModifyAttributeLabel = 0;
  ExportAttributeLabel = 1;
}

// GetTaskStatusReq 获取任务状态 请求
message GetTaskStatusReq {
  // 任务ID
  string task_id = 1 [(validate.rules).string.min_len = 1];
  // 任务类型
  string task_type = 2 ;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// TaskStatus 任务状态
enum TaskStatus {
  // 执行中
  RUNNING = 0;
  // 成功
  SUCCESS = 1;
  // 失败
  FAILED = 2;
  // 等待中
  PENDING = 3;
}

// GetTaskStatusRsp 获取任务状态 响应
message GetTaskStatusRsp {
  string task_id = 1;
  // 任务类型
  string task_type = 2;
  // 任务状态
  string status = 3;
  // 任务消息,如失败信息
  string message = 4;
  // 任务参数
  TaskParams params = 5;
}

// TaskParams 任务参数
message TaskParams {
  // 下载地址,需要通过cos桶临时秘钥去下载
  string cos_path = 1;
}

// 获取最新的消息 响应
message GetNotifyRsp {
  message PageNotify {
    uint64 id = 1;
    string subject = 2;
    string content = 3;
    bool allow_close = 4;
    string level = 5;
    repeated OP ops = 6;
  }
  message CursorNotify {
    uint64 id = 1;
    string subject = 2;
    string content = 3;
    bool allow_close = 4;
    string level = 5;
    repeated OP ops = 6;
  }
  message CenterNotify {
    uint64 id = 1;
    string subject = 2;
    string content = 3;
    bool allow_close = 4;
    bool is_readed = 5;
    int64 create_time = 6;
    string level = 7;
    repeated OP ops = 8;
  }
  // 页面通知
  repeated PageNotify page_notify = 1;
  // 全局通知
  repeated CursorNotify cursor_notify = 2;
  // 通知中心通知
  repeated CenterNotify center_notify = 3;
  // 未读消息数量
  uint64 unread_num = 4;
  // 未校验问答数量
  uint64 unconfirmed_qa_num = 5;
  // 是否有待校验问答
  bool has_unconfirmed_qa = 6;
}

// 获取历史消息 请求
message GetHistoryNotifyReq {
  // id
  uint64 id = 1 [(validate.rules).uint64.gte = 0];
  // limit
  uint32 limit = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 获取历史消息 响应
message GetHistoryNotifyRsp {
  message Notify {
    uint64 id = 1;
    string subject = 2;
    string content = 3;
    bool is_readed = 4;
    repeated OP ops = 5;
    int64 create_time = 6;
    string level = 7;
  }
  uint64 total = 1;
  repeated Notify list = 2;
}

// 获取历史消息 请求
message ListHistoryNotifyReq {
  // id
  uint64 id = 1 [(validate.rules).uint64.gte = 0];
  // limit
  uint32 limit = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3;
}

// 获取历史消息 响应
message ListHistoryNotifyRsp {
  message Notify {
    uint64 id = 1;
    string subject = 2;
    string content = 3;
    bool is_read = 4;
    repeated OP ops = 5;
    int64 create_time = 6;
    string level = 7;
  }
  uint64 total = 1;
  repeated Notify list = 2;
}

// 通知已读 请求
message ReadNotifyReq {
  repeated uint64 ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 通知已读 响应
message ReadNotifyRsp {}

// 通知关闭 请求
message CloseNotifyReq {
  repeated uint64 ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 通知关闭 响应
message CloseNotifyRsp {}

// 获取来源 请求
message GetReferDetailReq {
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  repeated uint64 ids = 2 [(validate.rules).repeated .min_items = 1];
}

// 获取来源 响应
message GetReferDetailRsp {// TODO @sinute 前端接口
  message Detail {
    uint64 refer_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    string doc_name = 3;
    // 分片内容, 待下个版本前端修改后可删除 @sinutelu
    // /qbot/admin/getReferDetail
    //   page_content -> org_data
    string page_content = 4 [deprecated = true];
    string question = 5;
    string answer = 6;
    // 置信度
    float confidence = 7;
    // 标记
    uint32 mark = 8;
    // 分片高亮内容
    repeated Highlight highlights = 9;
    string org_data = 10;
  }
  repeated Detail details = 1;
}

// 获取来源 请求
message DescribeReferReq {
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  repeated uint64 refer_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
}

// 获取来源 响应
message DescribeReferRsp {// TODO @sinute 前端接口
  message ReferDetail {
    uint64 refer_biz_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    string doc_name = 3;
    // 分片内容, 待下个版本前端修改后可删除 @sinutelu
    // /qbot/admin/getReferDetail
    //   page_content -> org_data
    string page_content = 4 [deprecated = true];
    string question = 5;
    string answer = 6;
    // 置信度
    float confidence = 7;
    // 标记
    uint32 mark = 8;
    // 分片高亮内容
    repeated Highlight highlights = 9;
    string org_data = 10;
  }
  repeated ReferDetail list = 1;
}

// 来源打标 请求
message MarkReferReq {
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // Deprecated
  uint64 refer_id = 2;
  uint32 mark = 3;
  uint64 refer_biz_id = 4;
}

// 来源请求 响应
message MarkReferRsp {}

// 导出QA列表请求
message ExportQAListReq {
  ListQAReq filters = 1; // 根据筛选数据导出
  uint64 bot_biz_id = 2; // 机器人ID
  repeated uint64 qa_biz_ids = 3; // 勾选导出
}

// 导出QA列表响应
message ExportQAListRsp {}

// Deprecated 导出QA列表请求
message ExportQAListReqV1 {
  GetQAListReq filters = 1; // 根据筛选数据导出
  uint64 bot_biz_id = 2; // 机器人ID
  repeated uint64 qa_ids = 3; // 勾选导出
}

// Deprecated 导出QA列表响应
message ExportQAListRspV1 {}

message CreateAppealReq {
  // 机器人ID
  uint64 bot_biz_id = 1;
  // 审核类型
  uint32 appeal_type = 2 [(validate.rules).uint32.gt = 0];
  // Deprecated 发布版本ID
  uint64 version_id = 3;
  // Deprecated 问答ID
  uint64 qa_id = 4;
  // 申诉原因
  string reason = 5;
  // 发布版本ID
  uint64 release_biz_id = 7;
  // 问答ID
  uint64 qa_biz_id = 8;
  // 文档bizID
  string doc_biz_id = 9;
}

message CreateAppealRsp {}

// 待标注测试会话详情请求
message JudgeReq {
  // Deprecated 任务ID
  uint64 test_id = 1;
  // Deprecated 单条评测记录ID
  uint64 record_id = 2;
  // 1 准确 2错误
  uint32 answer_judge = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
  // 任务ID
  uint64 test_biz_id = 5;
  // 单条评测记录ID
  uint64 record_biz_id = 6;
}

// 待标注测试会话详情响应
message JudgeRsp {}

// 待标注测试记录详情请求
message GetOneJudgingReq {
  // 任务ID
  uint64 test_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 待标注测试记录详情响应
message GetOneJudgingRsp {
  // 总评测记录数量（包括已经评测和没有评测的）
  uint32 test_num = 1;
  // 已经完成标注数量
  uint32 judge_num = 2;
  // 单条评测记录ID
  uint64 record_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
}

// 待标注测试记录详情请求
message DescribeWaitJudgeRecordReq {
  // 任务ID
  uint64 test_biz_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 待标注测试记录详情响应
message DescribeWaitJudgeRecordRsp {
  // 总评测记录数量（包括已经评测和没有评测的）
  uint32 test_number = 1;
  // 已经完成标注数量
  uint32 judge_number = 2;
  // 单条评测记录ID
  uint64 record_biz_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
}

// 查询标注记录详情请求
message GetRecordReq {
  // 任务ID
  uint64 test_id = 1;
  // 单条评测记录ID
  uint64 record_id = 2;
  // 机器人ID
  uint64 bot_biz_id = 3;
}

// 查询标注记录详情响应
message GetRecordRsp {
  // 总评测记录数量（包括已经评测和没有评测的）
  uint32 test_num = 1;
  // 已经完成标注数量
  uint32 judge_num = 2;
  // 单条评测记录ID
  uint64 record_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
  // 0 待标注 1 准确 2错误
  uint32 answer_judge = 6;
}

// 查询标注记录详情请求
message DescribeRecordReq {
  // 任务ID
  uint64 test_biz_id = 1;
  // 单条评测记录ID
  uint64 record_biz_id = 2;
  // 机器人ID
  uint64 bot_biz_id = 3;
}

// 查询标注记录详情响应
message DescribeRecordRsp {
  // 总评测记录数量（包括已经评测和没有评测的）
  uint32 test_number = 1;
  // 已经完成标注数量
  uint32 judge_number = 2;
  // 单条评测记录ID
  uint64 record_biz_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
  // 0 待标注 1 准确 2错误
  uint32 answer_judge = 6;
}

// 任务删除请求
message DeleteTestReq {
  // 任务ID
  repeated uint64 test_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}
// 任务停止请求
message StopTestReq {
  // 任务ID
  repeated uint64 test_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 任务重试请求
message RetryTestReq {
  // 任务ID
  repeated uint64 test_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 任务删除响应
message DeleteTestRsp {}
// 任务停止响应
message StopTestRsp {}
// 任务重试响应
message RetryTestRsp {}

// 任务删除请求
message DeleteEvaluateTestReq {
  // 任务ID
  repeated uint64 test_biz_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}
// 任务停止请求
message StopEvaluateTestReq {
  // 任务ID
  repeated uint64 test_biz_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 任务重试请求
message RetryEvaluateTestReq {
  // 任务ID
  repeated uint64 test_biz_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 任务删除响应
message DeleteEvaluateTestRsp {}
// 任务停止响应
message StopEvaluateTestRsp {}
// 任务重试响应
message RetryEvaluateTestRsp {}

// 条件查询任务请求
message QueryTestReq {
  // 测试任务名称
  string test_name = 1 [(validate.rules).string = {max_len: 50}];
  // 页码
  uint32 page = 2;
  // 页面大小
  uint32 page_size = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 条件查询任务响应
message QueryTestRsp {
  // 任务数量
  uint32 total = 1;
  // 任务列表
  repeated SampleTest tests = 2;
}

// 条件查询任务请求
message ListEvaluateTestReq {
  // 测试任务名称
  string test_name = 1 [(validate.rules).string = {max_len: 50}];
  // 页码
  uint32 page_number = 2;
  // 页面大小
  uint32 page_size = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 条件查询任务响应
message ListEvaluateTestRsp {
  // 任务数量
  uint32 total = 1;
  // 任务列表
  repeated SampleTestDetail list = 2;
}

// 样本集详情
message SampleTestDetail {
  // 任务ID
  uint64 test_biz_id = 3;
  // 任务名称
  string test_name = 4;
  // 样本数量
  uint32 test_number = 5;
  // 导入时间
  uint64 create_time = 6;
  // 已经完成标注数量
  uint32 judge_number = 7;
  // 已经完成标注数量
  uint32 judge_right_number = 8;
  // 任务状态0 待评测  1 评测中  2 标注中 3 标注完成 4 删除 5 评测失败 6 人工停止
  uint32 status = 9;
  // 错误原因
  string message = 10;
}

// 样本集详情
message SampleTest {
  // 任务ID
  uint64 test_id = 3;
  // 任务名称
  string test_name = 4;
  // 样本数量
  uint32 test_num = 5;
  // 导入时间
  uint64 create_time = 6;
  // 已经完成标注数量
  uint32 judge_num = 7;
  // 已经完成标注数量
  uint32 judge_right_num = 8;
  // 任务状态0 待评测  1 评测中  2 标注中 3 标注完成 4 删除 5 评测失败 6 人工停止
  uint32 status = 9;
  // 错误原因
  string message = 10;
}

// 创建评测任务请求
message CreateTestReq {
  // 集合ID
  uint64 set_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
  // 测试任务名称
  string test_name = 3 [(validate.rules).string = {max_len: 50}];
}

// 创建评测任务响应
message CreateTestRsp {
  // 任务ID Deprecated 上云后需删除
  uint64 test_id = 1;
}

// 创建评测任务请求
message CreateEvaluateTestReq {
  // 集合业务ID
  uint64 set_biz_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
  // 测试任务名称
  string test_name = 3 [(validate.rules).string = {max_len: 50}];
}

// 创建评测任务响应
message CreateEvaluateTestRsp {
  // 任务业务ID
  uint64 test_biz_id = 1;
}

// 批量删除样本集合请求
message DeleteSampleReq {
  // 集合ID列表
  repeated uint64 set_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 批量删除样本集合响应
message DeleteSampleRsp {}

// 批量删除样本集合请求
message DeleteSampleSetReq {
  // 集合ID列表
  repeated uint64 set_biz_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 批量删除样本集合响应
message DeleteSampleSetRsp {}

// 查询样本集请求
message QuerySampleReq {
  // 文件名/集合名
  string set_name = 1;
  // 页码
  uint32 page = 2;
  // 页面大小
  uint32 page_size = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 样本集列表响应
message QuerySampleRsp {
  // 样本集数量
  uint32 total = 1;
  // 样本集
  repeated SampleSet samples = 2;
}

// 样本集详情
message SampleSet {
  // 集合名
  uint64 set_id = 3;
  // 集合名
  string set_name = 4;
  // 样本数量
  uint32 num = 5;
  // 导入时间
  uint64 create_time = 6;
}

// 查询样本集请求
message ListSampleSetReq {
  // 文件名/集合名
  string set_name = 1;
  // 页码
  uint32 page_number = 2;
  // 页面大小
  uint32 page_size = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 样本集列表响应
message ListSampleSetRsp {
  // 样本集数量
  uint32 total = 1;
  // 样本集
  repeated SampleSetDetail list = 2;
}

// 样本集详情
message SampleSetDetail {
  // 集合名
  uint64 set_biz_id = 3;
  // 集合名
  string set_name = 4;
  // 样本数量
  uint32 number = 5;
  // 导入时间
  uint64 create_time = 6;
}

// 保存样本文件请求
message UploadSampleReq {
  // 文件名/集合名
  string file_name = 1 [(validate.rules).string = {max_len: 50}];
  // cos路径
  string cos_url = 2;
  // ETag 全称为 Entity Tag，是对象被创建时标识对象内容的信息标签，可用于检查对象的内容是否发生变化
  string e_tag = 3;
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4;
  // 文件大小
  uint32 size = 5;
  // 机器人ID
  uint64 bot_biz_id = 6;
}

// 保存样本文件响应
message UploadSampleRsp {
  // 文档 ID
  uint64 id = 1;
  // 文档内样例数量
  uint32 total = 2;
  // 导入错误
  string error_msg = 3;
  // 错误链接
  string error_link = 4;
  // 错误链接文本
  string error_link_text = 5;
}

// 保存样本文件请求
message UploadSampleSetReq {
  // 文件名/集合名
  string file_name = 1 [(validate.rules).string = {max_len: 50}];
  // cos路径
  string cos_url = 2;
  // e_tag 全称为 Entity Tag,对象被创建时标识对象内容的信息标签
  string e_tag = 3;
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4;
  // 文件大小
  uint64 size = 5;
  // 机器人ID
  uint64 bot_biz_id = 6;
}

// 保存样本文件响应
message UploadSampleSetRsp {
  // 文档 业务ID
  uint64 set_biz_id = 1;
  // 文档内样例数量
  uint32 total = 2;
  // 导入错误
  string error_msg = 3;
  // 错误链接
  string error_link = 4;
  // 错误链接文本
  string error_link_text = 5;
}

// 带校验保存样本文件校验请求
message UploadSampleSetWithCheckReq {
  // 文件名/集合名
  string file_name = 1 [(validate.rules).string = {max_len: 50}];
  // cos路径
  string cos_url = 2;
  // e_tag 全称为 Entity Tag,对象被创建时标识对象内容的信息标签
  string e_tag = 3;
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4;
  // 文件大小
  uint64 size = 5;
  // 机器人ID
  uint64 bot_biz_id = 6;
}

// 带校验保存样本文件校验响应
message UploadSampleSetWithCheckRsp {
  // 文档 业务ID
  uint64 set_biz_id = 1;
  // 文档内样例数量
  uint32 total = 2;
  // 导入错误
  string error_msg = 3;
  // 错误链接
  string error_link = 4;
  // 错误链接文本
  string error_link_text = 5;
  // 是否允许导入
  bool is_allow = 6;
}

// 获取拒答问题列表请求
message GetRejectedQuestionListReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询内容
  string query = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
  // 页码
  uint32 page_number = 5;
}

// 获取拒答问题列表响应
message GetRejectedQuestionListRsp {
  // 拒答问题列表
  message RejectedQuestions {
    // 拒答问题ID
    uint64 id = 1;
    // 被拒答的问题
    string question = 2;
    // 状态
    uint32 status = 3;
    // 状态描述
    string status_desc = 4;
    // 更新时间
    int64 update_time = 5;
    // 是否允许编辑
    bool is_allow_edit = 6;
    // 是否允许删除
    bool is_allow_delete = 7;
  }
  // 拒答问题总数
  uint64 total = 1;
  // 拒答问题列表
  repeated RejectedQuestions list = 2;
}

// 获取拒答问题列表请求
message ListRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询内容
  string query = 2;
  // 页码
  uint32 page_number = 3 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
  //操作行为：1新增 2修改 3删除 4发布
  repeated uint32 actions = 5;
}

// 获取拒答问题列表响应
message ListRejectedQuestionRsp {
  // 拒答问题列表
  message RejectedQuestions {
    // 拒答问题ID
    uint64 rejected_biz_id = 1;
    // 被拒答的问题
    string question = 2;
    // 状态
    uint32 status = 3;
    // 状态描述
    string status_desc = 4;
    // 更新时间
    int64 update_time = 5;
    // 是否允许编辑
    bool is_allow_edit = 6;
    // 是否允许删除
    bool is_allow_delete = 7;
  }
  // 拒答问题总数
  uint64 total = 1;
  // 拒答问题列表
  repeated RejectedQuestions list = 2;
}

// 创建拒答问题请求
message CreateRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 拒答问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 拒答问题来源
  uint32 business_source = 3 [(validate.rules).uint32 = {in: [1, 2]}];
  // 拒答问题来源的数据源唯一id
  uint64 business_id = 4;
}

// 创建拒答问题响应
message CreateRejectedQuestionRsp {}

// 修改拒答问题请求
message UpdateRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 拒答问题id
  uint64 id = 2;
  // 拒答问题
  string question = 3 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 拒答问题id
  uint64 rejected_biz_id = 4;
}

// 修改拒答问题响应
message UpdateRejectedQuestionRsp {}

// 修改拒答问题请求
message ModifyRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 拒答问题id
  uint64 id = 2;
  // 拒答问题
  string question = 3 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 拒答问题id
  uint64 rejected_biz_id = 4;
}

// 修改拒答问题响应
message ModifyRejectedQuestionRsp {}

// 删除拒答问题请求
message DeleteRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 拒答问题id列表
  repeated uint64 ids = 2;
  // 拒答问题id业务列表
  repeated uint64 rejected_biz_ids = 3;
}

// 删除拒答问题响应
message DeleteRejectedQuestionRsp {}

// 导出拒答问题请求
message ExportRejectedQuestionReq {
  GetRejectedQuestionListReq filters = 1;
  // 需要导出的拒答问题id列表
  repeated uint64 rejected_question_ids = 2;
  // 需要导出的拒答问题id列表
  repeated uint64 rejected_biz_ids = 3;
}

// 导出拒答问题响应
message ExportRejectedQuestionRsp {}

// 发布拒答问题预览请求
message PreviewRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询内容
  string query = 2;
  // 版本ID
  uint64 version_id = 3;
  // 状态
  repeated uint32 status = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
  // 页码
  uint32 page = 7 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
}

// 发布拒答问题预览响应
message PreviewRejectedQuestionRsp {
  // 发布拒答问题预览列表
  message RejectedQuestions {
    // 拒答问题ID
    uint64 id = 1;
    // 被拒答的问题
    string question = 2;
    // 更新时间
    int64 update_time = 3;
    // 状态
    uint32 status = 4;
    // 状态描述
    string status_desc = 5;
    // 失败原因
    string message = 6;
  }
  // 拒答问题预览总数
  uint64 total = 1;
  // 拒答问题列表
  repeated RejectedQuestions list = 2;
}

// 发布文档预览请求
message GetReleaseDocPreviewReq {
  // 查询内容
  string query = 1;
  // 版本ID
  uint64 version_id = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 状态
  repeated uint32 status = 5;
  // 页码
  uint32 page = 6 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 7 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 8 [(validate.rules).uint64.gt = 0];
}

// 发布文档预览响应
message GetReleaseDocPreviewRsp {
  message Doc {
    // 文档名称
    string file_name = 1;
    // 文档类型
    string file_type = 2;
    // 更新时间
    int64 update_time = 3;
    // 状态
    uint32 status = 4;
    // 状态描述
    string status_desc = 5;
    // 失败原因
    string message = 9;
  }
  uint64 total = 1;
  repeated Doc list = 2;
}

message GetReleaseQAPreviewReq {
  // 查询内容
  string query = 1;
  // 版本ID
  uint64 version_id = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 状态
  repeated uint32 status = 5;
  // 页码
  uint32 page = 6 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 7 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 8 [(validate.rules).uint64.gt = 0];
  // 发布状态
  repeated uint32 release_status = 9;
}

message GetReleaseQAPreviewRsp {
  message QA {
    // 问题
    string question = 1;
    // 更新时间
    int64 update_time = 2;
    // 状态
    uint32 status = 3;
    // 状态描述
    string status_desc = 4;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 5;
    // 来源描述
    string source_desc = 6;
    // 文档名称
    string file_name = 7;
    // 文档类型
    string file_type = 8;
    // 失败原因
    string message = 9;
    // 发布状态
    uint32 release_status = 10;
    // QAID
    uint64 qa_id = 11;
  }
  uint64 total = 1;
  repeated QA list = 2;
}

// 查询不满意回复列表
message GetUnsatisfiedReplyReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 检索，用户问题或答案
  string query = 2;
  // 错误类型检索
  repeated string reasons = 3;
  // 页码
  uint32 page = 4 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
}

// 查询不满意问题回复列表响应
message GetUnsatisfiedReplyRsp {
  message UnsatisfiedReply {
    // 不满意回复ID
    uint64 id = 1;
    // 消息记录ID
    string record_id = 2;
    // 用户问题
    string question = 3;
    // 机器人回复
    string answer = 4;
    // 错误类型
    repeated string reasons = 5;
  }
  // 总数
  uint64 total = 1;
  repeated UnsatisfiedReply list = 2;
}

// 查询不满意回复列表
message ListUnsatisfiedReplyReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 检索，用户问题或答案
  string query = 2;
  // 错误类型检索
  repeated string reasons = 3;
  // 页码
  uint32 page_number = 4 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
}

// 查询不满意问题回复列表响应
message ListUnsatisfiedReplyRsp {
  message UnsatisfiedReply {
    // 不满意回复ID
    uint64 reply_biz_id = 1;
    // 消息记录ID
    string record_biz_id = 2;
    // 用户问题
    string question = 3;
    // 机器人回复
    string answer = 4;
    // 错误类型
    repeated string reasons = 5;
  }
  // 总数
  uint64 total = 1;
  repeated UnsatisfiedReply list = 2;
}

// 批量忽略不满意回复
message IgnoreUnsatisfiedReplyReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // Deprecated 不满意回复ID数组
  repeated uint64 ids = 2;
  // 不满意回复ID数组 后续需添加TRPC框架校验
  repeated uint64 reply_biz_ids = 3;
}

message IgnoreUnsatisfiedReplyRsp {}

// 导出不满意回复请求
message ExportUnsatisfiedReplyReq {
  message Filters {
    // 检索，用户问题或答案
    string query = 1;
    // 错误类型检索
    repeated string reasons = 2;
  }
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 根据筛选数据导出
  Filters filters = 2;
  // Deprecated 勾选导出
  repeated uint64 ids = 3;
  // 勾选导出
  repeated uint64 reply_biz_ids = 4;
}

// 导出不满意回复响应
message ExportUnsatisfiedReplyRsp {}

// 获取不满意回复上下文请求
message GetUnsatisfiedReplyContextReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 不满意问题ID
  uint64 id = 2 [(validate.rules).uint64 = {gte: 1}];
}

// 获取不满意回复上下文响应
message GetUnsatisfiedReplyContextRsp {
  message Context {
    // 消息记录ID信息
    string record_id = 1;
    // 是否为用户
    bool is_visitor = 2;
    // 昵称
    string nick_name = 3;
    // 头像
    string avatar = 4;
    // 消息内容
    string content = 5;
  }
  // 不满意回复上下文
  repeated Context list = 1;
}

// 获取不满意回复上下文请求
message DescribeUnsatisfiedReplyReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 不满意问题ID
  uint64 reply_biz_id = 2 [(validate.rules).uint64 = {gte: 1}];
}

// 获取不满意回复上下文响应
message DescribeUnsatisfiedReplyRsp {
  message Context {
    // 消息记录ID信息
    string record_biz_id = 1;
    // 是否为用户
    bool is_visitor = 2;
    // 昵称
    string nick_name = 3;
    // 头像
    string avatar = 4;
    // 消息内容
    string content = 5;
  }
  // 不满意回复上下文
  repeated Context list = 1;
}

// 任务信息
message TaskInfo {
  // 任务ID
  uint64 id = 1;
  // 机器人ID
  uint64 robot_id = 2;
  // 任务类型
  uint32 task_type = 3;
  // 任务互斥
  uint32 task_mutex = 4;
  // 任务参数
  string params = 5;
  // 重试次数
  uint32 retry_times = 6;
  // 最大重试次数
  uint32 max_retry_times = 7;
  // 超时时间(s)
  uint32 timeout = 8;
  // 执行器
  string runner = 9;
  // 执行器实例
  string runner_instance = 10;
  // 本次结果
  string result = 11;
  // trace id
  string trace_id = 12;
  // 任务开始执行时间
  int64 start_time = 13;
  // 任务完成时间
  int64 end_time = 14;
  // 下次任务开始执行时间
  int64 next_start_time = 15;
  // 创建时间
  int64 create_time = 16;
  // 更新时间
  int64 update_time = 17;
}

// 历史任务信息
message TaskHistoryInfo {
  // 任务ID
  uint64 id = 1;
  // 机器人ID
  uint64 robot_id = 2;
  // 任务类型
  uint32 task_type = 3;
  // 任务互斥
  uint32 task_mutex = 4;
  // 任务参数
  string params = 5;
  // 重试次数
  uint32 retry_times = 6;
  // 最大重试次数
  uint32 max_retry_times = 7;
  // 超时时间(s)
  uint32 timeout = 8;
  // 执行器
  string runner = 9;
  // 执行器实例
  string runner_instance = 10;
  // 本次结果
  string result = 11;
  // 是否成功
  bool is_success = 12;
  // trace id
  string trace_id = 13;
  // 任务开始执行时间
  int64 start_time = 14;
  // 任务完成时间
  int64 end_time = 15;
  // 下次任务开始执行时间
  int64 next_start_time = 16;
  // 创建时间
  int64 create_time = 17;
}

// 属性标签信息
message AttributeLabelV1 {
  // 标签ID
  uint64 label_id = 1;
  // 标签名称
  string label_name = 2 [(validate.rules).string = {min_len: 1}];
  // 相似标签名称
  repeated string similar_labels = 3 [(validate.rules).repeated = {items: {string: {min_len: 1}}}];
}

// 属性标签信息
message AttributeLabel {
  // 标签ID
  uint64 label_biz_id = 1;
  // 标签名称
  string label_name = 2 [(validate.rules).string = {min_len: 1}];
  // 相似标签名称
  repeated string similar_labels = 3 [(validate.rules).repeated = {items: {string: {min_len: 1}}}];
}

// 创建属性请求
message CreateAttributeLabelV1Req {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性标识
  string attr_key = 2 [(validate.rules).string = {min_len: 1}];
  // 属性名称
  string attr_name = 3 [(validate.rules).string = {min_len: 1}];
  // 属性标签
  repeated AttributeLabelV1 labels = 4 [(validate.rules).repeated .min_items = 1];
}

// 创建属性标签响应
message CreateAttributeLabelV1Rsp {}

// 创建属性请求
message CreateAttributeLabelReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性标识
  string attr_key = 2 [(validate.rules).string = {min_len: 1}];
  // 属性名称
  string attr_name = 3 [(validate.rules).string = {min_len: 1}];
  // 属性标签
  repeated AttributeLabel labels = 4 [(validate.rules).repeated .min_items = 1];
}

// 创建属性标签响应
message CreateAttributeLabelRsp {}

// Deprecated 删除属性标签
message DeleteAttributeLabelReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性ID
  repeated uint64 ids = 2;
  // 属性ID
  repeated uint64 attribute_biz_ids = 3;
}

message DeleteAttributeLabelRsp {}

// 编辑属性标签请求
message UpdateAttributeLabelReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性ID
  uint64 id = 2 [(validate.rules).uint64.gt = 0];
  // 属性标识
  string attr_key = 3 [(validate.rules).string = {min_len: 1}];
  // 属性名称
  string attr_name = 4 [(validate.rules).string = {min_len: 1}];
  // 删除的属性标签
  repeated uint64 delete_label_ids = 5;
  // 新增或编辑的属性标签
  repeated AttributeLabelV1 labels = 6;
}

// 编辑属性标签响应
message UpdateAttributeLabelRsp {}

// 编辑属性标签请求
message ModifyAttributeLabelReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性ID
  uint64 attribute_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 属性标识
  string attr_key = 3 [(validate.rules).string = {min_len: 1}];
  // 属性名称
  string attr_name = 4 [(validate.rules).string = {min_len: 1}];
  // 删除的属性标签
  repeated uint64 delete_label_biz_ids = 5;
  // 新增或编辑的属性标签
  repeated AttributeLabel labels = 6;
}

// 编辑属性标签响应
message ModifyAttributeLabelRsp {
  // 任务ID
  uint64 task_id = 1;
}

// 查询属性标签列表请求
message GetAttributeLabelListReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 查询内容
  string query = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 查询属性标签列表响应
message GetAttributeLabelListRsp {
  // 属性标签
  message AttrLabel {
    // 属性ID
    uint64 id = 1;
    // 属性标识
    string attr_key = 2;
    // 属性名称
    string attr_name = 3;
    // 标签名称
    repeated string label_names = 4;
    // 属性标签是否在更新中
    bool is_updating = 5;
  }
  uint64 total = 1;
  // 列表
  repeated AttrLabel list = 2;
}

// 查询属性标签列表请求
message ListAttributeLabelReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 查询内容
  string query = 2;
  // 页码
  uint32 page_number = 3 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 查询属性标签列表响应
message ListAttributeLabelRsp {
  // 属性标签
  message AttrLabelDetail {
    // 属性ID
    uint64 attr_biz_id = 1;
    // 属性标识
    string attr_key = 2;
    // 属性名称
    string attr_name = 3;
    // 标签名称
    repeated string label_names = 4;
    // 属性标签是否在更新中
    bool is_updating = 5;
  }
  uint64 total = 1;
  // 列表
  repeated AttrLabelDetail list = 2;
}

// 查询属性标签详情请求
message GetAttributeLabelDetailReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性ID
  uint64 id = 2 [(validate.rules).uint64.gt = 0];
  // 查询标签或相似标签
  string query = 3;
  // 滚动加载游标的标签ID
  uint64 last_label_id = 4;
  // 每次加载数量
  uint32 limit = 5 [(validate.rules).uint32.gt = 0];
}

message GetAttributeLabelDetailRsp {
  // 属性ID
  uint64 id = 1;
  // 属性标识
  string attr_key = 2;
  // 属性名称
  string attr_name = 3;
  // 标签数量
  uint64 label_count = 4;
  // 标签名称
  repeated AttributeLabelV1 labels = 5;
}

// 查询属性标签详情请求
message DescribeAttributeLabelReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性ID
  uint64 attribute_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 查询标签或相似标签
  string query = 3;
  // 滚动加载游标的标签ID
  uint64 last_label_biz_id = 4;
  // 每次加载数量
  uint32 limit = 5 [(validate.rules).uint32.gt = 0];
}

message DescribeAttributeLabelRsp {
  // 属性ID
  uint64 attribute_biz_id = 1;
  // 属性标识
  string attr_key = 2;
  // 属性名称
  string attr_name = 3;
  // 标签数量
  uint64 label_number = 4;
  // 标签名称
  repeated AttributeLabel labels = 5;
}

// 导入属性标签请求
message UploadAttributeLabelReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文件名
  string file_name = 2 [(validate.rules).string = {min_len: 1, max_len: 255}];
  // cos路径
  string cos_url = 3 [(validate.rules).string.min_len = 1];
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4 [(validate.rules).string.min_len = 1];
  // 文件大小
  uint64 size = 5 [(validate.rules).uint64.gt = 0];
}

// 导入属性标签响应
message UploadAttributeLabelRsp {
  // 导入错误
  string error_msg = 1;
  // 错误链接
  string error_link = 2;
  // 错误链接文本
  string error_link_text = 3;
}

// 导出属性标签请求
message ExportAttributeLabelReq {
  message Filters {
    // 检索，属性或标签名称
    string query = 1;
  }
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 根据筛选数据导出
  Filters filters = 2;
  // Deprecated 勾选导出
  repeated uint64 ids = 3;
  // 勾选导出
  repeated uint64 attribute_biz_ids = 4;
}

// 导出属性标签响应
message ExportAttributeLabelRsp {
  // 任务ID
  uint64 task_id = 1;
}

// 检查属性下标签是否引用请求
message CheckAttributeLabelReferReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // Deprecated 属性IDs
  repeated uint64 ids = 2;
  // Deprecated 属性标签ID
  uint64 label_id = 3;
  // 属性IDs 后续补充必要校验[(validate.rules).repeated = {items: {uint64: {gte: 1}}}];
  repeated uint64 attribute_biz_ids = 4;
  // 属性标签ID
  uint64 label_biz_id = 5;
}

// 检查属性下标签是否引用响应
message CheckAttributeLabelReferRsp {
  // 是否引用
  bool is_refer = 1;
}

// 检查属性下的标签名是否存在请求
message CheckAttributeLabelExistReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // Deprecated 属性ID
  uint64 id = 2;
  // Deprecated滚动加载，最后一个属性标签ID
  uint64 last_label_id = 3;
  // 属性标签名称
  string label_name = 4 [(validate.rules).string.min_len = 1];
  // 属性ID 后续补充[(validate.rules).repeated = {items: {uint64: {gte: 1}}}];
  uint64 attribute_biz_id = 5;
  // 滚动加载，最后一个属性标签ID
  uint64 last_label_biz_id = 6;
}

// 检查属性下的标签名是否存在响应
message CheckAttributeLabelExistRsp {
  // 是否存在
  bool is_exist = 1;
}



// 重试发布请求
message RetryReleaseReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 版本ID
  uint64 version_id = 2;
  // 发布业务ID
  uint64 release_biz_id = 3;
}

// 重试发布响应
message RetryReleaseRsp {}

// 记录用户最后一次访问待校验问答时间请求
message RecordUserAccessUnCheckQATimeReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 记录用户最后一次访问待校验问答时间响应
message RecordUserAccessUnCheckQATimeRsp {}

// 操作生成问答标记请求
message RecordUserFirstGenQAReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 操作生成问答标记响应
message RecordUserFirstGenQARsp {}

// 应用基础配置
message BaseConfig {
  // 机器人昵称
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 机器人头像
  string avatar = 2 [(validate.rules).string.min_len = 1];
  // 机器人描述
  string desc = 3 [(validate.rules).string = {min_len: 0, max_len: 200}];
}

// 创建应用请求结构体
message CreateAppReq {
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 1;
  // 机器人头像
  BaseConfig base_config = 2;
  // 场景,可为空，主要给前端来区分来源，来选择默认模型; hunyuan-从混元官网介绍页跳转后（创建使用）
  string scenes = 3;
  // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
  string pattern = 4 [(validate.rules).string = {in: ['','standard','agent','single_workflow']}];
}

// 创建应用返回结构体
message CreateAppRsp {
  uint64 app_biz_id = 1;
  // 是否是自定义列表权限，自定义列表权限需要做出交互提示
  bool is_custom_list = 2;
}

// 模型配置
message AppModel {
  // 模型名称
  string name = 1;
  // 模型描述
  string desc = 2;
  // Deprecated 多轮上下文指代轮次
  uint32 context_limit = 3 [deprecated = true];
  // 模型别名
  string alias_name = 4;
  // 是否使用上下文指代轮次
  bool is_use_context = 6;
  // 上下文记忆轮数
  uint32 history_limit = 7 [(validate.rules).uint32 = {gte:0, lte: 100}];
  // 温度
  string temperature = 8;
  // TopP
  string top_p = 9;
  // 资源状态 1：资源可用；2：资源已用尽
  uint32 resource_status = 10;

  // Token余额，已废弃使用
  double token_balance = 5 [deprecated = true];
}

// 知识问答搜索配置
message KnowledgeQaSearch {
  // 知识来源 doc：文档，qa：问答  taskflow：业务流程，search：搜索增强，database：数据库
  string type = 1;
  // 问答-回复灵活度 1：已采纳答案直接回复 2：已采纳润色后回复
  uint32 reply_flexibility = 2;
  // 搜索增强-搜索引擎状态
  bool use_search_engine = 3;
  // 是否显示搜索引擎检索状态
  bool show_search_engine = 4;
  // 知识来源，是否选择
  bool is_enabled = 5;
  // 问答最大召回数量, 默认2，限制5
  uint32 qa_top_n = 6 [(validate.rules).uint32 = {gte:0, lte: 5}];
  // 文档最大召回数量, 默认5，限制50
  uint32 doc_top_n = 7 [(validate.rules).uint32 = {gte:0, lte: 50}];
  // 置信度，针对文档和问答有效
  float confidence = 8;
  // 资源状态 1：资源可用；2：资源已用尽
  uint32 resource_status = 9;
}

// 知识应用输出配置
message KnowledgeQaOutput {
  // 输出方式 1：流式 2：非流式
  uint32 method = 1;
  // 是否使用行业通用知识库
  bool use_general_knowledge = 2;
  // 未知回复语，300字符以内
  string bare_answer = 3;
  // Deprecated 是否展示问题澄清开关（前端使用） -- 2.4变更 废弃
  bool show_question_clarify = 4;
  // 是否打开问题澄清 -- 2.4变更 内部使用，不对外暴露
  bool use_question_clarify = 5;
  // 问题澄清关键词列表 -- 2.4变更 内部使用，不对外暴露
  repeated string question_clarify_keywords = 6;
  // 是否使用推荐问题
  bool use_recommended = 7;
}

// 知识应用工作流程配置
message KnowledgeWorkflow {
  // 是否启用工作流程
  bool is_enabled = 1;
  // 是否启用PDL
  bool use_pdl = 2;
}

// 意图表达方式
message IntentAchievement {
  // 意图达成优先级定义, qa:问答回复、doc：文档回复、workflow：工作流回复，llm：大模型回复
  string name = 1;
  // 意图达成优先级描述
  string desc  = 2;
}

// 知识问答配置
message KnowledgeQaConfig {
  // 欢迎语，200字符以内
  string greeting = 1;
  // 机器人描述(prompt 场景使用)
  string role_description = 2;
  // 模型配置
  AppModel model = 3;
  // 检索配置
  repeated KnowledgeQaSearch search = 4;
  // 输出配置
  KnowledgeQaOutput output = 5;
  // 工作流程配置
  KnowledgeWorkflow workflow = 6;
  // 检索范围
  SearchRange search_range = 7;
  // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
  string pattern = 8 [(validate.rules).string = {in: ['','standard','agent','single_workflow']}];
  // 知识检索策略配置
  SearchStrategy search_strategy = 9;
  // 单工作流模式下传入，工作流ID
  KnowledgeQaSingleWorkflow single_workflow = 10;
  // 应用引用的插件
  repeated KnowledgeQaPlugin plugins = 11;
  // 思考模型配置
  AppModel thought_model = 12;
  // 意图达成优先级, qa:问答回复、doc：文档回复、workflow：工作流回复，llm：大模型回复
  repeated IntentAchievement intent_achievements = 13;
  // 图文检索
  bool image_text_retrieval = 14;
  // AICall 智能通话
  AICallConfig ai_call = 15;
  // 共享知识库配置
  repeated ShareKnowledgeBase share_knowledge_bases = 16;

  // 是否打开审核， 默认打开
  optional bool enable_audit = 17;
}

// 共享知识库配置
message ShareKnowledgeBase {
  // 共享知识库ID
  uint64 knowledge_biz_id = 1;
  // 检索范围
  SearchRange search_range = 2;
}

// 检索策略类型
enum SearchStrategyTypeEnum {
    Mixing = 0; // 混合检索
    Semantics = 1; // 语义检索
}

// 检索策略配置
message SearchStrategy {
  // 检索策略类型 0:混合检索，1：语义检索
  SearchStrategyTypeEnum strategy_type = 1;
  // excel检索增强，默认关闭
  bool table_enhancement = 2;
}

// 知识问答配置插件信息
message KnowledgeQaPlugin {
  // 插件ID
  string plugin_id = 1 [(validate.rules).string = {min_len: 1}];
  // 插件名称
  string plugin_name = 2; 
  // 插件图标
  string plugin_icon = 3;
  // 工具ID
  string tool_id = 4 [(validate.rules).string = {min_len: 1}];
  // 工具名称
  string tool_name = 5;
  // 工具描述
  string tool_desc = 6;
  // 工具输入参数
  repeated RequestParam Inputs = 7;
  // 该插件是否和知识库绑定
  bool is_binding_knowledge = 8;
}

// 知识问答配置单工作流
message KnowledgeQaSingleWorkflow {
  // 工作流ID
  string workflow_id = 1;
  // 工作流名称
  string workflow_name = 2;
  // 工作流描述
  string workflow_desc = 3;
  // 工作流状态
  string status = 4;
  // 是否启用
  bool is_enable = 5;
  // 单工作流模式下，开启异步调用
  bool async_workflow = 6;
}

// 检索范围
message SearchRange {
  string condition = 1; //检索条件
  message ApiVarAttrInfo {
    string api_var_id = 1;
    uint64 attr_biz_id = 2;
  }
  repeated ApiVarAttrInfo api_var_attr_infos = 2;
}

// 知识摘要输出配置
message SummaryOutput {
  // 输出方式 1：流式 2：非流式
  uint32 method = 1;
  // 输出要求 1：文本总结 2：自定义要求
  uint32 requirement = 2;
  // 自定义要求指令
  string require_command = 3 [(validate.rules).string = {min_len: 0, max_len: 500}];
}

// 知识摘要应用配置
message SummaryConfig {
  // 模型配置
  AppModel model = 1;
  // 输出配置
  SummaryOutput output = 2;
  // 欢迎语，200字符以内
  string greeting = 3 [(validate.rules).string = {min_len: 0, max_len: 200}];
}

// 标签信息
message ClassifyLabel {
  // 标签名称
  string name = 1 [(validate.rules).string = {min_len: 0, max_len: 10}];
  // 标签描述
  string description = 2 [(validate.rules).string = {min_len: 0, max_len: 100}];
  // 标签取值范围
  repeated string values = 3 [(validate.rules).repeated = {items: {string: {max_len: 1000}}}];
}

// 标签提取配置
message ClassifyConfig {
  // 模型配置
  AppModel model = 1;
  // 标签列表
  repeated ClassifyLabel labels = 2;
  // 欢迎语，200字符以内
  string greeting = 3 [(validate.rules).string = {min_len: 0, max_len: 200}];
}

// 应用配置
message AppConfig {
  // 知识问答应用配置
  KnowledgeQaConfig knowledge_qa = 1;
  // 知识摘要应用配置
  SummaryConfig summary = 2;
  // 标签配置
  ClassifyConfig classify = 3;
}

// 智能通话配置
message AICallConfig {
  // 启用语音互动功能
  bool enable_voice_interact = 1;
  // 启用语音通话
  bool enable_voice_call = 2;
  // 启用数智人
  bool enable_digital_human = 3;
  // 音色配置
  VoiceConfig voice = 4;
  // 数智人配置
  DigitalHumanConfig digital_human = 5;
}

// 数智人配置
message DigitalHumanConfig{
  // 数智人形象资产id
  string asset_key = 1;
  // 数智人形象名称
  string name = 2;
  // 数智人图片
  string avatar = 3;
  // 数智人预览地址
  string preview_url = 4;
}

message VoiceConfig{
  // 公有云音色id
  uint32 voice_type = 1;
  // 数智人音色key,需要和公有云音色id对齐
  string timbre_key = 2;
  // 音色名称
  string voice_name = 3;
}

// 修改应用请求结构体
message ModifyAppReq {
  uint64 app_biz_id = 1;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 2;
  // 机器人头像
  BaseConfig base_config = 3;
  // 应用配置
  AppConfig app_config = 4;
}

// 修改应用返回结构体
message ModifyAppRsp {
  uint64 app_biz_id = 1;
  // 修改时间
  int64 update_time = 2;
}

// 获取应用列表请求结构体
message ListAppReq {
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 1;
  // 每页数目，整型
  uint32 page_size = 2;
  // 页码，整型
  uint32 page_number = 3;
  // 应用/修改人
  string keyword = 4;
}

// 应用信息
message AppInfo {
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 1;
  // 应用类型描述
  string app_type_desc = 2;
  // 应用ID
  uint64 app_biz_id = 3;
  // 应用名称
  string name = 4;
  // 应用头像
  string avatar = 5;
  // 应用描述
  string desc = 6;
  // 应用状态，1：未上线，2：运行中，3：停用
  uint32 app_status = 7;
  // 状态说明
  string app_status_desc = 8;
  // 修改时间
  int64 update_time = 9;
  // 最后修改人
  string operator = 10;
  // 生成模型别名
  string model_alias_name = 11;
  // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
  string pattern = 12;
  // 思考模型别名
  string thought_model_alias_name = 13;
}

// 获取应用列表返回结构体
message ListAppRsp {
  // 数量
  uint64 total = 1;
  repeated AppInfo list = 2;
}

// 获取应用详情请求结构体
message DescribeAppReq {
  uint64 app_biz_id = 1;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 2;
  // 是否发布配置 默认评测配置
  bool is_release = 3;
}

// 获取应用详情返回结构体
message DescribeAppRsp {
  // 应用 ID
  uint64 app_biz_id = 1;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 2;
  // 应用类型说明
  string app_type_desc = 3;
  // 机器人基础配置
  BaseConfig base_config = 4;
  // 应用配置
  AppConfig app_config = 5;
  // 头像是否在申诉中
  bool avatar_in_appeal = 6;
  // 角色描述是否在申诉中
  bool role_in_appeal = 7;
  // 应用名称是否在申诉中
  bool name_in_appeal = 8;
  // 欢迎语是否在申诉中
  bool greeting_in_appeal = 9;
  // 未知问题回复语是否在申诉中
  bool bare_answer_in_appeal = 10;
  // app_key
  string app_key = 11;
  // 应用状态，1：未上线，2：运行中，3：停用
  uint32 app_status = 12;
  // 状态说明
  string app_status_desc = 13;
}

// 删除应用请求结构体
message DeleteAppReq {
  uint64 app_biz_id = 1;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 2;
}

// 删除应用返回结构体
message DeleteAppRsp {}

// 获取模型列表请求结构体
message ListModelReq {
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 1;
  // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
  string pattern = 2 [(validate.rules).string = {in: ['','standard','agent','single_workflow']}];
  // 模型类别 generate：生成模型，thought：思考模型
  string model_category = 3 [(validate.rules).string = {in: ['','generate','thought']}];
}

// 模型基本信息信息
message ModelInfo {
  message ParameterRule {
    // 默认值
    float default = 1; 
    // 最小值
    float min = 2; 
    // 最大值
    float max = 3;
  }
  // 模型名称
  string model_name = 1;
  // 模型描述
  string model_desc = 2;
  // 模型别名
  string alias_name = 3;
  // 资源状态 1：资源可用；2：资源已用尽
  uint32 resource_status = 4;
  // 提示词内容字符限制
  string prompt_words_limit = 5;
  // topP 通过核心采样控制内容生成的多样性，较高的Top P值会导致生成更多样的内容
  ParameterRule top_p = 6;
  // temperature 温度，用于控制生成内容的随机性
  ParameterRule temperature = 7;
  // max_tokens 最多能生成的token数量
  ParameterRule max_tokens = 8;
  // 模型来源 Hunyuan:腾讯混元大模型,Experience 新模型体验,INDUSTRY 腾讯云行业大模型,Custom 自定义模型
  string source = 9;
  // 模型图标
  string icon = 10;
  // 是否限时免费
  bool is_free = 11;
  // 模型对话框可输入的上限
  uint32 input_len_limit = 12;
  // 支持工作流的类型 0:模型不支持; 1: 模型支持工作流； 2： 模型支持效果不佳；
  uint32 support_workflow_status = 13;
  // 模型类别 generate：生成模型，thought：思考模型,embedding：embedding模型，rerank：rerank模型
  string model_category = 14;
  // 是否默认模型
  bool is_default = 15;
  // 角色提示词输入长度限制
  uint32 role_len_limit = 16;
  // 是否专属并发
  bool is_exclusive = 17;
  // 支持ai通话类型 0:模型不支持; 1: 模型支持ai通话； 2： 模型ai通话支持效果不佳；
  uint32 support_ai_call_status = 18;
  // 专属并发数
  uint32 concurrency = 19;
}

// 获取模型列表返回结构体
message ListModelRsp {
  // 模型列表
  repeated ModelInfo list = 1;
}
// ListAppCategoryReq 应用类型列表请求
message ListAppCategoryReq {}

// ListAppCategoryRsp 应用类型列表响应
message ListAppCategoryRsp {
  message Option {
    string text = 1;
    string value = 2;
    string logo = 3;
  }
  repeated Option list = 1;
}

// GetAppSecretReq 获取应用密钥请求
message GetAppSecretReq {
  // 应用ID
  uint64 app_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// GetAppSecretRsp 获取应用密钥响应
message GetAppSecretRsp {
  string app_key = 1;
  int64 create_time = 2;
  bool is_release = 3;
  bool has_permission = 4;
}

// GetAppKnowledgeCountReq 获取知识库问答信息请求结构体
message GetAppKnowledgeCountReq {
  uint64 app_biz_id = 1; // 应用ID
  string type = 2; // doc-文档，qa-问答对
}

// GetAppKnowledgeCountRsp 获取知识库问答信息返回结构体
message GetAppKnowledgeCountRsp {
  uint64 total = 1; // 个数
}

// DescribeModelReq 模型详情请求
message DescribeModelReq {
  string app_type = 1 [(validate.rules).string = {min_len: 1}];  // 应用类型
  string model_name = 2 [(validate.rules).string = {min_len: 1}];  // 模型名字
}

// DescribeModelReq 模型详情响应
message DescribeModelRsp {
  uint32 chat_words_limit = 1; // 对话限制长度，prompt_words_limit=chat_words_limit+预留写指令4000
}

// ListClassifyLabelReq 标签列表请求
message ListClassifyLabelReq {
  // 分页数量
  uint32 page_size = 1;
  // 页码
  uint32 page_number = 2;
}

// ListClassifyLabelRsp 标签列表响应
message ListClassifyLabelRsp {
  // 总数
  uint64 total = 1;
  // 标签列表
  repeated ClassifyLabel list = 2;

  // 标签信息
  message ClassifyLabel {
    // 标签名称
    string name = 1 [(validate.rules).string = {min_len: 0, max_len: 10}];
    // 标签描述
    string description = 2 [(validate.rules).string = {min_len: 0, max_len: 100}];
    // 标签取值范围
    repeated string values = 3 [(validate.rules).repeated = {items: {string: {max_len: 1000}}}];
    // 标签ID
    uint32 id = 4;
  }
}

// ListAppPromptReq 应用Prompt列表请求
message ListSummaryPromptReq {
}

// ListAppPromptRsp 应用Prompt列表响应
message ListSummaryPromptRsp {
  // Prompt列表
  repeated Prompt list = 1;

  // Prompt prompt详情
  message Prompt {
    // 输出要求
    uint32 requirement = 1;
    // 名字
    string prompt_name = 2;
    // 描述
    string prompt_desc = 3;
    // 指令
    string prompt_command = 4;
  }
}

message DescribeLicenseReq {
  // 查询类型，app-应用;atomic-原子
  string type = 1;
}

message DescribeLicenseRsp {
  message LicenseInfo {
    // 名称
    string name = 1;
    // 证书使用状态: valid-有效; invalid-无效; expired-已过期; expiring-即将过期; probation-试用期
    string state = 2;
    // 证书使用状态说明
    string state_desc = 3;
    // 剩余天数
    uint32 remaining = 4;
    // 平台有效期起始日,ms
    uint64 validity_start = 5;
    // 平台有效期结束日,ms
    uint64 validity_end = 6;
  }
  // license信息列表
  repeated LicenseInfo list = 1;
}

// 添加反馈信息
message AddAgentFeedbackReq {
  // 应用业务ID
  string app_biz_id = 1;
  // 用户问题
  string question = 2;
  // 机器人回复
  string answer = 3;
  // 错误类型
  repeated string reasons = 4;
  // 回复方式
  uint32 reply_method = 5;
  // 意图类型
  string intent_category = 6;
  string session_id = 7;
  string record_id = 8;
  // ------------------- v2.7.1新增 --------------------------
  bool terms_accepted = 9; // 是否接受用户协议/隐私政策
}

// 添加反馈信息响应
message AddAgentFeedbackRsp {}


// 获取反馈详情请求
message DescribeAgentFeedbackReq {
  string feedback_biz_id = 1; // 反馈详情Id
}

message DescribeAgentFeedbackRsp{
  string business_id=1; // 反馈业务Id
  // 用户问题
  string question = 2;
  // 机器人回复
  string answer = 3;
  // 错误原因
  repeated string reasons = 4;
  // 处理状态
  uint32 status = 5;
  // 应用业务ID
  string app_biz_id = 6;
  // 拒绝理由
  string reject_reason = 7;
  // 优化结果
  string optimized_result = 8; // 优化结果
  string app_name = 9; // 应用名称
  string app_type = 10; // APP类型
}

message DeleteAgentFeedbackReq {
  repeated string feedback_biz_ids = 1; // 反馈Ids
}
message DeleteAgentFeedbackRsp{

}

// 查询反馈信息列表
message ListAgentFeedbackReq {
  // 检索，用户问题或答案
  string query = 1;
  // 错误类型检索
  repeated string reasons = 2;
  // 处理状态检索
  repeated uint32 status = 3;
  // 页码
  uint32 page_number = 4 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
  string app_biz_id = 6;
}

// 查询反馈信息列表响应
message ListAgentFeedbackRsp {
  message Feedback {
    // 反馈消息业务ID
    uint64 business_id = 1;
    // 用户问题
    string question = 2;
    // 机器人回复
    string answer = 3;
    // 反馈原因
    repeated string reasons = 4;
    // 处理状态
    uint32 status = 5;
    string app_name = 6; // 应用名称
    string app_biz_id = 7; // 应用Id
    string app_type = 8; // APP类型
  }
  // 总数
  uint32 total = 1;
  repeated Feedback list = 2;
}

enum FeedType {
  FLOW = 0;
  QA = 1;
  OTHER = 2; // 其他反馈
}

// 添加反馈信息
message AddFeedbackReq {
  // 应用业务ID
  uint64 app_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 用户问题
  string question = 2  [(validate.rules).string = {min_len: 1}];
  // 机器人回复
  string answer = 3  [(validate.rules).string = {min_len: 1}];
  // 错误类型
  repeated string reasons = 4;
  // 回复方式
  uint32 reply_method = 5;
  // 意图类型
  string intent_category = 6;
  // 参考来源
  repeated AnswerReference references = 7;
  // 正确答案
  string right_answer = 8;
  // 正确参考来源
  repeated AnswerReference right_references = 9;
  // 会话ID
  string session_id = 10  [(validate.rules).string = {min_len: 1}];
  // 会话消息记录ID
  string record_id = 11  [(validate.rules).string = {min_len: 1}];
  // 参考来源文档ID 后续替换为 doc_biz_id
  uint64 doc_id = 12;
  // ------------------- v2.6.0新增 --------------------------
  string workflow = 13; //  taskflow:老画布，workflow：新画布
  FeedType feed_type = 14; // 反馈类型； QA：知识型， FLOW：工作流
  string right_workflow_id = 15; // 正确的工作流画布ID
  string right_node_id = 16; // 正确的画布节点ID
  // ------------------- v2.7.1新增 --------------------------
  bool terms_accepted = 17; // 是否接受用户协议/隐私政策
  string expect_intent_category = 18; // 预期达成意图
}

// AnswerReference 答案的参考来源
message AnswerReference {
  // 参考来源类型 ，1: qa， 2: segment , 3: doc
  uint32 type = 1;
  // 来源的业务id, 优先使用biz id, 获取不到再用id
  uint64 biz_id = 2;
  // 来源id
  uint64 id = 3;
  // 来源名称
  string name = 4;
}

// 添加反馈信息响应
message AddFeedbackRsp {}

// 查询反馈信息列表
message ListFeedbackReq {
  // 检索，用户问题或答案
  string query = 1;
  // 错误类型检索
  repeated string reasons = 2;
  // 处理状态检索
  repeated uint32 status = 3;
  // 页码
  uint32 page_number = 4 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
}

// 查询反馈信息列表响应
message ListFeedbackRsp {
  message Feedback {
    // 反馈消息业务ID
    uint64 business_id = 1;
    // 用户问题
    string question = 2;
    // 机器人回复
    string answer = 3;
    // 反馈原因
    repeated string reasons = 4;
    // 处理状态
    uint32 status = 5;
    string app_name = 6; // 应用名称
    uint64 app_biz_id = 7; // 应用Id
    string feed_type = 8; // 反馈类型
    string app_type = 9; // 应用类型
  }
  // 总数
  uint32 total = 1;
  repeated Feedback list = 2;
}

// 查询反馈信息详情
message DescribeFeedbackReq {
  // 反馈业务ID
  uint64 feedback_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 查询反馈信息详情响应
message DescribeFeedbackRsp {
  // 反馈消息业务ID
  uint64 business_id = 1;
  // 用户问题
  string question = 2;
  // 机器人回复
  string answer = 3;
  // 错误类型
  repeated string reasons = 4;
  // 处理状态
  uint32 status = 5;
  // 参考来源信息
  repeated AnswerReference references = 6;
  // 正确答案
  string right_answer = 7;
  // 正确参考来源
  repeated AnswerReference right_references = 8;
  // 应用业务ID
  uint64 app_biz_id = 9;
  // 拒绝理由
  string reject_reason = 10;
  // 优化结果
  string optimized_result = 11;
  string feed_type = 12; // 反馈类型 知识型， FLOW：工作流
  string right_workflow_name = 13; // 正确的工作流名称
  string right_node_name = 14; // 正确的节点名称
  string right_node_type = 15; // 正确的节点类型
}

// 删除反馈信息
message DeleteFeedbackReq {
  // 反馈业务ID列表
  repeated uint64 feedback_biz_ids = 1 [(validate.rules).repeated .min_items = 1];
}

// 删除反馈信息响应
message DeleteFeedbackRsp {}

// 更新反馈信息状态
message UpdateFeedbackStatusReq {
  // 反馈业务ID列表
  uint64 feedback_biz_id = 1  [(validate.rules).uint64.gt = 0];
  // 处理状态
  uint32 status = 2;
  // 拒绝理由
  string reject_reason = 3;
  // 优化结果
  string optimized_result = 4;
}

// 企业账户类型
message AccountType {
  // 账户类型：
  // FileParse(文档解析)、Embedding、Rewrite(多轮改写)、
  // Concurrency(并发)、
  // KnowledgeSummary(知识总结)
  // KnowledgeQA(知识问答)、KnowledgeCapacity(知识库容量)、SearchEngine(搜索引擎)
  string type = 1;
  // 模型名称
  string model_name = 2;
}

message ListExperienceCenterAppReq {
  // 模型类型,knowledge_engine：知识引擎，hunyuan：混元
  string model_type = 1;
  // 每页数目，整型
  uint32 page_size = 2;
  // 页码，整型
  uint32 page_number = 3;
  // 应用名称
  string keyword = 4;
}

message ExperienceAppInfo {
  // 体验应用ID
  uint64 exp_app_biz_id = 1;
  // app_key
  string app_key = 2;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 3;
  // 应用类型说明
  string app_type_desc = 4;
  // 模型类型,knowledge_engine：知识引擎，hunyuan：混元
  string model_type = 5;
  // 模型类型说明
  string model_type_desc = 6;
  // 应用名称
  string name = 7;
  // 应用头像
  string avatar = 8;
  // 应用描述
  string desc = 9;
  // 创建时间
  int64 create_time = 10;
  // 更新时间
  int64 update_time = 11;
}

message ListExperienceCenterAppRsp {
  // 数量
  uint64 total = 1;
  // 体验应用列表
  repeated ExperienceAppInfo list = 2;
}

message CreateAppByExperienceAppReq {
  // 体验应用ID
  uint64 exp_app_biz_id = 1;
}

message CreateAppByExperienceAppRsp {
  // 应用ID
  uint64 app_biz_id = 1;
}

message GetDisplayDocsReq {
  // 应用ID
  uint64 app_biz_id = 1;
}

message DisplayDocInfo {
  uint64 doc_biz_id = 1;
  // 文件名称
  string file_name = 2;
  // 文件类型(markdown,word,txt)
  string file_type = 3;
  // 文件大小
  uint64 file_size = 4;
  // cos文件地址
  string cos_url = 5;
  // x-cos-hash-crc64ecma 用于校验文件一致性
  string cos_hash = 6;
  // 文档来源( 0  源文件导入  1 网页导入) 默认 0 源文件导入
  uint32 source = 7;
  // 网页导入url
  string web_url = 8;
}

message GetDisplayDocsRsp {
  repeated DisplayDocInfo list = 1;
}

message ListPackageReq {
  // 页码
  uint32 page_number = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 0:全部 1:可使用 2:已用完 3:已过期
  uint32 status = 3;
  // uin
  string uin_account = 4;
}

message ListPackageRsp {
  message Package {
    message Measure {
      // 计量类型
      string type = 1;
      // 配额
      double total = 2;
      // 余额
      double balance = 3;
      // 单位
      string unit = 4;
    }
    // 资源包名称
    string name = 1;
    // 来源描述
    string source_desc = 2;
    // 获取时间
    int64 create_time = 3;
    // 到期时间
    int64 expire_time = 4;
    // 计量详情
    Measure measure = 5;
  }
  // 筛选项目
  message FilterInfo{
    // 资源包名称
    string name = 1;
    // 子产品码
    string sub_product_code = 2;
  }
  // 总数
  uint64 total = 1;
  // 列表
  repeated Package list = 2;
  // 筛选列表
  repeated FilterInfo filters = 3;
}

message ListConcurrencyReq {
  // 页码
  uint32 page_number = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // uin
  string uin_account = 3;
}

message ListConcurrencyRsp {
  message Info {
    // 计费项名称
    string name = 1;
    // 类型描述
    string type_desc = 2;
    // 获取时间
    int64 create_time = 3;
    // 到期时间
    int64 expire_time = 4;
    // 并发规格
    uint32 concurrent_num = 5;
  }
  // 总数
  uint64 total = 1;
  // 列表
  repeated Info list = 2;
}

message ListKnowledgeCapacityReq {
  // 页码
  uint32 page_number = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // uin
  string uin_account = 3;
}

message ListKnowledgeCapacityRsp {
  message Info {
    message Capacity {
      // 配额
      double total = 2;
      // 余额
      double balance = 3;
      // 单位
      string unit = 4;
    }
    // 计费项名称
    string name = 1;
    // 获取时间
    int64 create_time = 2;
    // 到期时间
    int64 expire_time = 3;
    // 来源描述
    string source_desc = 4;
    // 计量详情
    Capacity capacity = 5;
  }
  // 总数
  uint64 total = 1;
  // 列表
  repeated Info list = 2;
}

// 调用仪表盘请求
message DescribeTokenUsageReq {
  // uin
  repeated string uin_account = 1;
  // 应用ID列表
  repeated uint64 app_biz_ids = 2;
  // 子业务类型
  string sub_biz_type = 3;
  // 模型标识
  string model_name = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
}

// 调用仪表盘响应
message DescribeTokenUsageRsp {
  // 总token消耗
  double total_token_usage = 1;
  // 输入token消耗
  double input_token_usage = 2;
  // 输出token消耗
  double output_token_usage = 3;
  // 接口调用次数
  uint32 api_call_stats = 4;
  // 搜索服务调用次数
  double search_usage = 5;
}

message DescribeTokenUsageGraphReq {
  // uin
  repeated string uin_account = 1;
  // 应用ID列表
  repeated uint64 app_biz_ids = 2;
  // 子业务类型
  string sub_biz_type = 3;
  // 模型标识
  string model_name = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
}

message DescribeTokenUsageGraphRsp {
  message Stat{
    // x轴坐标
    string x = 1;
    // y轴坐标
    double y = 2;
  }
  // 总消耗
  repeated Stat total = 1;
  // 输入消耗
  repeated Stat input = 2;
  // 输出消耗
  repeated Stat output = 3;
}

message DescribeCallStatsGraphReq {
  // uin
  repeated string uin_account = 1;
  // 应用ID列表
  repeated uint64 app_biz_ids = 2;
  // 子业务类型
  string sub_biz_type = 3;
  // 模型标识
  string model_name = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
}

message DescribeCallStatsGraphRsp {
  message Stat{
    // x轴坐标
    string x = 1;
    // y轴坐标
    double y = 2;
  }
  // 总消耗
  repeated Stat list = 1;
}

// 获取后付费产品列表请求
message ListPostpaidProductReq {}

// 获取后付费产品列表响应
message ListPostpaidProductRsp {
  // 产品信息
  message Product{
    // 产品名称
    string name = 1;
    // 产品状态
    uint32 status = 2;
    // 产品状态描述
    string status_desc = 3;
    // 后付费产品码
    string product_code = 4;
    // 后付费子产品码
    string sub_product_code = 5;
  }
  // 产品列表
  repeated Product list = 1;
}

// 修改后付费开关状态请求
message ModifyPostpaidSwitchReq {
  // 开关状态
  uint32 status = 1;
}

// 修改后付费开关状态响应
message ModifyPostpaidSwitchRsp {}

// 查询账户未生效资源包请求
message ListInvalidResourceReq {}

// 查询账户未生效资源包响应
message ListInvalidResourceRsp {
  // 是否有未生效的计费项
  bool has_invalid = 1;
  // 未生效计费项列表
  message Products{
    // 计费项名称
    string name = 1;
    // 计费项目状态 2：已用尽 3：已过期
    uint32 status = 2;
  }
  // 未生效计费项列表
  repeated Products list = 3;
}

// 创建单个后付费资源请求
message CreatePostPayResourceReq {
  // 后付费产品码
  string product_code = 1 [(validate.rules).string = {min_len: 1}];
  // 后付费子产品码
  string sub_product_code = 2 [(validate.rules).string = {min_len: 1}];
}

// 创建单个后付费资源响应
message CreatePostPayResourceRsp {}

// 查询后付费开关状态请求
message DescribePostpaidSwitchReq {}

// 查询后付费开关状态响应
message DescribePostpaidSwitchRsp {
  uint32 switch_status = 1;
}

// 查询搜索服务调用折线图请求
message DescribeSearchStatsGraphReq {
  // uin
  repeated string uin_account = 1;
  // 应用ID列表
  repeated uint64 app_biz_ids = 2;
  // 子业务类型
  string sub_biz_type = 3;
  // 模型标识
  string model_name = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
}

// 查询搜索服务调用折线图响应
message DescribeSearchStatsGraphRsp {
  message Stat{
    // x轴坐标
    string x = 1;
    // y轴坐标
    double y = 2;
  }
  // 总消耗
  repeated Stat list = 1;
}

message ListProductReq{}

message ListProductRsp{
  // 产品信息
  message Product{
    string name = 1;
    string alias = 2;
    string sub_biz_type = 3;
  }
  // 原子能力列表
  repeated Product atomic_list = 1;
  // 模型列表
  repeated Product list = 2;
  // 调用类型列表
  repeated Product call_type_list = 3;
}

message ListAccountReq{}

message ListAccountRsp{
  // 账号列表
  repeated string list = 1;
}

message ExportBillingInfoReq{
  // uin
  repeated string uin_account = 1;
  // 应用ID列表
  repeated uint64 app_biz_ids = 2;
  // 调用类型
  string call_type = 3;
  // 子业务类型
  string sub_biz_type = 4;
  // 模型标识
  string model_name = 5;
  // 开始时间
  int64 start_time = 6;
  // 结束时间
  int64 end_time = 7;
  // 页码
  uint32 page = 8;
  // 页面条数
  uint32 page_size = 9;
  // 文档解析子场景
  repeated string sub_scenes = 10;
}

message ExportBillingInfoRsp{}



// 获取搜索引擎资源状态
message GetSearchResourceStatusReq {
  // 应用ID
  uint64 app_biz_id = 1;
}

message GetSearchResourceStatusRsp {
  // 资源状态 1：资源可用；2：资源已用尽
  uint32 resource_status = 1;
}

message EventReportParams {
  // 事件内容
  string event_key = 1;
  // 事件内容
  string event_value = 2;
}

message AddEventReportReq {
  // 业务名称
  string business_name = 1;
  // 应用ID
  uint64 app_biz_id = 2;
  // 事件详情
  repeated EventReportParams event_params = 4;
  // 事件时间
  uint64 report_time = 5;
}

message AddEventReportRsp {}

message BatchEventReportParams {
  repeated EventReportParams event_params = 1;
  // 事件时间
  uint64 report_time = 2;
}

message BatchEventReportReq {
  // 业务名称
  string business_name = 1;
  // 应用ID
  uint64 app_biz_id = 2;
  // 上报内容
  repeated BatchEventReportParams batch_event_params = 3;
}

message BatchEventReportRsp {}



// 知识库容量统计请求
message DescribeKnowledgeUsageReq {}

// 知识库容量统计响应
message DescribeKnowledgeUsageRsp {
  // 可用字符数
  int64 available_char_size = 1;
  // 超量失效字符数
  int64 exceed_char_size = 2;
}

// 知识库容量统计饼图请求
message DescribeKnowledgeUsagePieGraphReq {
  // 应用ID列表
  repeated uint64 app_biz_ids = 1;
}

// 知识库容量统计饼图响应
message DescribeKnowledgeUsagePieGraphRsp {
  // 可用字符数
  int64 available_char_size = 1;
  message UsageDetail {
    // 应用名称
    string app_name = 1;
    // 用量
    uint64 used_char_size = 2;
    // 占比
    double proportion = 3;
  }
  // 知识库容量统计饼图列表信息
  repeated UsageDetail list = 2;
}

// 获取应用知识库调用明细请求
message ListAppKnowledgeDetailReq{
  // 应用id
  repeated uint64 app_biz_ids = 1;
  // 页码
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
}

// 获取应用知识库调用明细响应
message ListAppKnowledgeDetailRsp{
  uint32 total = 1;
  message KnowledgeDetail {
    // 应用名称
    string app_name = 1;
    // 已用字符数
    uint64 used_char_size = 2;
    // 占比
    double proportion = 3;
    // 超量字符数
    uint64 exceed_char_size = 4;
  }
  // 详情列表
  repeated KnowledgeDetail list = 2;
}

// 获取单次调用详情请求
message ListUsageCallDetailReq{
  // uin
  repeated string uin_account = 1;
  // 应用ID列表
  repeated uint64 app_biz_ids = 2;
  // 调用类型
  string call_type = 3;
  // 模型标识
  string model_name = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
  // 页码
  uint32 page_number = 7 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
}

// 获取单次调用详情响应
message ListUsageCallDetailRsp{
  message CallDetail{
    // id
    string id = 1;
    // 调用时间
    int64 call_time = 2;
    // 总token消耗
    double total_token_usage = 3;
    // 输入token消耗
    double input_token_usage = 4;
    // 输出token消耗
    double output_token_usage = 5;
    // 搜索服务调用次数
    uint32 search_usage = 6;
    // 模型名称
    string model_name = 7;
    // 调用类型
    string call_type = 8;
    // 账号
    string uin_account = 9;
    // 应用ID
    string app_name = 10;
  }
  // 列表
  repeated CallDetail list = 1;
  // 总数
  uint32 total = 2;
}

// 并发调用请求
message DescribeConcurrencyUsageReq{
  // 应用ID列表
  repeated uint64 app_biz_ids = 1;
  // 模型标识
  string model_name = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
}

// 并发调用响应
message DescribeConcurrencyUsageRsp{
  // 可用并发数
  uint32 available_concurrency = 1;
  // 并发峰值
  uint32 concurrency_peak = 2;
  // 调用超可用次数
  uint32 exceed_usage_time = 3;
}

// 并发调用明细请求
message ListConcurrencyDetailReq{
  // 应用ID列表
  repeated uint64 app_biz_ids = 1;
  // 模型标识
  string model_name = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 页码
  uint32 page_number = 5 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 6 [(validate.rules).uint32.gt = 0];
}

// 并发调用明细响应
message ListConcurrencyDetailRsp{
  message ConcurrencyDetail{
    // 调用时间
    int64 call_time = 1;
    // 模型名称
    string model_name = 2;
    // 应用名称
    string app_name = 3;
    // query
    string query = 4;
  }
  // 明细列表
  repeated ConcurrencyDetail list = 1;
  // 总条数
  uint32 total = 2;
}

// 并发调用折线图请求
message DescribeConcurrencyUsageGraphReq{
  // 应用ID列表
  repeated uint64 app_biz_ids = 1;
  // 子业务类型
  string sub_biz_type = 2;
  // 模型标识
  string model_name = 3;
  // 开始时间
  int64 start_time = 4;
  // 结束时间
  int64 end_time = 5;
}

// 并发调用折线图响应
message DescribeConcurrencyUsageGraphRsp{
  // 可用并发y轴坐标
  repeated int32 available_y = 1;
  // 成功调用并发y轴坐标
  repeated int32 success_call_y = 2;
  // x轴坐标
  repeated string x = 3;
}

message ExportConcurrencyInfoReq{
  // 应用ID列表
  repeated uint64 app_biz_ids = 1;
  // 子业务类型
  string sub_biz_type = 2;
  // 模型标识
  string model_name = 3;
  // 开始时间
  int64 start_time = 4;
  // 结束时间
  int64 end_time = 5;
  // 页码
  uint32 page = 8;
  // 页面条数
  uint32 page_size = 9;
}

message ExportConcurrencyInfoRsp{}

// 导出知识库容量详情请求
message ExportKnowledgeInfoReq {
  // 应用ID
  repeated uint64 app_biz_ids = 1;
}

// 导出知识库容量详情响应
message  ExportKnowledgeInfoRsp {
}

// 查询企业下应用和模型信息请求
message DescribeAppModelByCorpReq{
  // 企业ID
  uint64 corp_id = 1 [(validate.rules).uint64.gt = 0];
  // 应用id列表
  repeated uint64 app_biz_ids = 2;
}

// 发布标签预览请求
message ListReleaseLabelPreviewReq {
  // 查询内容
  string query = 1;
  // 版本ID
  uint64 release_biz_id = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 状态 (1新增2修改3删除)
  repeated uint32 actions = 5;
  // 页码
  uint32 page_number = 6 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 7 [(validate.rules).uint32.gt = 0];
  // 应用业务ID
  uint64 app_biz_id = 8 [(validate.rules).uint64.gt = 0];
}

// 发布标签预览响应
message ListReleaseLabelPreviewRsp {
  // 发布标签预览列表
  message Label {
    // 标签名称
    string label_name = 1;
    // 更新时间
    int64 update_time = 2;
    // 状态
    uint32 action = 3;
    // 状态描述
    string action_desc = 4;
    // 失败原因
    string message = 5;
  }
  uint64 total = 1;
  repeated Label list = 2;
}

// 发布同义词预览请求
message ListReleaseSynonymsPreviewReq {
  // 查询内容
  string query = 1;
  // 版本ID
  uint64 release_biz_id = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 状态 (1新增2修改3删除)
  repeated uint32 actions = 5;
  // 页码
  uint32 page_number = 6 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 7 [(validate.rules).uint32.gt = 0];
  // 应用业务ID
  uint64 app_biz_id = 8 [(validate.rules).uint64.gt = 0];
}

// 发布同义词预览响应
message ListReleaseSynonymsPreviewRsp {
   // 发布同义词预览列表
   message Synonyms {
    // 标准词名称
    string standard_word = 1;
    // 变更后的同义词
    repeated string synonyms = 2;
    // 更新时间
    int64 update_time = 3;
    // 状态
    uint32 action = 4;
    // 状态描述
    string action_desc = 5;
    // 失败原因
    string message = 6;
   }
   uint64 total = 1;
   repeated Synonyms list = 2;
}

// 修改应用基础信息请求
message ModifyAppBaseReq {
  uint64 app_biz_id = 1;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 2;
  // 机器人头像
  BaseConfig base_config = 3;
  // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
  string pattern = 8 [(validate.rules).string = {in: ['standard','agent','single_workflow']}];
  // 单工作流模式下传入，工作流ID
  string workflow_id = 9;
  // 单工作流模式下传入，是否开启异步工作流
  bool async_workflow = 10;
}

message ModifyAppBaseRsp {
}

// 创建自定义模型
message CreateCustomModelReq {
  // 模型ID
  string model = 1;
  // 模型别名
  string alias_name = 2 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 访问地址
  string endpoints = 3 [(validate.rules).string.min_len = 1];
  // 访问Key
  string api_key = 4;
  // 模型上下文长度单位K
  uint32 model_length = 5;
  // 输入长度单位K
  uint32 input_length = 6 [(validate.rules).uint32.gt = 0];
  // 接入来源 OpenAI、TI,默认OpenAI
  string access = 7 [(validate.rules).string = {in: ['TI','OpenAI']}];
}

message CreateCustomModelRsp{}

// 编辑自定义模型
message ModifyCustomModelReq {
  // 模型名称
  string model_name = 1 [(validate.rules).string.min_len = 1];
  // 模型ID
  string model = 2;
  // 模型别名
  string alias_name = 3 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 访问地址
  string endpoints = 4 [(validate.rules).string.min_len = 1];
  // 访问Key
  string api_key = 5;
  // 模型上下文长度单位K
  uint32 model_length = 6;
  // 输入长度单位K
  uint32 input_length = 7 [(validate.rules).uint32.gt = 0];
  // 接入来源 OpenAI、TI,默认OpenAI
  string access = 8 [(validate.rules).string = {in: ['TI','OpenAI']}];
}

message ModifyCustomModelRsp {}

// 删除自定义模型 
message DeleteCustomModelReq {
  // 模型名称
  string model_name = 1 [(validate.rules).string.min_len = 1];
}

message DeleteCustomModelRsp {}

// 查询自定义模型详情
message DescribeCustomModelReq {
  // 模型名称
  string model_name = 1 [(validate.rules).string.min_len = 1];
}

message DescribeCustomModelRsp {
  // 模型名称
  string model_name = 1;
  // 模型ID
  string model = 2;
  // 模型别名
  string alias_name = 3 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 访问地址
  string endpoints = 4 [(validate.rules).string.min_len = 1];
  // 访问Key
  string api_key = 5;
  // 模型上下文长度单位K
  uint32 model_length = 6;
  // 输入长度单位K
  uint32 input_length = 7 [(validate.rules).uint32.gt = 0];
  // 接入来源 OpenAI、TI,默认OpenAI
  string access = 8 [(validate.rules).string = {in: ['TI','OpenAI']}];
}

// 获取模型关联的应用
message DescribeModelAppsReq {
  // 模型名称
  string model_name = 1 [(validate.rules).string.min_len = 1];
}

message DescribeModelAppsRsp {
  message App {
    // 应用名称
    string app_name = 1;
  }
  message Workflow {
    // 应用名称
    string app_name = 1;
    // 工作流名称
    repeated string workflow_names = 2;
  }
  // 应用名称
  repeated App apps = 1;
  // 工作流信息
  repeated Workflow workflows = 2;
}


// GetUserGuideViewInfosReq  查看用户引导场景是否查看过信息请求
message GetUserGuideViewInfosReq {
  repeated  string scenes = 1; // 引导场景
}
// GetUserGuideViewInfosRsp 查看用户引导场景是否查看过信息返回
message GetUserGuideViewInfosRsp {
  message SceneInfo {
    string scene = 1; // 场景
    uint32 viewed = 2; // 是否观看过 1-已观看 2-未观看
  }
  repeated  SceneInfo  list = 1;
}

// SetUserGuideViewInfoReq 设置用户引导场景已观看信息请求
message SetUserGuideViewInfoReq {
    string scene = 1; // 设置用户引导场景已观看信息请求场景; 如： guide, tip, tipKnown (前端定义，后端未做任何限制)
}

// SetUserGuideViewInfoRsp 设置用户引导场景已观看信息返回
message SetUserGuideViewInfoRsp {
}


// 导出调用统计并发QPM、TPM分钟用量信息
message ExportMinuteDosageReq {
  // 应用ID列表
  repeated uint64 app_biz_ids = 1;
  // 模型标识
  string model_name = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
}

message ExportMinuteDosageRsp {}

// 图片预览请求,私有读
message GetImagePreviewReq {
   // 应用业务ID
   uint64 app_biz_id = 1;
   // 区分类型: offline:离线文件，realtime:实时文件；为空默认为offline
   string type_key = 2;
   // cos路径
   string cos_url = 3;
}

message GetImagePreviewRsp {
  // cos临时地址
  string url = 1;
}

// DescribeVoiceListReq 查询音色列表请求
message ListVoiceReq {
}

// DescribeVoiceListRsp 查询音色列表响应
message ListVoiceRsp {
  repeated VoiceInfo voice_list = 1;
}

// VoiceInfo音色信息
message VoiceInfo{
  // 公有云音色id
  uint32 voice_type = 1;
  // 音色名称
  string voice_name = 2;
  // 数智人音色key
  string timbre_key = 3;
  // 性别，male,female
  string sex = 4 [(validate.rules).string = {in: ['male','female']}];
  // 试听demo地址
  string demo_url = 5;
}

message ListDigitalHumanReq{}

message ListDigitalHumanRsp{
  repeated DigitalHuman digital_human_list = 1;
}

message DigitalHuman {
  string asset_key = 1;
  string name = 2;
  string avatar = 3;
  string preview_url = 4;
}

// GetPromptTemplateListReq 模板中心请求
message GetPromptTemplateListReq{
  string query = 1;
  uint32 page = 2;
  uint32 page_size = 3;
}

// GetPromptTemplateListRsp 模板中心响应
message GetPromptTemplateListRsp{
  message PromptTemplate{
    string title = 1;
    repeated  string label = 2;
    repeated string custom_variable = 3;
    string prompt_content = 4;
    int64 create_time = 5;
    string description = 6;
  }
  uint32 total = 1;
  repeated  PromptTemplate list=2;
}

message GetMsgLogListReq {
  string app_biz_id = 1 ; // 应用ID
  uint32 page = 2 ;  // 页码;
  uint32 page_size = 3; // 分页数量
  uint32 channel_type = 4 ;  // 渠道类型, 0 全部
  int32 feedback_type = 5 ; // 反馈类型， -1 为全部
  string query_type = 6; // 查询类型
  string query = 7;  // 查询内容
  string begin_time = 8; // 开始日期 2025-05-15
  string end_time = 9; // 结束日期 2025-05-15
  string create_time_orderby = 10 ; // create_time排序方式
}

message GetMsgLogListRsp {
  uint64 total = 1;
  repeated MsgLog list = 2;
}

message MsgLog {

  string id = 1; // 消息ID
  string app_biz_id = 2;  // 应用业务ID
  string record_id = 3;   // 记录ID
  string related_record_id = 4;   // 关联记录ID
  uint32 from_type = 5;   // 用户来源
  string from_id = 6;   // 用户ID
  uint32 channel_type = 7;   // 渠道类型
  string question = 8;   // 问题
  string answer = 9;   // 答案
  string intent = 10;   // 意图
  string intent_category = 11;  // 意图分类
  uint32 score = 12;   // 分数
  string user_biz_id = 13;   // 访客ID
  string session_id = 14;   // 会话ID
  string create_time = 15;   // 创建时间
  string user_biz_nickname = 16;   // 访客名称
  string user_biz_avatar = 17;   // 访客名称
}

message ExportMsgLogReq {
  string app_biz_id = 1 ; // 应用ID
  uint32 channel_type = 2 ;  // 渠道类型, 0 全部
  int32 feedback_type = 3 ; // 反馈类型， -1 为全部
  string query_type = 4; // 查询类型
  string query = 5;  // 查询内容
  string begin_time = 6; // 开始日期 2025-05-15
  string end_time = 7; // 结束日期 2025-05-15
}

message ExportMsgLogRsp {
}

message ConcurrencyRule {
  // 模型名称，专属模型
  string model_name = 1;
  // 所选应用ID
  repeated uint64 app_biz_ids = 2;
  // 并发限制
  uint32 concurrency = 3;
}

// 修改并发规则
message ModifyConcurrencyRuleReq {
  // 规则类型 1:所有应用共用模型并发，2：按应用设置并发上限
  uint32 rule_type = 1;
  // 规则列表
  repeated ConcurrencyRule rules = 2;
}

message ModifyConcurrencyRuleRsp {}

// 查看并发规则
message DescribeConcurrencyRuleReq {
}

message DescribeConcurrencyRuleRsp {
  // 规则类型 1:所有应用共用模型并发，2：按应用设置并发上限
  uint32 rule_type = 1;
  // 规则列表
  repeated ConcurrencyRule rules = 2;
  // 生效时间
  uint64 effective_time = 3;
}

message ListChannelReq {
  // 应用id
  uint64 app_biz_id = 1;
  // 页码
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  // 渠道类型 10000 微信订阅号 10001 微信服务号 10002 企微应用
  repeated uint32 channel_type = 4;
  // 渠道状态 1未发布 2运行中 2已下线
  repeated  uint32 channel_status = 5;
}

message ChannelInfo{
  // 渠道类型 10000 微信订阅号 10001 微信服务号 10002 企微应用
  uint32 channel_type = 1;
  // 渠道状态 1未发布 2运行中 2已下线
  uint32 channel_status = 2;
  // 渠道名称
  string channel_name = 3;
  // 渠道id 业务id
  uint64 channel_id = 4;
  // 备注
  string comment = 5;
  // 更新时间
  int64 update_time = 6;
  // 创建时间
  int64 create_time = 7;
  // 最后更新人
  string updated_user = 8;
}

message ListChannelRsp {
  // 总数据条数
  uint32 total = 1;
  // 渠道信息列表
  repeated ChannelInfo ListChannel = 2;

}

message DescribeChannelInfoReq {
  // 渠道业务id
  uint64 channel_id = 1;
  // 应用id
  uint64 app_biz_id = 2;
}

message DescribeChannelInfoRsp{
  // 渠道基本信息
  ChannelInfo basic_info = 1;
  // 企微相关参数
  WecomInfo wecom_info = 2;
  // 微信相关参数
  WechatInfo wechat_info = 3;
}

message WecomInfo {
  // 企业id
  string wecom_corp_id = 1;
  // 企微应用id
  uint64 wecom_agent_id = 2;
  // 企业微信应用secret
  string wecom_agent_secret = 3;
  // 企微回调token
  string wecom_callback_token = 4;
  // 企微回调secret
  string wecom_callback_aes_key = 5;
  // 多个逗号间隔 暂时不用
  string wecom_ip_list = 6;
  // 企微回调地址 服务企地址
  string wecom_callback_url = 7;
  // 企业反向代理地址
  string wecom_reverse_proxy = 8;
}

message WechatInfo {
  // 公众号id
  string wechat_app_id = 1;
  // 公众号refresh_token channel-msg服务会刷出来 调用这里写db
  string wechat_refresh_token = 2;
}


message UpdateChannelStatusReq{
  // 应用id
  uint64 app_biz_id = 1;
  // 渠道业务id
  uint64 channel_id = 2;
  // 渠道状态 1未发布 2运行中 2已下线
  uint32 channel_status = 3;
}

message UpdateChannelStatusRsp{

}

message UnbindChannelReq{
  // 应用id
  uint64 app_biz_id = 1;
  // 渠道id 业务id
  uint64 channel_id = 2;
}

message UnbindChannelRsp{

}

message UpdateChannelReq{
  // 应用id
  uint64 app_biz_id = 1;
  // 渠道id 业务id
  uint64 channel_id = 2;
  // 备注
  string comment = 3;

}
message UpdateChannelRsp{
}

// 创建渠道信息
message CreateChannelReq {
  //大模型应用ID
  uint64 app_biz_id = 1;
  //渠道的id
  string third_channel_id = 2;
  //渠道的企业id
  string third_channel_corp_id = 3;
  // 最后更新人
  string updated_user = 4;
  // 渠道类型 10000 微信订阅号 10001 微信服务号 10002 企微应用
  uint32 channel_type = 5;
  // 渠道备注
  string comment = 6;
  // 企微相关参数
  WecomInfo wecom_info = 7;
  // 微信相关参数
  WechatInfo wechat_info = 8;
}

message CreateChannelRsp{

}


message WecomDefaultInfoReq {
  //大模型应用ID
  uint64 app_biz_id = 1;
}

message WecomDefaultInfoRsp {
  // 企微应用 回调 token
  string wecom_agent_token = 1;
  // 企微应用 回调 aes key
  string wecom_agent_aes_key = 2;
  // 企微应用 回调 地址
  string wecom_callback_url = 3;
}


message CheckWhitelistReq {
  repeated string whitelist_key_list = 1;
}

message CheckWhitelistRsp {
  repeated string whitelist_key_list = 1;
}

message GetMsgLogOverviewReq {
  string app_biz_id = 1 ; // 应用ID
  uint32 channel_type = 2 ;  // 渠道类型, 0 全部
  string begin_time = 3; // 开始日期 2025-05-15
  string end_time = 4; // 结束日期 2025-05-15
}

message GetMsgLogOverviewRsp {
  uint32 count = 1; // 总数
  uint32 user_count = 2; // 反馈数
  uint32 like_count = 3; // 反馈率
  uint32 dislike_count = 4; // 意图数
  uint32 feedback_count = 5; // 意图率
}

message GetMsgLogCountTrendReq{
  string app_biz_id = 1 ; // 应用ID
  uint32 channel_type = 2 ;  // 渠道类型, 0 全部
  string begin_time = 3; // 开始日期 2025-05-15
  string end_time = 4; // 结束日期 2025-05-15
}

message GetMsgLogCountTrendRsp {
  message MsgLogCountTrendPoint {
    string datetime = 1; // 时间
    uint32 count = 2; // 数量
  }
  repeated MsgLogCountTrendPoint list = 1;
}

message GetMsgLogUserCountTrendReq{
  string app_biz_id = 1 ; // 应用ID
  uint32 channel_type = 2 ;  // 渠道类型, 0 全部
  string begin_time = 3; // 开始日期 2025-05-15
  string end_time = 4; // 结束日期 2025-05-15
}

message GetMsgLogUserCountTrendRsp {
  message MsgLogUserCountTrendPoint {
    string datetime = 1; // 时间
    uint32 count = 2; // 数量
  }
  repeated MsgLogUserCountTrendPoint list = 1;
}

message GetMsgLogFeedbackCountTrendReq{
  string app_biz_id = 1 ; // 应用ID
  uint32 channel_type = 2 ;  // 渠道类型, 0 全部
  string begin_time = 3; // 开始日期 2025-05-15
  string end_time = 4; // 结束日期 2025-05-15
}

message GetMsgLogFeedbackCountTrendRsp {
  message MsgLogFeedbackCountTrendPoint {
    string datetime = 1; // 时间
    uint32 like_count = 2; // 点赞数
    uint32 dislike_count = 3; // 点踩数
    uint32 feedback_count = 4; // 反馈数
  }
  repeated MsgLogFeedbackCountTrendPoint list = 1;
}

message ExportMsgLogStatisticalReq {
  string app_biz_id = 1 ; // 应用ID
  uint32 channel_type = 2 ;  // 渠道类型, 0 全部
  string begin_time = 3; // 开始日期 2025-05-15
  string end_time = 4; // 结束日期 2025-05-15
}
message ExportMsgLogStatisticalRsp {

}

// ApprovalStatusType 审批状态
enum ApprovalStatusType {
  Processing = 0;  // 审批中
  Approved = 1;    // 审批通过
  Rejected = 2;    // 审批驳回
}

// 提交业务审批请求
message CreateApprovalReq {
  // 资源ID
  string resource_id = 1 [(validate.rules).string = {min_len: 1}];
  // 资源类型
  uint64 resource_type = 2;
  // 审批通过后有效期（单位：小时）不填永久有效
  uint64 approval_expired_hours = 3;
}
// 提交业务审批响应
message CreateApprovalRsp {
  // 审批ID
  string approval_id = 1;
  // 是否成功
  bool is_success = 2;
  // 错误信息
  string err_msg = 3;
}

// 修改审批状态请求
message UpdateApprovalStatusReq {
  // 审批ID
  string approval_id = 1 [(validate.rules).string = {min_len: 1}];
  // 审批状态 1同意，2驳回
  ApprovalStatusType approval_status = 2;
}
// 修改审批状态响应
message UpdateApprovalStatusRsp {
  // 任务ID
  string approval_id = 1;
}

// 修改审批状态请求
message GetLastApprovalReq {
  // 审批ID
  string resource_id = 1 [(validate.rules).string = {min_len: 1}];
  // 资源类型
  uint64 resource_type = 2;
}
// 修改审批状态响应
message GetLastApprovalRsp {
  uint64 id = 1;
  string approval_id = 2;
  string resource_id = 3;
  uint64 resource_type = 4;
  ApprovalStatusType status = 5;
  uint64 approval_authorization_hours = 6;
  int64 approval_expired_time = 7;
  int64 approval_time = 8;
  int64 create_time = 9;
}