syntax = "proto3";

package trpc.KEP.bot_knowledge_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server";


// import "trpc.proto";
import "validate.proto";
import "knowledge-common.proto";
import "knowledge-custom-resource.proto";
import "knowledge-realtime.proto";
import "knowledge-search.proto";
import "knowledge_file_manager_callback.proto";
import "knowledge-share.proto";
import "bot-task-config-server/workflow-api.proto";

// 知识问答配置
service KnowledgeConfig {

}

// 管理后台 需要鉴权
service Admin {
  // 获取对象存储的临时密钥
  // @alias=/DescribeStorageCredential
  rpc DescribeStorageCredential(DescribeStorageCredentialReq) returns (DescribeStorageCredentialRsp);
  // Deprecated 获取对象存储的临时密钥
  // @alias=/GetCredential
  rpc GetCredential(GetCredentialReq) returns (GetCredentialRsp);
  // Deprecated 获取审核开关
  // @alias=/GetAuditSwitch
  rpc GetAuditSwitch(GetAuditSwitchReq) returns (GetAuditSwitchRsp);
  // 获取审核开关
  // @alias=/DescribeAuditSwitch
  rpc DescribeAuditSwitch(DescribeAuditSwitchReq) returns (DescribeAuditSwitchRsp);
  // 文档列表
  // @alias=/ListDoc
  rpc ListDoc(ListDocReq) returns (ListDocRsp);
  // 文档列表
  // @alias=/ListDocV1
  rpc ListDocV1(ListDocV1Req) returns (ListDocV1Rsp);
  // 保存文档
  // @alias=/SaveDoc
  rpc SaveDoc(SaveDocReq) returns (SaveDocRsp);
  // 保存文档
  // @alias=/SaveDocV1
  rpc SaveDocV1(SaveDocV1Req) returns (SaveDocV1Rsp);
  // 修改文档
  // @alias=/ModifyDoc
  rpc ModifyDoc(ModifyDocReq) returns (ModifyDocRsp);
  // 修改文档状态
  // @alias=/ModifyDocStatus
  rpc ModifyDocStatus(ModifyDocStatusReq) returns (ModifyDocStatusRsp);
  // 引用共享知识库
  // @alias=/ReferShareKnowledge
  rpc ReferShareKnowledge(trpc.KEP.knowledge.ReferSharedKnowledgeReq) returns(trpc.KEP.knowledge.ReferSharedKnowledgeRsp);
  // 查看引用共享知识库列表
  // @alias=/ListReferShareKnowledge
  rpc ListReferShareKnowledge(trpc.KEP.knowledge.ListReferSharedKnowledgeReq) returns(trpc.KEP.knowledge.ListReferSharedKnowledgeRsp);
  // 批量修改文档外部链接，到期时间
  // @alias=/BatchModifyDoc
  rpc BatchModifyDoc(BatchModifyDocReq) returns (BatchModifyDocRsp);
  // 批量编辑文档适用范围
  // @alias=/ModifyDocAttrRange
  rpc ModifyDocAttrRange(ModifyDocAttrRangeReq) returns (ModifyDocAttrRangeRsp);
  // 修改文档
  // @alias=/ModifyDocV1
  rpc ModifyDocV1(ModifyDocV1Req) returns (ModifyDocV1Rsp);
  // 删除文档
  // @alias=/DeleteDoc
  rpc DeleteDoc(DeleteDocReq) returns (DeleteDocRsp);
  // 检查文档引用的工作流
  // @alias=/CheckDocReferWorkFlow
  rpc CheckDocReferWorkFlow(CheckDocReferWorkFlowReq) returns (CheckDocReferWorkFlowRsp);
  // 检查标签引用的工作流
  // @alias=/CheckAttributeReferWorkFlow
  rpc CheckAttributeReferWorkFlow(CheckAttributeReferWorkFlowReq) returns (CheckAttributeReferWorkFlowRsp);
  // 获取文档详情
  // @alias=/DescribeDoc
  rpc DescribeDoc(DescribeDocReq) returns (DescribeDocRsp);
  // 批量获取文档详情
  // @alias=/DescribeDocs
  rpc DescribeDocs(DescribeDocsReq) returns (DescribeDocsRsp);
  // 获取片段详情
  // @alias=/DescribeSegments
  rpc DescribeSegments(DescribeSegmentsReq) returns (DescribeSegmentsRsp);
  // 批量获取非表格类型文档切片内容（不是精确翻页）
  // @alias=/ListDocSegment
  rpc ListDocSegment(ListDocSegmentReq) returns (ListDocSegmentRsp);
  // 获取表格类型文档sheet数据
  // @alias=/ListTableSheet
  rpc ListTableSheet(ListTableSheetReq) returns (ListTableSheetRsp);
  // 修改非表格类型文档切片（增、删、改）
  // @alias=/ModifyDocSegment
  rpc ModifyDocSegment(ModifyDocSegmentReq) returns (ModifyDocSegmentRsp);
  // 修改表格类型文档sheet
  // @alias=/ModifyTableSheet
  rpc ModifyTableSheet(ModifyTableSheetReq) returns (ModifyTableSheetRsp);
  // 提交切片干预任务
  // @alias=/CreateDocParsingIntervention
  rpc CreateDocParsingIntervention(CreateDocParsingInterventionReq) returns (CreateDocParsingInterventionRsp);
  // 答案中是否引用
  // @alias=/ReferDoc
  rpc ReferDoc(ReferDocReq) returns (ReferDocRsp);
  // Deprecated 重新生成QA对
  // @alias=/StartCreateQA
  rpc StartCreateQA(StartCreateQAReq) returns (StartCreateQARsp);
  // 获取文档预览链接
  // @alias=/GetDocPreview
  rpc GetDocPreview(GetDocPreviewReq) returns (GetDocPreviewRsp);
  // 生成QA对
  // @alias=/GenerateQA
  rpc GenerateQA(GenerateQAReq) returns (GenerateQARsp);
  // Deprecated 获取文档下拉列表
  // @alias=/GetSelectDoc
  rpc GetSelectDoc(GetSelectDocReq) returns (GetSelectDocRsp);
  // 获取文档下拉列表
  // @alias=/ListSelectDoc
  rpc ListSelectDoc(ListSelectDocReq) returns (ListSelectDocRsp);
  // 获取网页内容
  // @alias=/FetchURLContent
  rpc FetchURLContent(FetchURLContentReq) returns (FetchURLContentRsp);
  // 中止文档解析
  // @alias=/StopDocParse
  rpc StopDocParse(StopDocParseReq) returns (StopDocParseRsp);
  // 重试文档解析
  // @alias=/RetryDocParse
  rpc RetryDocParse(RetryDocParseReq) returns (RetryDocParseRsp);
  // 重试文档审核
  // @alias=/RetryDocAudit
  rpc RetryDocAudit(RetryDocAuditReq) returns (RetryDocAuditRsp);

  // 获取Doc分类
  // @alias=/ListDocCate
  rpc ListDocCate(ListCateReq) returns (ListCateRsp);
  // 创建Doc分类
  // @alias=/CreateDocCate
  rpc CreateDocCate(CreateCateReq) returns (CreateCateRsp);
  // Doc分类修改
  // @alias=/ModifyDocCate
  rpc ModifyDocCate(ModifyCateReq) returns (ModifyCateRsp);
  // Doc分类删除
  // @alias=/DeleteDocCate
  rpc DeleteDocCate(DeleteCateReq) returns (DeleteCateRsp);
  // Doc分组
  // @alias=/GroupDoc
  rpc GroupDoc(GroupObjectReq) returns (GroupObjectRsp);

  // 获取Synonyms分类
  // @alias=/ListSynonymsCate
  rpc ListSynonymsCate(ListCateReq) returns (ListCateRsp);
  // 创建Synonyms分类
  // @alias=/CreateSynonymsCate
  rpc CreateSynonymsCate(CreateCateReq) returns (CreateCateRsp);
  // Synonyms分类修改
  // @alias=/ModifySynonymsCate
  rpc ModifySynonymsCate(ModifyCateReq) returns (ModifyCateRsp);
  // Synonyms分类删除
  // @alias=/DeleteSynonymsCate
  rpc DeleteSynonymsCate(DeleteCateReq) returns (DeleteCateRsp);
  // Synonyms分类批量操作
  // @alias=/GroupSynonyms
  rpc GroupSynonyms(GroupObjectReq) returns (GroupObjectRsp);

  // 获取QA分类
  // @alias=/ListQACate
  rpc ListQACate(ListQACateReq) returns (ListQACateRsp);
  // Deprecated 获取QA分类V1
  // @alias=/ListQACateV1
  rpc ListQACateV1(ListQACateV1Req) returns (ListQACateV1Rsp);
  // 创建QA分类
  // @alias=/CreateQACate
  rpc CreateQACate(CreateQACateReq) returns (CreateQACateRsp);
  // Deprecated 创建QA分类
  // @alias=/CreateQACateV1
  rpc CreateQACateV1(CreateQACateV1Req) returns (CreateQACateV1Rsp);
  // Deprecated QA分类修改
  // @alias=/UpdateQACate
  rpc UpdateQACate(UpdateQACateReq) returns (UpdateQACateRsp);
  // QA分类修改
  // @alias=/ModifyQACate
  rpc ModifyQACate(ModifyQACateReq) returns (ModifyQACateRsp);
  // QA分类删除
  // @alias=/DeleteQACate
  rpc DeleteQACate(DeleteQACateReq) returns (DeleteQACateRsp);
  // Deprecated 获取QA列表
  // @alias=/GetQAList
  rpc GetQAList(GetQAListReq) returns (GetQAListRsp);
  // 获取QA列表
  // @alias=/ListQA
  rpc ListQA(ListQAReq) returns (ListQARsp);
  // 生成相似问答对
  // @alias=/GenerateSimilarQuestions
  rpc GenerateSimilarQuestions(GenerateSimilarQuestionsReq) returns (GenerateSimilarQuestionsRsp);
  // 生成问答任务列表
  // @alias=/ListQaTask
  rpc ListQaTask(ListQaTaskReq) returns (ListQaTaskRsp);
  // 删除生成QA任务
  // @alias=/DeleteQaTask
  rpc DeleteQaTask(DeleteQaTaskReq) returns (DeleteQaTaskRsp);
  // 暂停或取消生成qa任务
  // @alias=/StopQaTask
  rpc StopQaTask(StopQaTaskReq) returns (StopQaTaskRsp);
  // 重试或继续生成qa任务
  // @alias=/RetryQaTask
  rpc RetryQaTask(RetryQaTaskReq) returns (RetryQaTaskRsp);
  // Deprecated 获取QA详情
  // @alias=/GetQADetail
  rpc GetQADetail(GetQADetailReq) returns (GetQADetailRsp);
  // 获取QA详情
  // @alias=/DescribeQA
  rpc DescribeQA(DescribeQAReq) returns (DescribeQARsp);
  // 新建QA
  // @alias=/CreateQA
  rpc CreateQA(CreateQAReq) returns (CreateQARsp);
  // 新建QA
  // @alias=/CreateQAV1
  rpc CreateQAV1(CreateQAV1Req) returns (CreateQAV1Rsp);
  // Deprecated 编辑QA
  // @alias=/UpdateQA
  rpc UpdateQA(UpdateQAReq) returns (UpdateQARsp);
  // 编辑QA
  // @alias=/ModifyQA
  rpc ModifyQA(ModifyQAReq) returns (ModifyQARsp);
  // 删除QA
  // @alias=/DeleteQA
  rpc DeleteQA(DeleteQAReq) returns (DeleteQARsp);
  // 删除QA
  // @alias=/DeleteQAV1
  rpc DeleteQAV1(DeleteQAV1Req) returns (DeleteQAV1Rsp);
  // 校验QA
  // @alias=/VerifyQA
  rpc VerifyQA(VerifyQAReq) returns (VerifyQARsp);
  // 校验QA
  // @alias=/VerifyQAV1
  rpc VerifyQAV1(VerifyQAV1Req) returns (VerifyQAV1Rsp);
  // QA分组
  // @alias=/GroupQA
  rpc GroupQA(GroupQAReq) returns (GroupQARsp);
  // 编辑QA适用范围
  // @alias=/ModifyQAAttrRange
  rpc ModifyQAAttrRange(ModifyQAAttrRangeReq) returns (ModifyQAAttrRangeRsp);
  // 批量修改问答过期时间
  // @alias=/BatchModifyQaExpire
  rpc BatchModifyQaExpire(BatchModifyQaExpireReq) returns (BatchModifyQaExpireRsp);
  // 批量修改问答关联文档
  // @alias=/BatchModifyQaDoc
  rpc BatchModifyQaDoc(BatchModifyQaDocReq) returns (BatchModifyQaDocRsp);
  // 编辑QA停用启用状态
  // @alias=/ModifyQAStatus
  rpc ModifyQAStatus(ModifyQAStatusReq) returns (ModifyQAStatusRsp);
  // 导出QA列表
  // @alias=/ExportQAList
  rpc ExportQAList(ExportQAListReq) returns (ExportQAListRsp);
  // Deprecated 导出QA列表
  // @alias=/ExportQAListV1
  rpc ExportQAListV1(ExportQAListReqV1) returns (ExportQAListRspV1);
  // 发布文档预览
  // @alias=/ListReleaseDocPreview
  rpc ListReleaseDocPreview(ListReleaseDocPreviewReq) returns (ListReleaseDocPreviewRsp);
  // 发布问答预览
  // @alias=/ListReleaseQAPreview
  rpc ListReleaseQAPreview(ListReleaseQAPreviewReq) returns (ListReleaseQAPreviewRsp);
  // 发布拒答问题预览
  // @alias=/ListRejectedQuestionPreview
  rpc ListRejectedQuestionPreview(ListRejectedQuestionPreviewReq) returns (ListRejectedQuestionPreviewRsp);
  // 是否存在未确认问答
  // @alias=/CheckUnconfirmedQa
  rpc CheckUnconfirmedQa(CheckUnconfirmedQaReq) returns (CheckUnconfirmedQaRsp);
  // Deprecated 拉取相似问答对
  // @alias=/GetQaSimilar
  rpc GetQaSimilar(GetQaSimilarReq) returns (GetQaSimilarRsp);
  // 拉取相似问答对
  // @alias=/ListQaSimilar
  rpc ListQaSimilar(ListQaSimilarReq) returns (ListQaSimilarRsp);
  // Deprecated 拉取相似问答对详情
  // @alias=/GetQaSimilarDetail
  rpc GetQaSimilarDetail(GetQaSimilarDetailReq) returns (GetQaSimilarDetailRsp);
  // 拉取相似问答对详情
  // @alias=/DescribeQaSimilar
  rpc DescribeQaSimilar(DescribeQaSimilarReq) returns (DescribeQaSimilarRsp);
  // 提交相似问答选择结果
  // @alias=/SubmitQaSimilar
  rpc SubmitQaSimilar(SubmitQaSimilarReq) returns (SubmitQaSimilarRsp);
  // Deprecated 获取来源详情
  // @alias=/GetReferDetail
  rpc GetReferDetail(GetReferDetailReq) returns (GetReferDetailRsp);
  // 获取来源详情
  // @alias=/DescribeRefer
  rpc DescribeRefer(DescribeReferReq) returns (DescribeReferRsp);
  // 来源打标
  // @alias=/MarkRefer
  rpc MarkRefer(MarkReferReq) returns (MarkReferRsp);
  // Deprecated 上传样本集合
  // @alias=/UploadSampleFile
  rpc UploadSampleFile(UploadSampleReq) returns (UploadSampleRsp);
  // 上传样本集合
  // @alias=/UploadSampleSet
  rpc UploadSampleSet(UploadSampleSetReq) returns (UploadSampleSetRsp);
  // 带校验上传样本集合
  // @alias=/UploadSampleSetWithCheck
  rpc UploadSampleSetWithCheck(UploadSampleSetWithCheckReq) returns (UploadSampleSetWithCheckRsp);
  // Deprecated 查询样本集列表
  // @alias=/QuerySampleSetList
  rpc QuerySampleSetList(QuerySampleReq) returns (QuerySampleRsp);
  // Deprecated 批量删除样本集
  // @alias=/DeleteSampleFiles
  rpc DeleteSampleFiles(DeleteSampleReq) returns (DeleteSampleRsp);
  // 查询样本集
  // @alias=/ListSampleSet
  rpc ListSampleSet(ListSampleSetReq) returns (ListSampleSetRsp);
  // 删除样本集
  // @alias=/DeleteSampleSet
  rpc DeleteSampleSet(DeleteSampleSetReq) returns (DeleteSampleSetRsp);
  // Deprecated 创建评测任务
  // @alias=/CreateTest
  rpc CreateTest(CreateTestReq) returns (CreateTestRsp);
  // 创建评测任务
  // @alias=/CreateEvaluateTest
  rpc CreateEvaluateTest(CreateEvaluateTestReq) returns (CreateEvaluateTestRsp);
  // Deprecated 条件查询任务列表
  // @alias=/QueryTestList
  rpc QueryTestList(QueryTestReq) returns (QueryTestRsp);
  // 查询任务列表
  // @alias=/ListEvaluateTest
  rpc ListEvaluateTest(ListEvaluateTestReq) returns (ListEvaluateTestRsp);
  // Deprecated 任务删除
  // @alias=/DeleteTest
  rpc DeleteTest(DeleteTestReq) returns (DeleteTestRsp);
  // Deprecated 任务停止
  // @alias=/StopTest
  rpc StopTest(StopTestReq) returns (StopTestRsp);
  // Deprecated 任务重试
  // @alias=/RetryTest
  rpc RetryTest(RetryTestReq) returns (RetryTestRsp);
  // 任务删除
  // @alias=/DeleteEvaluateTest
  rpc DeleteEvaluateTest(DeleteEvaluateTestReq) returns (DeleteEvaluateTestRsp);
  // 任务停止
  // @alias=/StopEvaluateTest
  rpc StopEvaluateTest(StopEvaluateTestReq) returns (StopEvaluateTestRsp);
  // 任务重试
  // @alias=/RetryEvaluateTest
  rpc RetryEvaluateTest(RetryEvaluateTestReq) returns (RetryEvaluateTestRsp);
  // Deprecated 待标注测试记录详情
  // @alias=/GetOneWaitJudging
  rpc GetOneWaitJudging(GetOneJudgingReq) returns (GetOneJudgingRsp);
  // 待标注测试记录详情
  // @alias=/DescribeWaitJudgeRecord
  rpc DescribeWaitJudgeRecord(DescribeWaitJudgeRecordReq) returns (DescribeWaitJudgeRecordRsp);
  // Deprecated 查询标注记录详情
  // @alias=/GetRecord
  rpc GetRecord(GetRecordReq) returns (GetRecordRsp);
  // 查询标注记录详情
  // @alias=/DescribeRecord
  rpc DescribeRecord(DescribeRecordReq) returns (DescribeRecordRsp);
  // 标注会话
  // @alias=/JudgeRecord
  rpc JudgeRecord(JudgeReq) returns (JudgeRsp);
  // 导出评测任务
  // @alias=/ExportEvaluateTask
  rpc ExportEvaluateTask(ExportEvaluateTaskReq) returns (ExportEvaluateTaskRsp);
  // Deprecated 获取拒答问题列表
  // @alias=/GetRejectedQuestionList
  rpc GetRejectedQuestionList(GetRejectedQuestionListReq) returns (GetRejectedQuestionListRsp);
  // 获取拒答问题列表
  // @alias=/ListRejectedQuestion
  rpc ListRejectedQuestion(ListRejectedQuestionReq) returns (ListRejectedQuestionRsp);
  // 创建拒答问题
  // @alias=/CreateRejectedQuestion
  rpc CreateRejectedQuestion(CreateRejectedQuestionReq) returns (CreateRejectedQuestionRsp);
  // Deprecated 修改拒答问题
  // @alias=/UpdateRejectedQuestion
  rpc UpdateRejectedQuestion(UpdateRejectedQuestionReq) returns (UpdateRejectedQuestionRsp);
  // 修改拒答问题
  // @alias=/ModifyRejectedQuestion
  rpc ModifyRejectedQuestion(ModifyRejectedQuestionReq) returns (ModifyRejectedQuestionRsp);
  // 删除拒答问题
  // @alias=/DeleteRejectedQuestion
  rpc DeleteRejectedQuestion(DeleteRejectedQuestionReq) returns (DeleteRejectedQuestionRsp);
  // 导出拒答问题
  // @alias=/ExportRejectedQuestion
  rpc ExportRejectedQuestion(ExportRejectedQuestionReq) returns (ExportRejectedQuestionRsp);
  // Deprecated 获取不满意回复
  // @alias=/GetUnsatisfiedReply
  rpc GetUnsatisfiedReply(GetUnsatisfiedReplyReq) returns (GetUnsatisfiedReplyRsp);
  // 获取不满意回复
  // @alias=/ListUnsatisfiedReply
  rpc ListUnsatisfiedReply(ListUnsatisfiedReplyReq) returns (ListUnsatisfiedReplyRsp);
  // 忽略不满意回复
  // @alias=/IgnoreUnsatisfiedReply
  rpc IgnoreUnsatisfiedReply(IgnoreUnsatisfiedReplyReq) returns (IgnoreUnsatisfiedReplyRsp);
  // 导出不满意回复
  // @alias=/ExportUnsatisfiedReply
  rpc ExportUnsatisfiedReply(ExportUnsatisfiedReplyReq) returns (ExportUnsatisfiedReplyRsp);
  // Deprecated 获取不满意回复上下文
  // @alias=/GetUnsatisfiedReplyContext
  rpc GetUnsatisfiedReplyContext(GetUnsatisfiedReplyContextReq) returns (GetUnsatisfiedReplyContextRsp);
  // 获取不满意回复上下文
  // @alias=/DescribeUnsatisfiedReplyContext
  rpc DescribeUnsatisfiedReplyContext(DescribeUnsatisfiedReplyReq) returns (DescribeUnsatisfiedReplyRsp);
  // 记录操作首次生成问答标记
  // @alias=/RecordUserFirstGenQA
  rpc RecordUserFirstGenQA(RecordUserFirstGenQAReq) returns (RecordUserFirstGenQARsp);
  // 记录访问未检验问答时间
  // @alias=/RecordUserAccessUnCheckQATime
  rpc RecordUserAccessUnCheckQATime(RecordUserAccessUnCheckQATimeReq) returns (RecordUserAccessUnCheckQATimeRsp);
  // 创建属性标签
  // @alias=/CreateAttributeLabelV1
  rpc CreateAttributeLabelV1(CreateAttributeLabelV1Req) returns (CreateAttributeLabelV1Rsp);
  // 创建属性标签
  // @alias=/CreateAttributeLabel
  rpc CreateAttributeLabel(CreateAttributeLabelReq) returns (CreateAttributeLabelRsp);
  // 删除属性标签
  // @alias=/DeleteAttributeLabel
  rpc DeleteAttributeLabel(DeleteAttributeLabelReq) returns (DeleteAttributeLabelRsp);
  // Deprecated 编辑属性标签
  // @alias=/UpdateAttributeLabel
  rpc UpdateAttributeLabel(UpdateAttributeLabelReq) returns (UpdateAttributeLabelRsp);
  // 编辑属性标签
  // @alias=/ModifyAttributeLabel
  rpc ModifyAttributeLabel(ModifyAttributeLabelReq) returns (ModifyAttributeLabelRsp);
  // Deprecated 查询属性标签列表
  // @alias=/GetAttributeLabelList
  rpc GetAttributeLabelList(GetAttributeLabelListReq) returns (GetAttributeLabelListRsp);
  // 查询属性标签列表
  // @alias=/ListAttributeLabel
  rpc ListAttributeLabel(ListAttributeLabelReq) returns (ListAttributeLabelRsp);
  // Deprecated 查询属性标签详情
  // @alias=/GetAttributeLabelDetail
  rpc GetAttributeLabelDetail(GetAttributeLabelDetailReq) returns (GetAttributeLabelDetailRsp);
  // 查询属性标签详情
  // @alias=/DescribeAttributeLabel
  rpc DescribeAttributeLabel(DescribeAttributeLabelReq) returns (DescribeAttributeLabelRsp);
  // 导入属性标签
  // @alias=/UploadAttributeLabel
  rpc UploadAttributeLabel(UploadAttributeLabelReq) returns (UploadAttributeLabelRsp);
  // 导出属性标签
  // @alias=/ExportAttributeLabel
  rpc ExportAttributeLabel(ExportAttributeLabelReq) returns (ExportAttributeLabelRsp);
  // 检查属性下标签是否引用
  // @alias=/CheckAttributeLabelRefer
  rpc CheckAttributeLabelRefer(CheckAttributeLabelReferReq) returns (CheckAttributeLabelReferRsp);
  // 检查属性下的标签名是否存在请求
  // @alias=/CheckAttributeLabelExist
  rpc CheckAttributeLabelExist(CheckAttributeLabelExistReq) returns (CheckAttributeLabelExistRsp);
  // 提交申诉请求申请人工审核
  // @alias=/CreateAppeal
  rpc CreateAppeal(CreateAppealReq) returns (CreateAppealRsp);

  // 同义词
  // 同义词列表
  // @alias=/ListSynonyms
  rpc ListSynonyms(ListSynonymsReq) returns (ListSynonymsRsp);
  // 新增同义词
  // @alias=/CreateSynonyms
  rpc CreateSynonyms(CreateSynonymsReq) returns (CreateSynonymsRsp);
  // 删除同义词
  // @alias=/DeleteSynonyms
  rpc DeleteSynonyms(DeleteSynonymsReq) returns (DeleteSynonymsRsp);
  // 修改同义词
  // @alias=/ModifySynonyms
  rpc ModifySynonyms(ModifySynonymsReq) returns (ModifySynonymsRsp);
  // 导入同义词
  // @alias=/UploadSynonymsList
  rpc UploadSynonymsList(UploadSynonymsListReq) returns (UploadSynonymsListRsp);
  // 导出同义词
  // @alias=/ExportSynonymsList
  rpc ExportSynonymsList(ExportSynonymsListReq) returns (ExportSynonymsListRsp);

  // 获取知识库知识个数
  // @alias=/GetAppKnowledgeCount
  rpc GetAppKnowledgeCount(GetAppKnowledgeCountReq) returns (GetAppKnowledgeCountRsp);

  // 获取任务状态
  // @alias=/GetTaskStatus
  rpc GetTaskStatus(GetTaskStatusReq) returns (GetTaskStatusRsp);

  // 获取不展示的角色模型列表
  // @alias=/ListNonRoleModel
  rpc ListNonRoleModel(ListNonRoleModelReq) returns (ListNonRoleModelRsp);

  // 点踩点赞数据统计
  // @alias=/GetLikeDataCount
  rpc GetLikeDataCount(GetLikeDataCountReq) returns (GetLikeDataCountRsp);

  // 回答类型数据统计
  // @alias=/GetAnswerTypeDataCount
  rpc GetAnswerTypeDataCount(GetAnswerTypeDataCountReq) returns (GetAnswerTypeDataCountRsp);

  // 获取账号下字符使用量与容量
  // @alias=/GetCharacterUsage
  rpc GetCharacterUsage(GetCharacterUsageReq) returns (GetCharacterUsageRsp);

  // 恢复文档
  // @alias=/ResumeDoc
  rpc ResumeDoc(ResumeDocReq) returns (ResumeDocRsp);

  // 恢复文档
  // @alias=/ResumeQA
  rpc ResumeQA(ResumeQAReq) returns (ResumeQARsp);

  // 文档重命名
  // @alias=/RenameDoc
  rpc RenameDoc(RenameDocReq) returns (RenameDocRsp);

  // ---------- 文档diff相关接口 -------------
  // 获取文档diff列表
  // @alias=/ListDocDiffTask
  rpc ListDocDiffTask(ListDocDiffTaskReq) returns (ListDocDiffTaskRsp);
  // 创建文档diff任务
  // @alias=/CreateDocDiffTask
  rpc CreateDocDiffTask(CreateDocDiffTaskReq) returns (CreateDocDiffTaskRsp);
  // 获取文档diff任务详情
  // @alias=/DescribeDocDiffTask
  rpc DescribeDocDiffTask(DescribeDocDiffTaskReq) returns (DescribeDocDiffTaskRsp);
  // 删除文档diff
  // @alias=/DeleteDocDiffTask
  rpc DeleteDocDiffTask(DeleteDocDiffTaskReq) returns (DeleteDocDiffTaskRsp);
  // 处理文档diff任务
  // @alias=/HandleDocDiffTask
  rpc HandleDocDiffTask(HandleDocDiffTaskReq) returns (HandleDocDiffTaskRsp);
  // 获取文档diff结果
  // @alias=/ListDocDiffData
  rpc ListDocDiffData(ListDocDiffDataReq) returns (ListDocDiffDataRsp);
  // ---------- 文档diff相关接口 -------------

  // ---------- 共享知识库接口 START -------------

  // 创建共享知识库
  // @alias=/CreateSharedKnowledge
  rpc CreateSharedKnowledge(trpc.KEP.knowledge.CreateSharedKnowledgeReq) returns (
  trpc.KEP.knowledge.CreateSharedKnowledgeRsp);

  // 删除共享知识库
  // @alias=/DeleteSharedKnowledge
  rpc DeleteSharedKnowledge(trpc.KEP.knowledge.DeleteSharedKnowledgeReq) returns (
  trpc.KEP.knowledge.DeleteSharedKnowledgeRsp);

  // 列举共享知识库
  // @alias=/ListSharedKnowledge
  rpc ListSharedKnowledge(trpc.KEP.knowledge.ListSharedKnowledgeReq) returns (
  trpc.KEP.knowledge.ListSharedKnowledgeRsp);

  // 查询共享知识库
  // @alias=/DescribeSharedKnowledge
  rpc DescribeSharedKnowledge(trpc.KEP.knowledge.DescribeSharedKnowledgeReq) returns (
  trpc.KEP.knowledge.DescribeSharedKnowledgeRsp);

  // 更新共享知识库
  // @alias=/UpdateSharedKnowledge
  rpc UpdateSharedKnowledge(trpc.KEP.knowledge.UpdateSharedKnowledgeReq) returns (
  trpc.KEP.knowledge.UpdateSharedKnowledgeRsp);

  // ---------- 共享知识库接口 END -------------
  
  // ---------- 知识库权限校验相关接口 -------------
  // 校验知识库权限
  // @alias=/CheckKnowledgePermission
  rpc CheckKnowledgePermission(CheckKnowledgePermissionReq) returns (CheckKnowledgePermissionRsp);
  // 获取应用端权限配置状态
  // @alias=/GetAclConfigStatus
  rpc GetAclConfigStatus(GetAclConfigStatusReq) returns (GetAclConfigStatusRsp);
  // 新增用户
  // @alias=/CreateCustUser
  rpc CreateCustUser(CreateCustUserReq) returns (CreateCustUserRsp);
  // 编辑用户
  // @alias=/ModifyCustUser
  rpc ModifyCustUser(ModifyCustUserReq) returns (ModifyCustUserRsp);
  // 批量编辑用户
  // @alias=/BatchModifyUser
  rpc BatchModifyUser(BatchModifyUserReq) returns (BatchModifyUserRsp);
  // 获取用户列表
  // @alias=/ListCustUser
  rpc ListCustUser(ListCustUserReq) returns (ListCustUserRsp);
  // 获取用户详情
  // @alias=/DescribeCustUser
  rpc DescribeCustUser(DescribeCustUserReq) returns (DescribeCustUserRsp);
  // 删除用户
  // @alias=/DeleteCustUser
  rpc DeleteCustUser(DeleteCustUserReq) returns (DeleteCustUserRsp);
  // 设置特殊权限配置
  // @alias=/SetCustUserConfig
  rpc SetCustUserConfig(SetCustUserConfigReq) returns (SetCustUserConfigRsp);
  // 获取特殊权限配置
  // @alias=/GetCustUserConfig
  rpc GetCustUserConfig(GetCustUserConfigReq) returns (GetCustUserConfigRsp);
  // 设置外部权限接口配置
  // @alias=/SetThirdAclConfig
  rpc SetThirdAclConfig(SetThirdAclConfigReq) returns (SetThirdAclConfigRsp);
  // 获取外部权限接口配置
  // @alias=/GetThirdAclConfig
  rpc GetThirdAclConfig(GetThirdAclConfigReq) returns (GetThirdAclConfigRsp);
  // 新增角色
  // @alias=/CreateKnowledgeRole
  rpc CreateKnowledgeRole(CreateRoleReq) returns (CreateRoleRsp);
  // 保存角色
  // @alias=/ModifyKnowledgeRole
  rpc ModifyKnowledgeRole(ModifyReq) returns (ModifyRsp);
  // 获取角色详情
  // @alias=/DescribeKnowledgeRole
  rpc DescribeKnowledgeRole(DescribeKnowledgeRoleReq) returns (DescribeKnowledgeRoleRsp);
  // 获取角色选择的特定知识
  // @alias=/DescribeRoleSearch
  rpc DescribeRoleSearch(DescribeRoleSearchReq) returns (DescribeRoleSearchRsp);
  // 获取角色列表
  // @alias=/ListKnowledgeRole
  rpc ListKnowledgeRole(ListRoleReq) returns (ListRoleRsp);
  // 删除角色前检查引用
  // @alias=/CheckDeleteRole
  rpc CheckDeleteRole(CheckDeleteRoleReq) returns (CheckDeleteRoleRsp);
  // 删除角色
  // @alias=/DeleteKnowledgeRole
  rpc DeleteKnowledgeRole(DeleteRoleReq) returns (DeleteRoleRsp);
  // ---------- 知识库权限校验相关接口 -------------

  // ------------- 外部的数据源管理 -------------
  // 数据源，连通性
  // @alias=/TestDbSourceConnection
  rpc TestDbSourceConnection(DbSourceConnectionReq) returns (TestDbSourceConnectionRsp);

  // 数据源，下的表
  // @alias=/ListSourceTableNames
  rpc ListSourceTableNames(ListTablesReq) returns (ListTablesRsp);

  // 新增数据库
  // @alias=/AddDbSource
  rpc AddDbSource(AddDbSourceReq) returns (AddDbSourceRsp);

  // 删除数据库
  // @alias=/DeleteDbSource
  rpc DeleteDbSource(DeleteDbSourceReq) returns (DeleteDbSourceRsp);

  // 更新数据库
  // @alias=/UpdateDbSource
  rpc UpdateDbSource(UpdateDbSourceReq) returns (UpdateDbSourceRsp);

  // 获取单一应用下数据库
  // @alias=/ListDbSource
  rpc ListDbSource(ListDbSourceReq) returns (ListDbSourceRsp);

  // 获取数据库详情
  // @alias=/GetDbSource
  rpc GetDbSource(GetDbSourceReq) returns (GetDbSourceRsp);


  // 删除单一表数据
  // @alias=/DeleteDbTable
  rpc DeleteDbTable(DeleteDbTableReq) returns (DeleteDbTableRsp);

  // 查询单一表数据
  // @alias=/GetDbTable
  rpc GetDbTable(GetDbTableReq) returns (GetDbTableRsp);

  // 分页查询表数据
  // @alias=/ListDbTable
  rpc ListDbTable(ListDbTableReq) returns (ListDbTableRsp);

  // 获取表格下的列数据
  // @alias=/ListDbTableColumn
  rpc ListDbTableColumn(ListDbTableColumnReq) returns (ListDbTableColumnRsp);

  // 更新表和列数据
  // @alias=/UpdateDbTableAndColumns
  rpc UpdateDbTableAndColumns(UpdateDbTableAndColumnsReq) returns (UpdateDbTableAndColumnsResp);

  // 预览外部数据库表格下的列数据
  // @alias=/PreviewTable
  rpc PreviewTable (PreviewTableReq) returns (PreviewTableRsp);

  // 发布数据库查看
  // @alias=/ListReleaseDb
  rpc ListReleaseDb(ListReleaseDbReq) returns (ListReleaseDbRsp);

  // 发布数据表查看
  // @alias=/ListReleaseDbTable
  rpc ListReleaseDbTable(ListReleaseDbDbTableReq) returns (ListReleaseDbDbTableRsp);

  // 获取公钥
  // @alias=/GetDbSourcePublicKey
  rpc GetDbSourcePublicKey(GetDbSourcePublicKeyReq) returns (GetDbSourcePublicKeyRsp);

  // ------------- 外部的数据源管理 -------------

  // ------------- 解析干预 -------------


  // text2sql 预览外部数据库表格下的列数据
  // @alias=/Text2SqlPreviewTable
  rpc Text2SqlPreviewTable (Text2SqlPreviewTableReq) returns (Text2SqlPreviewTableRsp);

  // text2sql 获取text2sql 列描述
  // @alias=/Text2SqlGetColumns
  rpc Text2SqlGetColumns(GetText2SqlColumnsReq) returns (GetText2SqlColumnsRsp);

  // text2sql 更新列描述数据
  // @alias=/UpdateText2SqlColumns
  rpc UpdateText2SqlColumns(UpdateText2SqlColumnsReq) returns (UpdateText2SqlColumnsRsp);

  // ------------- 解析干预 -------------


  // ---------- KBAgent相关接口 -------------
  // 生成知识库schema
  // @alias=/GenerateKnowledgeSchema
  rpc GenerateKnowledgeSchema(GenerateKnowledgeSchemaReq) returns (GenerateKnowledgeSchemaRsp);
  // 获取知识库schema任务信息
  // @alias=/GetKnowledgeSchemaTask
  rpc GetKnowledgeSchemaTask(GetKnowledgeSchemaTaskReq) returns (GetKnowledgeSchemaTaskRsp);
  // 获取知识库schema
  // @alias=/GetKnowledgeSchema
  rpc GetKnowledgeSchema(GetKnowledgeSchemaReq) returns (GetKnowledgeSchemaRsp);
  // 设置知识库配置
  // @alias=/SetKnowledgeConfig
  rpc SetKnowledgeConfig(SetKnowledgeConfigReq) returns (SetKnowledgeConfigRsp);
  // 获取知识库配置
  // @alias=/GetKnowledgeConfig
  rpc GetKnowledgeConfig(GetKnowledgeConfigReq) returns (GetKnowledgeConfigRsp);
  // ---------- KBAgent相关接口 -------------
}

// Api接口 不需要鉴权
service Api {
  // 更新QA/Segment状态回调
  // @alias=/ReleaseDetailNotify
  rpc ReleaseDetailNotify(ReleaseDetailNotifyReq) returns (ReleaseDetailNotifyRsp);
  // 更新发布任务回调
  // @alias=/ReleaseNotify
  rpc ReleaseNotify(ReleaseNotifyReq) returns (ReleaseNotifyRsp);
  // 获取临时链接
  // @alias=/GetPresignedURL
  rpc GetPresignedURL(GetPresignedURLReq) returns (GetPresignedURLRsp);
  // 获取临时链接,内部调用
  // @alias=/GetPresignedURLNoCheck
  rpc GetPresignedURLNoCheck(GetPresignedURLReq) returns (GetPresignedURLRsp);
  // 对话评测
  // @alias=/SearchPreview
  rpc SearchPreview(SearchPreviewReq) returns (SearchPreviewRsp);
  // 向量特征检索
  // @alias=/Search
  rpc Search(SearchReq) returns (SearchRsp);
  // 向量特征检索，需要做鉴权(后续接口弃用，推动接入SearchKnowledgeRelease)
  // @alias=/SearchRelease
  rpc SearchRelease(SearchReleaseReq) returns (SearchReleaseRsp);
  // 向量特征检索，需要做鉴权
  // @alias=/SearchKnowledgeRelease
  rpc SearchKnowledgeRelease(SearchKnowledgeReleaseReq) returns (SearchKnowledgeReleaseRsp);
  // 对话评测
  // @alias=/CustomSearchPreview
  rpc CustomSearchPreview(CustomSearchPreviewReq) returns (CustomSearchPreviewRsp);
  // 查找
  // @alias=/CustomSearch
  rpc CustomSearch(CustomSearchReq) returns (CustomSearchRsp);
  // 匹配来源
  // @alias=/MatchRefer
  rpc MatchRefer(MatchReferReq) returns (MatchReferRsp);
  // 审核回调
  // @alias=/AuditResultCallback
  rpc AuditResultCallback(trpc.KEP.knowledge.CheckResultReq) returns (trpc.KEP.knowledge.CheckResultRsp);
  // 解析任务结果回调
  // @alias=/FileParserCallback
  rpc FileParserCallback(trpc.KEP.knowledge.FileParserCallbackReq) returns (trpc.KEP.knowledge.FileParserCallbackRes);
  // 获取文档内容
  // @alias=/GetDocs
  rpc GetDocs(GetDocsReq) returns (GetDocsRsp);
  // 干预文档解析/拆分结果
  rpc DocParsingIntervention(DocParsingInterventionReq) returns (DocParsingInterventionRsp);

  // 拒答问题测评库查询
  // @alias=/SearchPreviewRejectedQuestion
  rpc SearchPreviewRejectedQuestion(SearchPreviewRejectedQuestionReq) returns (SearchPreviewRejectedQuestionRsp);
  // 拒答问题线上库查询
  // @alias=/SearchReleaseRejectedQuestion
  rpc SearchReleaseRejectedQuestion(SearchReleaseRejectedQuestionReq) returns (SearchReleaseRejectedQuestionRsp);
  // 获取拒答问题列表
  // @alias=/ListRejectedQuestion
  rpc ListRejectedQuestion(ListRejectedQuestionReq) returns (ListRejectedQuestionRsp);
  // 添加不满意回复
  // @alias=/AddUnsatisfiedReply
  rpc AddUnsatisfiedReply(AddUnsatisfiedReplyReq) returns (AddUnsatisfiedReplyRsp);
  // 获取QA列表
  // @alias=/ListQA
  rpc ListQA(ListQAReq) returns (ListQARsp);

  // 获取admin任务列表
  // @alias=/GetAdminTaskList
  rpc GetAdminTaskList(GetAdminTaskListReq) returns (GetAdminTaskListRsp);
  // 获取admin历史任务列表
  // @alias=/GetAdminTaskHistoryList
  rpc GetAdminTaskHistoryList(GetAdminTaskHistoryListReq) returns (GetAdminTaskHistoryListRsp);
  // 获取获取vector_doc任务列表
  // @alias=/GetVectorDocTaskList
  rpc GetVectorDocTaskList(GetVectorDocTaskListReq) returns (GetVectorDocTaskListRsp);
  // 获取vector_doc任务历史列表
  // @alias=/GetVectorDocTaskHistoryList
  rpc GetVectorDocTaskHistoryList(GetVectorDocTaskHistoryListReq) returns (GetVectorDocTaskHistoryListRsp);

  // 更新审核单状态
  // @alias=/UpdateAuditStatus
  rpc UpdateAuditStatus(UpdateAuditStatusReq) returns (UpdateAuditStatusRsp);
  // 全局干预知识列表
  // @alias=/ListGlobalKnowledge
  rpc ListGlobalKnowledge(ListGlobalKnowledgeReq) returns (ListGlobalKnowledgeRsp);
  // 添加全局干预知识
  // @alias=/AddGlobalKnowledge
  rpc AddGlobalKnowledge(AddGlobalKnowledgeReq) returns (AddGlobalKnowledgeRsp);
  // 删除全局干预知识
  // @alias=/DelGlobalKnowledge
  rpc DelGlobalKnowledge(DelGlobalKnowledgeReq) returns (DelGlobalKnowledgeRsp);
  // 更新全局干预知识
  // @alias=/UpdGlobalKnowledge
  rpc UpdGlobalKnowledge(UpdGlobalKnowledgeReq) returns (UpdGlobalKnowledgeRsp);
  // 全局干预知识
  // @alias=/GlobalKnowledge
  rpc GlobalKnowledge(GlobalKnowledgeReq) returns (GlobalKnowledgeRsp);
  // 强制同步全局干预知识
  // @alias=/ForceSyncGlobalKnowledge
  rpc ForceSyncGlobalKnowledge(ForceSyncGlobalKnowledgeReq) returns (ForceSyncGlobalKnowledgeRsp);
  // 计算相似度
  // @alias=/CustomSimilarity
  rpc CustomSimilarity(CustomSimilarityReq) returns (CustomSimilarityRsp);
  // 底座获取资源列表
  // @alias=/GetCustomResource
  rpc GetCustomResource(trpc.KEP.knowledge.GetCustomResourceReq) returns (trpc.KEP.knowledge.GetCustomResourceRsp);
  // 创建通知
  // @alias=/CreateNotice
  rpc CreateNotice(CreateNoticeReq) returns (CreateNoticeRsp);
  // 获取意图
  // @alias=/GetIntent
  rpc GetIntent(GetIntentReq) returns (GetIntentRsp);
  // 获取意图列表
  // @alias=/ListIntent
  rpc ListIntent(ListIntentReq) returns (ListIntentRsp);
  // 创建意图
  // @alias=/CreateIntent
  rpc CreateIntent(CreateIntentReq) returns (CreateIntentRsp);
  // 更新意图
  // @alias=/UpdateIntent
  rpc UpdateIntent(UpdateIntentReq) returns (UpdateIntentRsp);
  // 删除意图
  // @alias=/DeleteIntent
  rpc DeleteIntent(DeleteIntentReq) returns (DeleteIntentRsp);
  // 获取策略绑定的意图列表
  // @alias=/ListIntentByPolicyID
  rpc ListIntentByPolicyID(ListIntentByPolicyIDReq) returns (ListIntentByPolicyIDRsp);
  // 获取策略列表
  // @alias=/ListIntentPolicy
  rpc ListIntentPolicy(ListIntentPolicyReq) returns (ListIntentPolicyRsp);
  // 创建策略
  // @alias=/CreateIntentPolicy
  rpc CreateIntentPolicy(CreateIntentPolicyReq) returns (CreateIntentPolicyRsp);
  // 更新策略
  // @alias=/UpdateIntentPolicy
  rpc UpdateIntentPolicy(UpdateIntentPolicyReq) returns (UpdateIntentPolicyRsp);
  // 删除策略
  // @alias=/DeleteIntentPolicy
  rpc DeleteIntentPolicy(DeleteIntentPolicyReq) returns (DeleteIntentPolicyRsp);
  // 获取未使用的意图列表
  // @alias=/ListUnusedIntentKeyMap
  rpc ListUnusedIntentKeyMap(ListUnusedIntentKeyMapReq) returns (ListUnusedIntentKeyMapRsp);
  // 获取策略列表映射关系
  // @alias=/ListIntentPolicyKeyMap
  rpc ListIntentPolicyKeyMap(ListIntentPolicyKeyMapReq) returns (ListIntentPolicyKeyMapRsp);
  // 新增企业自定义模型
  // @alias=/CreateCorpCustomModel
  rpc CreateCorpCustomModel(CreateCorpCustomModelReq) returns (CreateCorpCustomModelRsp);
  // 获取应用检索配置
  // @alias=/GetRobotRetrievalConfig
  rpc GetRobotRetrievalConfig(GetRobotRetrievalConfigReq) returns (GetRobotRetrievalConfigRsp);
  // 保存应用检索配置
  // @alias=/SaveRobotRetrievalConfig
  rpc SaveRobotRetrievalConfig(SaveRobotRetrievalConfigReq) returns (SaveRobotRetrievalConfigRsp);

  // 删除文档切片图片 -- 仅vector服务调用
  // @alias=/DeleteDocSegmentImages
  rpc DeleteDocSegmentImages(DeleteDocSegmentImagesReq) returns (DeleteDocSegmentImagesRsp);

  // ---------- 实时文档相关接口 ----------
  // 实时文档解析（流式接口）
  rpc StreamSaveDoc(stream trpc.KEP.knowledge.StreamSaveDocReq) returns (stream trpc.KEP.knowledge.StreamSaveDocRsp);

  // 获取文档全文
  // @alias=/GetDocFullText
  rpc GetDocFullText(trpc.KEP.knowledge.GetDocFullTextReq) returns (trpc.KEP.knowledge.GetDocFullTextRsp);

  // 实时文档检索
  // @alias=/SearchRealtime
  rpc SearchRealtime(trpc.KEP.knowledge.SearchRealtimeReq) returns (trpc.KEP.knowledge.SearchRealtimeRsp);

  // 实时文档删除
  // @alias=/DeleteRealtimeDoc
  rpc DeleteRealtimeDoc(trpc.KEP.knowledge.DeleteRealtimeDocReq) returns (trpc.KEP.knowledge.DeleteRealtimeDocRsp);

  // 获取文档摘要接口
  // @alias=/GetDocSummary
  rpc GetDocSummary(trpc.KEP.knowledge.GetDocSummaryReq) returns (stream trpc.KEP.knowledge.GetDocSummaryRsp);

  // ---------- 实时文档相关接口 ----------

  // ---------- 计费相关接口 -------------

  // 通知知识库容量到期
  // @alias=/NotifyKnowledgeCapacityExpired
  rpc NotifyKnowledgeCapacityExpired(NotifyKnowledgeCapacityExpiredReq) returns (NotifyKnowledgeCapacityExpiredRsp);

  // 获取企业下应用字符数使用情况
  // @alias=/GetCorpCharacterUsage
  rpc GetCorpCharacterUsage(GetCorpCharacterUsageReq) returns (GetCorpCharacterUsageRsp);

  // ---------- 计费相关接口 -------------

  // ---------- 检索相关接口 -------------

  // 知识库检索
  // @alias=/SearchKnowledge
  rpc SearchKnowledge(trpc.KEP.knowledge.SearchKnowledgeReq) returns (trpc.KEP.knowledge.SearchKnowledgeRsp);

  // 知识库检索(支持同时检索多个知识库)
  // @alias=/SearchKnowledgeBatch
  rpc SearchKnowledgeBatch(trpc.KEP.knowledge.SearchKnowledgeBatchReq) returns (trpc.KEP.knowledge.SearchKnowledgeRsp);

  // ---------- 检索相关接口 -------------

  // ---------- 标签相关接口 -------------

  // 更新发布端标签缓存
  rpc UpdateAttrLabelsCacheProd(UpdateAttrLabelsCacheProdReq) returns (UpdateAttrLabelsCacheProdRsp);

  // 查询标签信息
  // @alias=/GetAttributeInfo
  rpc GetAttributeInfo(GetAttributeInfoReq) returns (GetAttributeInfoRsp);
  // ---------- 标签相关接口 -------------

  // ---------- 共享知识库相关接口 START -------------

  // 批量获取共享知识库
  // @alias=/BatchGetSharedKnowledge
  rpc BatchGetSharedKnowledge(trpc.KEP.knowledge.BatchGetSharedKnowledgeReq) returns (
  trpc.KEP.knowledge.BatchGetSharedKnowledgeRsp);

  // 查看引用共享知识库列表
  // @alias=/ListReferShareKnowledge
  rpc ListReferShareKnowledge(trpc.KEP.knowledge.ListReferSharedKnowledgeReq) returns(trpc.KEP.knowledge.ListReferSharedKnowledgeRsp);

  // ---------- 共享知识库相关接口 END -------------

  // 检查自定义参数是否被使用
  rpc CheckVarIsUsed(CheckVarIsUsedReq) returns (CheckVarIsUsedRsp);
  // 修改应用检索范围自定义参数
  rpc ModifyAppVar(ModifyAppVarReq) returns (ModifyAppVarRsp);

  // 申诉回调接口
  rpc AppealCallback(AppealCallbackReq) returns (AppealCallbackRsp);

  // ---------- 同义词相关接口 -------------
  // NER同义词替换接口
  // @alias=/SynonymsNER
  rpc SynonymsNER(SynonymsNERReq) returns (SynonymsNERRsp);
  // ---------- 同义词相关接口 -------------

  // ---------- 文档相关接口 -------------

  // 批量获取文档详情（内部接口）
  // @alias=/InnerDescribeDocs
  rpc InnerDescribeDocs(InnerDescribeDocsReq) returns (InnerDescribeDocsRsp);

  // ---------- 文档相关接口 -------------


  // ---------- 应用相关接口 -------------

  // 清理应用知识库资源
  // @alias=/ClearAppKnowledgeResource
  rpc ClearAppKnowledgeResource(ClearAppKnowledgeResourceReq) returns (ClearAppKnowledgeResourceRsp);

  // ---------- 文档相关接口 -------------

  // ---------- KBAgent相关接口 -------------
  // 获取知识库schema
  // @alias=/GetKnowledgeSchema
  rpc GetKnowledgeSchema(GetKnowledgeSchemaReq) returns (GetKnowledgeSchemaRsp);
  // ---------- KBAgent相关接口 -------------

  // ------------- 外部的数据源管理 -------------
  // 获取外部数据库模块未发布的数量
  // @alias=/GetUnreleasedDbCount
  rpc GetUnreleasedDbCount(GetUnreleasedDbCountReq) returns (GetUnreleasedDbCountRsp);

  // 发送发布数据库任务事件, 任务采集、任务发布、任务暂停重试
  // @alias=/SendPublishDbTaskEvent
  rpc SendPublishDbTaskEvent(SendPublishDbTaskEventReq) returns (SendPublishDbTaskEventRsp);

  // 获取发布数据库任务详情
  // @alias=/GetPublishDbTask
  rpc GetPublishDbTask(GetPublishDbTaskReq) returns (GetPublishDbTaskRsp);

  // ------------- 外部的数据源管理 -------------
}

// 占位符
message Placeholder {
  // 占位符
  string key = 1;
  // 占位符内容
  string value = 2;
}

// 获取对象存储临时密钥请求
message DescribeStorageCredentialReq {
  // 机器人ID
  string bot_biz_id = 1;
  // 文件类型
  string file_type = 2;
  // 区分场景，是否公有场景
  bool is_public = 3;
  // 区分类型: offline:离线文件，realtime:实时文件；为空默认为offline
  string type_key = 4;
}

// 获取对象存储临时密钥响应
message DescribeStorageCredentialRsp {
  message Credentials {
    // token
    string token = 1;
    // 临时证书密钥 ID
    string tmp_secret_id = 2;
    // 临时证书密钥 Key
    string tmp_secret_key = 3;
  }
  // 密钥信息
  Credentials credentials = 1;
  // 失效时间
  uint32 expired_time = 2;
  // 起始时间
  uint32 start_time = 3;
  // 对象存储 桶
  string bucket = 4;
  // 对象存储 可用区
  string region = 5;
  // 文件目录
  string file_path = 6;
  // 主号
  uint64 corp_uin = 7;
  // 存储类型
  string type = 8;
  // 图片目录
  string image_path = 9;
  // 上传路径
  string upload_path = 10;
}

// 获取对象存储临时密钥请求
message GetCredentialReq {}

// 获取对象存储临时密钥响应
message GetCredentialRsp {
  message Credentials {
    // token
    string token = 1;
    // 临时证书密钥 ID
    string tmp_secret_id = 2;
    // 临时证书密钥 Key
    string tmp_secret_key = 3;
  }
  // 密钥信息
  Credentials credentials = 1;
  // 失效时间
  uint32 expired_time = 2;
  // 起始时间
  uint32 start_time = 3;
  // 对象存储 桶
  string bucket = 4;
  // 对象存储 可用区
  string region = 5;
  // 目录
  string file_path = 6;
  // 主号
  uint64 corp_uin = 7;
  // 存储类型
  string type = 8;
}

// 获取临时链接请求
message GetPresignedURLReq {
  // 文档ID
  uint64 business_id = 1;
  // 无需检查是否展示引用
  bool is_no_check_refer = 2;
}

// 获取临时链接响应
message GetPresignedURLRsp {
  // 文件名
  string file_name = 1;
  // 文件类型
  string file_type = 2;
  // cos路径
  string cos_url = 3;
  // cos临时地址
  string url = 4;
  // cos桶
  string bucket = 5;
}

// 获取审核开关 请求
message GetAuditSwitchReq {}

// 获取审核开关 响应
message GetAuditSwitchRsp {
  bool switch = 1;
}

// 获取审核开关 请求
message DescribeAuditSwitchReq {}

// 获取审核开关 响应
message DescribeAuditSwitchRsp {
  bool switch = 1;
}

// 文档列表请求
message ListDocReq {
  message FilterFlag {
    // 标识位
    string flag = 1;
    // 标识值
    bool value = 2;
  }
  // 查询内容
  string query = 1;
  // 页码
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  string bot_biz_id = 4 [(validate.rules).string.min_len = 1];
  // 发布状态:7 审核中、8 审核失败、10 待发布、11 发布中、12 已发布、13 学习中、14 学习失败 17 已过期
  repeated uint32 status = 5;
  // 查询类型 filename 文档名称、 attribute 标签
  string query_type = 6;
  // 分类ID -1代表不过滤
  int64 cate_biz_id = 7;
  // 文件类型过滤 ["txt","jpg"]
  repeated string file_types = 8;
  // 标识筛选
  repeated FilterFlag filter_flag = 9;
  uint32 show_curr_cate = 10; //是否只展示当前分类的数据 0不是，1是
}

// Deprecated 属性标签详情信息
message AttrLabelV1 {
  message Label {
    // 标签ID
    uint64 label_id = 1;
    // 标签名称
    string label_name = 2;
  }
  // 属性标签来源，1：属性标签
  uint32 source = 1;
  // 属性ID
  uint64 attr_id = 2;
  // 属性标识
  string attr_key = 3;
  // 属性名称
  string attr_name = 4;
  // 标签ID
  repeated Label labels = 5;
}

// 属性标签详情信息
message AttrLabel {
  message Label {
    // 标签ID
    uint64 label_biz_id = 1;
    // 标签名称
    string label_name = 2;
  }
  // 属性标签来源，1：属性标签
  uint32 source = 1;
  // 属性ID
  uint64 attr_biz_id = 2;
  // 属性标识
  string attr_key = 3;
  // 属性名称
  string attr_name = 4;
  // 标签ID
  repeated Label labels = 5;
}

// 属性标签引用信息
message AttrLabelReferV1 {
  // 属性标签来源，1：属性标签
  uint32 source = 1 [(validate.rules).uint32.gt = 0];
  // 属性ID
  uint64 attr_id = 2 [(validate.rules).uint64.gt = 0];
  ;
  // 标签ID
  repeated uint64 label_ids = 3 [(validate.rules).repeated .min_items = 1];
}

// 属性标签引用信息
message AttrLabelRefer {
  // 属性标签来源，1：属性标签
  uint32 source = 1 [(validate.rules).uint32.gt = 0];
  // 属性ID
  string attribute_biz_id = 2 [(validate.rules).string = {min_len: 1}];
  // 标签ID
  repeated string label_biz_ids = 3 [(validate.rules).repeated .min_items = 1];
}

// DocProcessing 文档处理事项
enum DocProcessing {
  // 文档比对处理
  HandleDocDiff = 0;
  // 文档生成问答
  CreateDocQa = 1;
}

// DocAttribute 文档属性
enum DocAttributeFlag {
  // 公开
  Public = 0;
  // 文档停用属性
  Disable = 1;
}

// 文档列表响应
message ListDocRsp {
  message Doc {
    // 文档ID
    uint64 doc_biz_id = 1;
    // 文件名
    string file_name = 2;
    // cos路径
    string cos_url = 3;
    // 更新时间
    int64 update_time = 4;
    // 状态值
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 文件类型
    string file_type = 7;
    // 生成失败原因
    string reason = 8;
    // 答案中是否引用
    bool is_refer = 9;
    // qa对数量
    uint32 qa_num = 10;
    // 是否删除
    bool is_deleted = 11;
    // 文档来源
    uint32 source = 12;
    // 来源描述
    string source_desc = 13;
    // 是否允许重新生成
    bool is_allow_restart = 14;
    // qa是否已删除
    bool is_deleted_qa = 15;
    // 问答是否生成中
    bool is_creating_qa = 16;
    // 是否允许删除
    bool is_allow_delete = 17;
    // 是否允许操作引用开关
    bool is_allow_refer = 18;
    // 问答是否生成过
    bool is_created_qa = 19;
    // 文档字符量
    uint64 doc_char_size = 20;
    // 是否允许编辑
    bool is_allow_edit = 21;
    // 属性标签适用范围 1：全部，2：按条件范围
    uint32 attr_range = 22;
    // 属性标签
    repeated AttrLabel attr_labels = 23;
    // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
    uint32 refer_url_type = 24;
    // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
    string web_url = 25;
    // 知识库有效开始时间，unix时间戳
    uint64 expire_start = 26;
    // 知识库有效结束时间，unix时间戳，0代表永久有效
    uint64 expire_end = 27;
    // 允许重试 0:否 1:是
    bool is_allow_retry = 28;
    // 新文档名称, 重命名提交之后,文档发布之前都使用这个名称,列表优先展示这个,但下载与预览按状态展示
    string new_name = 29;
    // 所有正在处理中的事项
    repeated DocProcessing processing = 30;
    // 创建时间
    int64 create_time = 31;
    // 文档分类ID
    uint64 cate_biz_id = 32;
    // 外部客户的知识ID
    string customer_knowledge_id = 33;
    // 文档属性
    repeated DocAttributeFlag attribute_flags = 34;
    // 是否停用，false：未停用，true：已停用
    bool is_disabled = 35;
  }
  uint64 total = 1;
  repeated Doc list = 2;
}

// 文档列表请求
message ListDocV1Req {
  // 查询内容
  string query = 1;
  // 页码
  uint32 page = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 4 [(validate.rules).uint64.gt = 0];
  // 发布状态:7 审核中、8 审核失败、10 待发布、11 发布中、12 已发布、13 学习中、14 学习失败 17 已过期
  repeated uint32 status = 5;
}

// 文档列表响应
message ListDocV1Rsp {
  message Doc {
    // 文档ID
    uint64 id = 1;
    // 文件名
    string file_name = 2;
    // cos路径
    string cos_url = 3;
    // 更新时间
    int64 update_time = 4;
    // 状态值
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 文件类型
    string file_type = 7;
    // 生成失败原因
    string reason = 8;
    // 答案中是否引用
    bool is_refer = 9;
    // qa对数量
    uint32 qa_num = 10;
    // 是否删除
    bool is_deleted = 11;
    // 文档来源
    uint32 source = 12;
    // 来源描述
    string source_desc = 13;
    // 是否允许重新生成
    bool is_allow_restart = 14;
    // qa是否已删除
    bool is_deleted_qa = 15;
    // 问答是否生成中
    bool is_creating_qa = 16;
    // 是否允许删除
    bool is_allow_delete = 17;
    // 是否允许操作引用开关
    bool is_allow_refer = 18;
    // 问答是否生成过
    bool is_created_qa = 19;
    // 文档字符量
    uint64 doc_char_size = 20;
    // 是否允许编辑
    bool is_allow_edit = 21;
    // 属性标签适用范围 1：全部，2：按条件范围
    uint32 attr_range = 22;
    // 属性标签
    repeated AttrLabelV1 attr_labels = 23;
    // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
    uint32 refer_url_type = 24;
    // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
    string web_url = 25;
    // 知识库有效开始时间，unix时间戳
    uint64 expire_start = 26;
    // 知识库有效结束时间，unix时间戳，0代表永久有效
    uint64 expire_end = 27;
    // 允许重试 0:否 1:是
    bool is_allow_retry = 28;
  }
  uint64 total = 1;
  repeated Doc docs = 2;
}

// 保存文档请求
message SaveDocReq {
  // 文件名
  string file_name = 1 [(validate.rules).string = {min_len: 1, max_len: 255}];
  // cos路径
  string cos_url = 2 [(validate.rules).string.min_len = 1];
  // 文件类型
  string file_type = 3 [(validate.rules).string.min_len = 1];
  // 机器人ID
  string bot_biz_id = 4 [(validate.rules).string = {min_len: 1}];
  // ETag 全称为 Entity Tag，是对象被创建时标识对象内容的信息标签，可用于检查对象的内容是否发生变化
  string e_tag = 5 [(validate.rules).string.min_len = 1];
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 6 [(validate.rules).string.min_len = 1];
  // 文件大小
  string size = 7 [(validate.rules).string = {min_len: 1}];
  // 是否开始生成QA
  bool is_start_create = 8;
  // 来源(0 源文件导入 1 网页导入)
  uint32 source = 9;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 10 [(validate.rules).string.max_len = 2000];
  // 是否引用链接
  bool is_refer = 11;
  // 属性标签适用范围 1：全部，2：按条件范围
  uint32 attr_range = 12;
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 13;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 14;
  // 知识库有效开始时间，unix时间戳
  string expire_start = 15;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  string expire_end = 16;
  // 文档操作类型；1-批量导入；2-文档导入
  uint32 opt = 17;
  // 分类ID
  string cate_biz_id = 18;
  // 网页导入原始url
  string original_url = 19;
  // 外部客户的知识ID
  string customer_knowledge_id = 20;
  // 文档属性
  repeated DocAttributeFlag attribute_flags = 21;
}

// 保存文档响应
message SaveDocRsp {
  // 文档 ID
  uint64 doc_biz_id = 1;
  // 导入是否错误
  string error_msg = 2;
  // 错误链接
  string error_link = 3;
  // 错误链接文本
  string error_link_text = 4;
}

// 保存文档请求
message SaveDocV1Req {
  // 文件名
  string file_name = 1 [(validate.rules).string = {min_len: 1, max_len: 255}];
  // cos路径
  string cos_url = 2 [(validate.rules).string.min_len = 1];
  // 文件类型
  string file_type = 3 [(validate.rules).string.min_len = 1];
  // 机器人ID
  uint64 bot_biz_id = 4 [(validate.rules).uint64.gt = 0];
  // ETag 全称为 Entity Tag，是对象被创建时标识对象内容的信息标签，可用于检查对象的内容是否发生变化
  string e_tag = 5 [(validate.rules).string.min_len = 1];
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 6 [(validate.rules).string.min_len = 1];
  // 文件大小
  uint64 size = 7 [(validate.rules).uint64.gt = 0];
  // 是否开始生成QA
  bool is_start_create = 8;
  // 来源(0 源文件导入 1 网页导入)
  uint32 source = 9;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 10 [(validate.rules).string.max_len = 2000];
  // 是否引用链接
  bool is_refer = 11;
  // 属性标签适用范围 1：全部，2：按条件范围
  uint32 attr_range = 12 [(validate.rules).uint32.gte = 0];
  // 属性标签引用
  repeated AttrLabelReferV1 attr_labels = 13;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 14;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 15;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 16;
}

// 保存文档响应
message SaveDocV1Rsp {
  // 文档 ID
  uint64 id = 1;
  // 导入是否错误
  string error_msg = 2;
  // 错误链接
  string error_link = 3;
  // 错误链接文本
  string error_link_text = 4;
}

// 修改文档请求
message ModifyDocV1Req {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 文档id
  uint64 doc_id = 2;
  // 是否引用链接
  bool is_refer = 3;
  // 属性标签适用范围，1：全部，2：按条件范围
  uint32 attr_range = 4 [(validate.rules).uint32.gte = 0];
  // 属性标签
  repeated AttrLabelReferV1 attr_labels = 5;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 6;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 7 [(validate.rules).string.max_len = 2000];
  // 知识库有效开始时间，unix时间戳,
  uint64 expire_start = 8;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 9;
}

message ModifyDocV1Rsp {}

// 修改文档请求
message ModifyDocReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档id
  string doc_biz_id = 2 [(validate.rules).string = {min_len: 1}];
  // 是否引用链接
  bool is_refer = 3;
  // 属性标签适用范围，1：全部，2：按条件范围
  uint32 attr_range = 4 [(validate.rules).uint32.gte = 0];
  // 属性标签
  repeated AttrLabelRefer attr_labels = 5;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 6;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 8 [(validate.rules).string.max_len = 2000];
  // 知识库有效开始时间，unix时间戳,
  string expire_start = 9;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  string expire_end = 10;
  // 分类ID
  string cate_biz_id = 11;
  // 外部客户的知识ID
  string customer_knowledge_id = 12;
  // 文档属性
  repeated DocAttributeFlag attribute_flags = 13;
}

message ModifyDocRsp {}

// 修改文档状态请求
message ModifyDocStatusReq {
  // 机器人ID
  string app_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档id
  string doc_biz_id = 2 [(validate.rules).string = {min_len: 1}];
  // 文档状态，停用，启用
  bool is_disabled = 3;
}
// 修改文档状态响应
message ModifyDocStatusRsp {}

// 批量修改文档外部链接，到期时间
message BatchModifyDocReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档id
  repeated string doc_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
  // 是否引用链接
  bool is_refer = 3;
  // 外部引用链接类型 0 使用系统链接（预览） 1 使用用户自定义链接
  uint32 refer_url_type = 4;
  // 网页地址（用户自定义链接）；当source来源是 1 或 refer_url_type 是 1 时 web_url 必填。
  string web_url = 5 [(validate.rules).string.max_len = 2000];
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  string expire_end = 6;
  // action 0-同时修改到期时间和外部引用链接  1-修改到期时间  2-修改外部引用链接
  uint32 action_type= 7;
}

// 批量修改文档外部链接，到期时间
message BatchModifyDocRsp {}

// 删除文档请求【加校验】
message DeleteDocReq {
  // 文档ID
  repeated string  ids = 1;
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string = {min_len: 1}];
  // 文档业务ID
  repeated string doc_biz_ids = 3;
}

// 删除文档响应
message DeleteDocRsp {}

// 检查文档引用的工作流请求
message CheckDocReferWorkFlowReq {
  // 机器人ID
  string bot_biz_id = 1;
  // 文档业务ID
  repeated string doc_biz_ids = 2;
}

// 检查文档引用的工作流响应
message CheckDocReferWorkFlowRsp {
  repeated trpc.KEP.bot_task_config_server.DocRefByWorkflow list = 1;
}

// DocRefByWorkflow详情
message DocRefByWorkflow {
  string doc_biz_id = 1; // 文档ID
  repeated WorkflowRef work_flow_list = 2; // 工作流详情集合
}

// 检查标签引用的工作流请求
message CheckAttributeReferWorkFlowReq {
  // 机器人ID
  string bot_biz_id = 1;
  // 标签业务ID
  repeated string attribute_biz_ids = 2;
}

// 检查文档引用的工作流响应
message CheckAttributeReferWorkFlowRsp {
  repeated trpc.KEP.bot_task_config_server.AttributeRefByWorkflow list = 1;
}

// AttributeRefByWorkflow详情
message AttributeRefByWorkflow {
  string attribute_biz_id = 1; // 标签ID
  repeated WorkflowRef work_flow_list = 2; // 工作流详情集合
}


// AttributeRefByWorkflow详情
message AttributeLabelRefByWorkflow {
  string attribute_label_biz_id = 1; // 标签值ID
  repeated WorkflowRef work_flow_list = 2; // 工作流详情集合
}

// WorkflowRef详情
message WorkflowRef {
  string work_flow_id = 1; // 任务流ID
  string work_flow_name = 2; //任务流名称
  string work_flow_desc = 3; //任务流描述
  string app_biz_id = 4; // 应用ID
  uint32 update_time = 5; // 更新时间
}


// 文档详情请求
message DescribeDocReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档ID
  string doc_biz_id = 2;
}

// 文档详情响应
message DescribeDocRsp {
  // 文档ID
  uint64 doc_biz_id = 1;
  // 文件名
  string file_name = 2;
  // cos路径
  string cos_url = 3;
  // 更新时间
  int64 update_time = 4;
  // 状态值(5审核通过 7审核中 8审核不通过 9审核通过 10待发布 11发布中 12发布成功 13学习中 14学习失败)
  uint32 status = 5;
  // 状态描述
  string status_desc = 6;
  // 文件类型
  string file_type = 7;
  // 生成失败原因
  string reason = 8;
  // 答案中是否引用
  bool is_refer = 9;
  // qa对数量
  uint32 qa_num = 10;
  // 是否删除
  bool is_deleted = 11;
  // 文档来源
  uint32 source = 12;
  // 来源描述
  string source_desc = 13;
  // 是否允许重新生成
  bool is_allow_restart = 14;
  // qa是否已删除
  bool is_deleted_qa = 15;
  // 问答是否生成中
  bool is_creating_qa = 16;
  // 是否允许删除
  bool is_allow_delete = 17;
  // 是否允许操作引用开关
  bool is_allow_refer = 18;
  // 问答是否生成过
  bool is_created_qa = 19;
  // 文档字符量
  uint64 doc_char_size = 20;
  // 是否允许编辑
  bool is_allow_edit = 21;
  // 属性标签适用范围 1：全部，2：按条件范围
  uint32 attr_range = 22;
  // 属性标签
  repeated AttrLabel attr_labels = 23;
  // 分类ID
  uint64 cate_biz_id = 24;
  // 外部客户的知识ID
  string customer_knowledge_id = 25;
  // 文档属性
  repeated DocAttributeFlag attribute_flags = 26;
  // 是否停用，0：未停用，1：已停用
  bool is_disabled = 27;
}

// 文档详情请求
message DescribeDocsReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档ID列表
  repeated string doc_biz_ids = 2;
}

// 文档详情响应
message DescribeDocsRsp {
  message DocDetail {
    // 文档ID
    uint64 doc_biz_id = 1;
    // 文件名
    string file_name = 2;
    // cos路径
    string cos_url = 3;
    // 更新时间
    int64 update_time = 4;
    // 状态值(5审核通过 7审核中 8审核不通过 9审核通过 10待发布 11发布中 12发布成功 13学习中 14学习失败)
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 文件类型
    string file_type = 7;
    // 生成失败原因
    string reason = 8;
    // 答案中是否引用
    bool is_refer = 9;
    // qa对数量
    uint32 qa_num = 10;
    // 是否删除
    bool is_deleted = 11;
    // 文档来源
    uint32 source = 12;
    // 来源描述
    string source_desc = 13;
    // 是否允许重新生成
    bool is_allow_restart = 14;
    // qa是否已删除
    bool is_deleted_qa = 15;
    // 问答是否生成中
    bool is_creating_qa = 16;
    // 是否允许删除
    bool is_allow_delete = 17;
    // 是否允许操作引用开关
    bool is_allow_refer = 18;
    // 问答是否生成过
    bool is_created_qa = 19;
    // 文档字符量
    uint64 doc_char_size = 20;
    // 是否允许编辑
    bool is_allow_edit = 21;
    // 属性标签适用范围 1：全部，2：按条件范围
    uint32 attr_range = 22;
    // 属性标签
    repeated AttrLabel attr_labels = 23;
    // 分类ID
    uint64 cate_biz_id = 24;
  }
  repeated DocDetail docs = 1;
}

// 获取片段详情请求
message DescribeSegmentsReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 片段 ID 列表
  repeated string seg_biz_id = 2;
}

// 获取片段详情响应数据
message DescribeSegmentsRsp {
  message Segment {
    uint64    id = 1;
    uint64    business_id = 2;    // 业务ID
    uint64    doc_id = 3;    // 文章ID
    string    file_type = 4;    // 文件类型(markdown,word,txt)
    string    segment_type = 5;    // 文档切片类型(segment-文档切片 table-表格)
    string    title = 6;    // 标题
    string    page_content = 7;    // 段落内容
    string    org_data = 8;    // 段落原文
    uint64    doc_biz_id = 9;    // 文档业务ID
    string    doc_url = 10;   // 文档链接
  }
  repeated Segment list = 1;
}

// 批量获取非表格类型文档切片内容请求
message ListDocSegmentReq {
  // 应用ID
  string app_biz_id = 1;
  // 文档ID
  string doc_biz_id = 2;
  // 关键词
  string keywords = 3;
  // 切片起始位置
  uint32 page_number = 4;
  // 期望返回的切片数
  uint32 page_size = 5;
  // 文档类型
  string file_type = 6;
  // 参考ID（对话处使用输入后会检索t_refer表）
  string refer_biz_id = 7;
}

// 批量获取非表格类型文档切片内容响应
message ListDocSegmentRsp {
  // 返回的切片总数
  uint64 total = 1;
  message DocSegmentItem {
    // 切片ID
    string seg_biz_id = 1;
    // 切片原文（客户可以编辑的内容）
    string org_data = 2;
    // 切片对应的原文位置
    repeated uint64 page_infos = 3;
    // 是否是原始切片。0:原始 1:更新
    bool is_origin = 4;
    // 是否手动添加 0:自动生成 1:手动添加
    bool is_add = 5;
    // 切片类型
    string segment_type = 6;
    // 是否停用
    bool is_disabled = 7;
  }
  // 切片列表
  repeated DocSegmentItem segment_list = 2;
  // 该文档切片是否被修改过，但未提交。0：未修改 1:已修改
  bool is_modify = 3;
}

// 获取表格类型文档sheet数据请求
message ListTableSheetReq {
  // 应用ID
  string app_biz_id = 1;
  // 文档ID
  string doc_biz_id = 2;
  uint32 page_number = 3;
  uint32 page_size = 4;
  // 参考ID（对话处使用输入后会检索t_refer表）
  string refer_biz_id = 5;
}

// 获取表格类型文档sheet数据响应
message ListTableSheetRsp {
  // 返回的sheet总数
  uint64 total = 1;
  message TableSheetItem {
    // 临时表对应的sheet id
    string sheet_biz_id = 1;
    // sheet名称
    string sheet_name = 2;
    string bucket = 3;
    string region = 4;
    // sheet cos地址
    string cos_url = 5;
    // sheet cos存储的md5
    string cos_hash = 6;
    // sheet cos文件名称
    string file_name = 7;
    // sheet cos文件类型
    string file_type = 8;
    // 是否是原始sheet内容。
    bool is_origin = 9;
    // 是否停用
    bool is_disabled = 10;
    // 是否支持结构化结果展示
    bool is_support_structured_output = 11;
    // 是否停用检索增强
    bool is_disabled_retrieval_enhance = 12;
  }
  // sheet cos 地址列表
  repeated TableSheetItem sheet_list = 2;
  // 该文档表格内容是否被修改过，但未提交。
  bool is_modify = 3;
}

// 修改非表格类型文档切片请求
message ModifyDocSegmentReq {
  // 应用ID
  string app_biz_id = 1;
  // 文档ID
  string doc_biz_id = 2;
  // 删除的切片ID
  repeated string delete_seg_biz_ids = 3;
  message ModifyDocSegmentItem {
    // 切片ID
    string seg_biz_id = 1;
    // 添加切片的上一个切片的ID（第一个插入，输入first）
    string last_seg_biz_id = 2;
    // 仅LastDocSegmentId输入first使用，添加切片的后一个切片的ID
    string after_seg_biz_id = 3;
    // 编辑/新增的切片内容
    string org_data = 4;
  }
  // 新增或编辑的切片
  repeated ModifyDocSegmentItem modify_segments = 4;
  // 停用的切片ID
  repeated string disabled_seg_biz_ids = 5;
  // 启用的切片ID
  repeated string enable_seg_biz_ids = 6;
}

// 修改非表格类型文档切片响应
message ModifyDocSegmentRsp {
  // 文档ID
  string doc_biz_id = 1;
}

// 修改表格类型文档sheet请求
message ModifyTableSheetReq {
  // 应用ID
  string app_biz_id = 1;
  // 文档ID
  string doc_biz_id = 2;
  message ModifyTableSheetItem {
    // 临时表对应的sheet id
    string sheet_biz_id = 1;
    // sheet名称
    string sheet_name = 2;
    string bucket = 3;
    string region = 4;
    // 编辑后的sheet cos地址
    string cos_url = 5;
    // sheet cos存储的md5
    string cos_hash = 6;
    // sheet cos文件名称
    string file_name = 7;
    // sheet cos文件类型
    string file_type = 8;
  }
  // 编辑后的sheet
  repeated ModifyTableSheetItem modify_table_sheets = 3;
  // 删除的sheetID
  repeated string delete_sheet_biz_ids = 4;
  // 停用的sheetID
  repeated string disabled_sheet_biz_ids = 5;
  // 启用的sheetID
  repeated string enable_sheet_biz_ids = 6;
  // 开启检索增强的sheet名称
  repeated string enable_retrieval_enhance_sheet_names = 7;
  // 关闭检索增强的sheet名称
  repeated string disabled_retrieval_enhance_sheet_names = 8;
}

// 修改表格类型文档sheet响应
message ModifyTableSheetRsp {
  // 文档ID
  string doc_biz_id = 1;
}

// 提交切片干预任务请求
message CreateDocParsingInterventionReq {
  // 文档名称，要和之前保持一致
  string file_name = 1;
  // 文档类型，docx、ppt、csv等
  string file_type = 2;
  // 应用业务ID
  string app_biz_id = 3;
  // 原始文档ID，如果干预过多次，则是最近一次干预的文档ID
  string origin_doc_biz_id = 4;
}

// 提交切片干预任务响应
message CreateDocParsingInterventionRsp {
  // 文档ID
  string doc_biz_id = 1;
  // 原始文档ID，如果干预过多次，则是最近一次干预的文档ID
  string origin_doc_biz_id = 2;
}

// 答案中是否引用请求
message ReferDocReq {
  // 文档ID
  uint64 doc_id = 1;
  // 答案中是否引用
  bool is_refer = 2;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
  // 文档业务ID
  uint64 doc_biz_id = 4;
}

// 答案中是否引用响应
message ReferDocRsp {}

// 重新生成QA请求
message StartCreateQAReq {
  // 文档ID
  repeated string ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string.min_len = 1];
}

// 生成QA响应
message StartCreateQARsp {}

// 文档预览请求
message GetDocPreviewReq {
  // 文档ID
  string doc_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string = {min_len: 1}];
  // 区分类型: offline:离线文件，realtime:实时文件；为空默认为offline
  string type_key = 3;
}

// 文档预览响应
message GetDocPreviewRsp {
  // 文件名, 发布端固定使用这个名称
  string file_name = 1;
  // 文件类型
  string file_type = 2;
  // cos路径
  string cos_url = 3;
  // cos临时地址
  string url = 4;
  // cos桶
  string bucket = 5;
  // 存在文档重命名情况下的新名称, 评测端优先使用这个名称
  string new_name = 6;
  // 文件md路径
  string parse_result_cos_url = 7;
}

// 重新生成QA请求
message GenerateQAReq {
  // 文档ID
  repeated string doc_biz_ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string = {min_len: 1}];
}

// 生成QA响应
message GenerateQARsp {}

// 文档下拉列表请求
message GetSelectDocReq {
  // 文档名称
  string file_name = 1;
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 发布状态:7 审核中、8 审核失败、10 待发布、11 发布中、12 已发布、13 学习中、14 学习失败 20 已过期
  repeated uint32 status = 3;
}

// 文档下拉列表响应
message GetSelectDocRsp {
  message Option {
    string text = 1;
    string value = 2;
    uint64 char_size = 3;
    string file_type = 4;
  }
  repeated Option list = 1;
}

// 终止文档解析
message StopDocParseReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档ID
  string doc_biz_id = 2 [(validate.rules).string = {min_len: 1}];
}

// 终止文档解析响应
message StopDocParseRsp {}

// 重试文档解析
message RetryDocParseReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档ID
  string doc_biz_id = 2 [(validate.rules).string = {min_len: 1}];
}

// 重试文档解析响应
message RetryDocParseRsp {}

// 重试文档审核
message RetryDocAuditReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档ID
  string doc_biz_id = 2 [(validate.rules).string = {min_len: 1}];
}

// 重试文档审核响应
message RetryDocAuditRsp {}

message ListSelectDocReq {
  // 文档名称
  string file_name = 1;
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string.min_len = 1];
  // 发布状态:7 审核中、8 审核失败、10 待发布、11 发布中、12 已发布、13 学习中、14 学习失败 20 已过期
  repeated uint32 status = 3;
}

// 文档下拉列表响应
message ListSelectDocRsp {
  message Option {
    string text = 1;
    string value = 2;
    uint64 char_size = 3;
    string file_type = 4;
  }
  repeated Option list = 1;
}

// 获取网页内容请求
message FetchURLContentReq {
  // url地址
  string url = 1 [(validate.rules).string.min_len = 1];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 获取网页内容响应
message FetchURLContentRsp {
  // title 标题
  string title = 1;
  // content 内容
  string content = 2;
}

// 获取QA分类分组请求
message ListQACateReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
}

// 获取QA分类分组响应
message ListQACateRsp {
  message Cate {
    // QA分类的业务ID
    uint64 cate_biz_id = 1;
    // 分类名称
    string name = 2;
    // 分类下QA数量
    uint32 total = 3;
    // 是否可新增
    bool can_add = 4;
    // 是否可编辑
    bool can_edit = 5;
    // 是否可删除
    bool can_delete = 6;
    // 子分类
    repeated Cate children = 7;
  }
  repeated Cate list = 1;
}

// 获取QA分类分组请求
message ListQACateV1Req {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 获取QA分类分组响应
message ListQACateV1Rsp {
  message Cate {
    // 分类ID
    int64 id = 1;
    // 分类名称
    string name = 2;
    // 分类下QA数量
    uint32 total = 3;
    // 是否可新增
    bool can_add = 4;
    // 是否可编辑
    bool can_edit = 5;
    // 是否可删除
    bool can_delete = 6;
    // 子分类
    repeated Cate children = 7;
  }
  repeated Cate records = 1;
}

// 创建问答分类请求
message CreateQACateReq {
  // 分类名称
  string name = 1 [(validate.rules).string.min_len = 1];
  // 父分类业务ID,0为未分类
  string parent_biz_id = 2;
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string.min_len = 1];
}

// 创建问答分类响应
message CreateQACateRsp {
  // 分类业务ID
  uint64 cate_biz_id = 1;
  // 是否可新增
  bool can_add = 2;
  // 是否可编辑
  bool can_edit = 3;
  // 是否可删除
  bool can_delete = 4;
}

// 创建问答分类请求
message CreateQACateV1Req {
  // 分类名称
  string name = 1 [(validate.rules).string.min_len = 1];
  int64 parent_id = 2;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 创建问答分类响应
message CreateQACateV1Rsp {
  // 分类 ID
  uint64 id = 1;
  // 是否可新增
  bool can_add = 2;
  // 是否可编辑
  bool can_edit = 3;
  // 是否可删除
  bool can_delete = 4;
}

// 分类修改请求
message UpdateQACateReq {
  // 分类ID
  uint64 id = 1;
  // 分类名称
  string name = 2 [(validate.rules).string.min_len = 1];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
  // 分类业务ID
  uint64 cate_biz_id = 4;
}

// 分类修改响应
message UpdateQACateRsp {}

// 分类修改请求
message ModifyQACateReq {
  // 分类ID
  string id = 1;
  // 分类名称
  string name = 2 [(validate.rules).string.min_len = 1];
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string = {min_len: 1}];
  // 分类业务ID
  string cate_biz_id = 4;
}

// 分类修改响应
message ModifyQACateRsp {}

// 分类删除请求
message DeleteQACateReq {
  // 分类ID
  string id = 1;
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string.min_len = 1];
  // 分类业务ID
  string cate_biz_id = 3;
}

// 分类删除响应
message DeleteQACateRsp {}

// 获取QA列表请求
message GetQAListReq {
  // 查询内容
  string query = 1;
  // 分类ID -1代表不过滤
  int64 cate_id = 2;
  // 校验状态 1未校验2采纳3不采纳
  repeated uint32 accept_status = 3;
  // 发布状态(2 待发布 3 发布中 4 已发布 5 发布失败)
  repeated uint32 release_status = 4;
  // 文档ID
  uint64 doc_id = 5;
  // QAID
  uint64 qa_id = 6;
  // 页码
  uint32 page = 7 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
  // 来源(1 文档生成 2 批量导入 3 手动添加)
  uint32 source = 9;
  // 机器人ID
  uint64 bot_biz_id = 10 [(validate.rules).uint64.gt = 0];
}

// 获取QA列表响应
message GetQAListRsp {
  // QA详情
  message QA {
    // QA ID
    uint64 id = 1;
    // 问题
    string question = 2;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 3;
    // 来源描述
    string source_desc = 4;
    // 更新时间
    int64 update_time = 5;
    // 状态 1未校验2未发布3发布中4已发布5发布失败6不采纳
    uint32 status = 6;
    // 状态描述
    string status_desc = 7;
    // 文档id
    uint64 doc_id = 8;
    // 创建时间
    int64 create_time = 9;
    // 是否允许编辑
    bool is_allow_edit = 10;
    // 是否允许删除
    bool is_allow_delete = 11;
    // 是否允许校验
    bool is_allow_accept = 12;
    // 文档名称
    string file_name = 13;
    // 文档类型
    string file_type = 14;
    // 答案
    string answer = 15;
    // 问答字符数
    uint64 qa_char_size = 16;
    // 知识库有效开始时间，unix时间戳
    uint64 expire_start = 17;
    // 知识库有效结束时间，unix时间戳，0代表永久有效
    uint64 expire_end = 18;
  }
  uint64 total = 1;
  // 待校验
  uint64 wait_verify_total = 2;
  // 未采纳
  uint64 no_accepted_total = 3;
  // 已校验
  uint64 accepted_total = 4;
  // 页码
  uint32 page = 5 [(validate.rules).uint32.gt = 0];
  // 列表
  repeated QA list = 6;
}

// 获取QA列表请求
message ListQAReq {
  // 查询内容
  string query = 1;
  // 分类ID -1代表不过滤
  int64 cate_biz_id = 2;
  // 校验状态 1未校验2采纳3不采纳
  repeated uint32 accept_status = 3;
  // 发布状态(2待发布 3发布中 4已发布 7审核中 8审核失败 9人工申述中 11人工申述失败)
  repeated uint32 release_status = 4;
  // 文档ID
  uint64 doc_biz_id = 5;
  // QAID
  uint64 qa_biz_id = 6;
  // 页码
  uint32 page_number = 7 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
  // 来源(1 文档生成 2 批量导入 3 手动添加)
  uint32 source = 9;
  // 机器人ID
  uint64 bot_biz_id = 10 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  string query_answer = 11;
  // 根据多个QA业务ID查询列表
  repeated uint64 qa_biz_ids = 12;
  // 查询类型 filename 文档名称、 attribute 标签
  string query_type = 13;
  uint32 show_curr_cate = 14; //是否只展示当前分类的数据 0不是，1是
}

// 获取QA列表响应
message ListQARsp {
  // QA详情
  message QA {
    // QA ID
    uint64 qa_biz_id = 1;
    // 问题
    string question = 2;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 3;
    // 来源描述
    string source_desc = 4;
    // 更新时间
    int64 update_time = 5;
    // 状态 1未校验2未发布3发布中4已发布5发布失败6不采纳
    uint32 status = 6;
    // 状态描述
    string status_desc = 7;
    // 文档id
    uint64 doc_biz_id = 8;
    // 创建时间
    int64 create_time = 9;
    // 是否允许编辑
    bool is_allow_edit = 10;
    // 是否允许删除
    bool is_allow_delete = 11;
    // 是否允许校验
    bool is_allow_accept = 12;
    // 文档名称
    string file_name = 13;
    // 文档类型
    string file_type = 14;
    // 答案
    string answer = 15;
    // 问答字符数
    uint64 qa_char_size = 16;
    // 知识库有效开始时间，unix时间戳
    uint64 expire_start = 17;
    // 知识库有效结束时间，unix时间戳，0代表永久有效
    uint64 expire_end = 18;
    // 属性标签适用范围 1：全部，2：按条件
    uint32 attr_range = 19;
    // 属性标签
    repeated AttrLabel attr_labels = 20;
    // 相似问个数
    uint32 similar_question_num = 21;
    // 问答关联的相似问,仅展示一条,联动搜索条件
    string similar_question_tips = 22;
    // 是否停用，0：未停用，1：已停用
    bool is_disabled = 23;
  }
  uint64 total = 1;
  // 待校验
  uint64 wait_verify_total = 2;
  // 未采纳
  uint64 not_accepted_total = 3;
  // 已校验
  uint64 accepted_total = 4;
  // 页码
  uint32 page_number = 5 [(validate.rules).uint32.gt = 0];
  // 列表
  repeated QA list = 6;
}

// 生成相似问答对请求
message GenerateSimilarQuestionsReq {
  // 应用ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 问题
  string question = 2;
  // 问题
  string answer = 3;
}

// 生成相似问答对响应
message GenerateSimilarQuestionsRsp {
    // 生成的相似问集合
    repeated string question = 1;
}

// 生成问答任务请求
message ListQaTaskReq {
  // 页码
  uint32 page_number = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string.min_len = 1];
}
// 生成问答任务响应
message ListQaTaskRsp {
  message Task {
    // 任务ID
    uint64 task_id = 1;
    // 文档ID
    uint64 doc_biz_id = 2;
    // 文件名
    string file_name = 3;
    // 文件类型
    string file_type = 4;
    // 已完成问答的切片数量
    uint32 segment_count_done = 5;
    // 总切片数
    uint32 segment_count = 6;
    // 生成问答数
    uint32 qa_count = 7;
    // 输入token数
    uint64 input_token = 8;
    // 输出token数
    uint64 output_token = 9;
    // 状态 (1生成中 2成功 3手动暂停 4资源耗尽暂停 5失败 6手动取消)
    uint32 status = 10;
    // 任务异常错误信息
    string task_message = 11;
    // 来源类型(0来源于文档生成问答任务 1来源于文档对比任务)
    uint32 source_type = 12;
    // 来源ID
    uint64 source_id = 13;
  }
  uint64 total = 1;
  repeated Task list = 2;
}

// 删除QA请求
message DeleteQaTaskReq {
  // 任务ID
  string task_id = 1;
  // 机器人ID
  string bot_biz_id = 2;
}
// 删除生成QA任务响应
message DeleteQaTaskRsp {}

// 暂停生成qa任务请求
message StopQaTaskReq {
  // 任务ID
  string task_id = 1;
  // 机器人ID
  string bot_biz_id = 2;
  // 是否取消任务
  bool is_cancel = 3;
}
// 暂停生成qa任务响应
message StopQaTaskRsp {}

// 重试生成qa任务请求
message RetryQaTaskReq {
  // 任务ID
  string task_id = 1;
  // 机器人ID
  string bot_biz_id = 2;
  // 是否继续
  bool is_continue = 3;
}
// 重试生成qa任务响应
message RetryQaTaskRsp {}

// 获取QA详情请求
message GetQADetailReq {
  // QA ID
  uint64 id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 获取QA详情响应
message GetQADetailRsp {
  // QA ID
  uint64 id = 1;
  // question
  string question = 2;
  // answer
  string answer = 3;
  // 来源
  uint32 source = 4;
  // 来源描述
  string source_desc = 5;
  // 更新时间
  int64 update_time = 6;
  // 状态
  uint32 status = 7;
  // 状态描述
  string status_desc = 8;
  // 分类ID
  uint64 cate_id = 9;
  // 是否允许校验
  bool is_allow_accept = 10;
  // 是否允许编辑
  bool is_allow_edit = 11;
  // 是否允许删除
  bool is_allow_delete = 12;
  // 文档id
  uint64 doc_id = 13;
  // 文档名称
  string file_name = 14;
  // 文档类型
  string file_type = 15;
  // 分片ID
  uint64 segment_id = 16;
  // 分片内容, 待下个版本前端修改后可删除 @sinutelu
  // /qbot/admin/getQADetail
  //   page_content -> org_data
  string page_content = 17 [deprecated = true];
  // 分片高亮内容
  repeated Highlight highlights = 18;
  // 分片内容
  string org_data = 19;
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 20;
  // 属性标签
  repeated AttrLabelV1 attr_labels = 21;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 22;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 23;
}

// 获取QA详情请求
message DescribeQAReq {
  // QA业务ID
  string qa_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string.min_len = 1];
}

// 获取QA详情响应
message DescribeQARsp {
  // QA业务ID
  uint64 qa_biz_id = 1;
  // 问题
  string question = 2;
  // 答案
  string answer = 3;
  // 来源
  uint32 source = 4;
  // 来源描述
  string source_desc = 5;
  // 更新时间
  int64 update_time = 6;
  // 状态
  uint32 status = 7;
  // 状态描述
  string status_desc = 8;
  // 分类ID
  uint64 cate_biz_id = 9;
  // 是否允许校验
  bool is_allow_accept = 10;
  // 是否允许编辑
  bool is_allow_edit = 11;
  // 是否允许删除
  bool is_allow_delete = 12;
  // 文档id
  uint64 doc_biz_id = 13;
  // 文档名称
  string file_name = 14;
  // 文档类型
  string file_type = 15;
  // 分片ID
  uint64 segment_biz_id = 16;
  // 分片内容, 待下个版本前端修改后可删除 @sinutelu
  // /qbot/admin/getQADetail
  //   page_content -> org_data
  string page_content = 17 [deprecated = true];
  // 分片高亮内容
  repeated Highlight highlights = 18;
  // 分片内容
  string org_data = 19;
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 20;
  // 属性标签
  repeated AttrLabel attr_labels = 21;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 22;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 23;
  // 自定义参数
  string custom_param = 24;
  // 相似问
  repeated SimilarQuestion similar_questions = 25;
  // 问题和答案文本审核状态 1审核失败
  uint32 qa_audit_status = 26;
  // 答案中的图片审核状态 1审核失败
  uint32 pic_audit_status = 27;
  // 答案中的视频审核状态 1审核失败
  uint32 video_audit_status = 28;
  // 问题描述 qa意图的描述
  string question_desc = 29;
  // 是否停用，0：未停用，1：已停用
  bool is_disabled = 30;
}

// 分片高亮内容
message Highlight {
  // 高亮启始位置
  uint64 start_pos = 1;
  // 高亮结束位置
  uint64 end_pos = 2;
  // 高亮子文本
  string text = 3;
}

// 新建QA请求
message CreateQAReq {
  // 分组ID
  string cate_biz_id = 1;
  // question
  string question = 2 [(validate.rules).string.min_len = 1];
  // answer
  string answer = 3 [(validate.rules).string.min_len = 1];
  // 文档ID
  string doc_biz_id = 4;
  // 机器人ID
  string bot_biz_id = 5 [(validate.rules).string.min_len = 1];
  // 业务来源
  uint32 business_source = 6;
  // 来源的业务ID
  uint64 business_id = 7;
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 8 [(validate.rules).uint32.gte = 0];
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 9;
  // 知识库有效开始时间，unix时间戳
  string expire_start = 10;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  string expire_end = 11;
  // 自定义参数
  string custom_param = 12;
  // 相似问
  repeated string similar_questions = 13;
  // 问题描述 qa意图的描述
  string question_desc = 14;
}

// 相似问
message SimilarQuestion {
  // 相似问 BIZ ID
  uint64 sim_biz_id = 1;
  // 相似问内容
  string question = 2 [(validate.rules).string.min_len = 1];
  // 相似问审核状态 1审核失败
  uint32 audit_status = 3;
}

// 相似问相关的额外信息
message SimilarQuestionExtra {
  // 当检索到的是相似问时，返回该相似问ID
  uint64 similar_id = 1;
  // 当检索到的是相似问时，返回该相似问的问题
  string similar_question = 2;
}

// 新建QA响应
message CreateQARsp {
  // 主 QA ID
  uint64 qa_biz_id = 1;
}

// 新建QA请求
message CreateQAV1Req {
  // 分组ID
  uint64 cate_id = 1;
  // question
  string question = 2 [(validate.rules).string.min_len = 1];
  // answer
  string answer = 3 [(validate.rules).string.min_len = 1];
  // 文档ID
  uint64 doc_id = 4;
  // 机器人ID
  uint64 bot_biz_id = 5 [(validate.rules).uint64.gt = 0];
  // 业务来源
  uint32 business_source = 6;
  // 来源的业务ID
  uint64 business_id = 7;
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 8 [(validate.rules).uint32.gte = 0];
  // 属性标签引用
  repeated AttrLabelReferV1 attr_labels = 9;
  // 知识库有效开始时间，unix时间戳,
  uint64 expire_start = 10;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 11;
}

// 新建QA响应
message CreateQAV1Rsp {
  // QA ID
  uint64 id = 1;
}

// 编辑QA请求
message UpdateQAReq {
  // QA ID
  uint64 id = 1 [(validate.rules).uint64.gt = 0];
  // question
  string question = 2 [(validate.rules).string.min_len = 1];
  // answer
  string answer = 3 [(validate.rules).string.min_len = 1];
  // 分组ID
  uint64 cate_id = 4;
  // 文档id
  uint64 doc_id = 5;
  // 机器人ID
  uint64 bot_biz_id = 6 [(validate.rules).uint64.gt = 0];
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 7;
  // 属性标签引用
  repeated AttrLabelReferV1 attr_labels = 8;
  // 知识库有效开始时间，unix时间戳
  uint64 expire_start = 9;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  uint64 expire_end = 10;
}

// 编辑QA响应
message UpdateQARsp {}

// 编辑QA请求
message ModifyQAReq {
  // QA ID
  string qa_biz_id = 1 [(validate.rules).string.min_len = 1];
  // question
  string question = 2 [(validate.rules).string.min_len = 1];
  // answer
  string answer = 3 [(validate.rules).string.min_len = 1];
  // 分组ID
  string cate_biz_id = 4;
  // 文档id
  string doc_biz_id = 5;
  // 机器人ID
  string bot_biz_id = 6 [(validate.rules).string.min_len = 1];
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 7;
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 8;
  // 知识库有效开始时间，unix时间戳,
  string expire_start = 9;
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  string expire_end = 10;
  // 自定义参数
  string custom_param = 11;
  // 相似问修改请求
  SimilarQuestionModify similar_question_modify = 12;
  // 问题描述 qa意图的描述
  string question_desc = 13;
}

// 相似问修改请求
message SimilarQuestionModify {
  // 新增相似问列表
  repeated string add_questions = 1;
  // 修改相似问列表
  repeated SimilarQuestion update_questions = 2;
  // 删除相似问列表
  repeated SimilarQuestion delete_questions = 3;
}

// 编辑QA响应
message ModifyQARsp {}

// 删除QA请求
message DeleteQAReq {
  // QA ID 列表
  repeated string qa_biz_ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string.min_len = 1];
}

// 删除QA响应
message DeleteQARsp {}

// 删除QA请求
message DeleteQAV1Req {
  // QA ID 列表
  repeated uint64 ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 删除QA响应
message DeleteQAV1Rsp {}

// 校验QA请求
message VerifyQAReq {
  // 问答信息
  message QAList {
    // QAID
    string qa_biz_id = 1 [(validate.rules).string = {min_len: 1}];
    // 是否采纳
    bool is_accepted = 2;
    // 分组ID
    string cate_biz_id = 3;
    // 问题
    string question = 4;
    // 答案
    string answer = 5;
  }
  // QA
  repeated QAList list = 1;
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string = {min_len: 1}];
}

// 校验QA响应
message VerifyQARsp {}

// 校验QA请求
message VerifyQAV1Req {
  // 问答信息
  message QAList {
    // QA ID
    uint64 id = 1;
    // 是否采纳
    bool is_accepted = 2;
    // 分组ID(待删除)
    uint64 cate_id = 3;
    // 问题
    string question = 4;
    // 答案
    string answer = 5;
    // QA业务ID(待补充规则必传且大于0)
    uint64 qa_biz_id = 6;
    // 分组业务ID
    uint64 cate_biz_id = 7;
  }
  // QA
  repeated QAList list = 1;
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 校验QA响应
message VerifyQAV1Rsp {}

// QA分组请求
message GroupQAReq {
  // QA ID
  repeated string ids = 1;
  // 分组 ID
  string cate_id = 2;
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string.min_len = 1];
  // QA业务ID列表【ids】
  repeated uint64 qa_biz_ids = 4;
  // 分组 ID【cate_id】
  string cate_biz_id = 5;
}

// QA分组响应
message GroupQARsp {}

// 编辑QA适用范围请求
message ModifyQAAttrRangeReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // QA业务ID列表【ids】
  repeated string qa_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 3;
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 4;
}

// 编辑QA适用范围响应
message ModifyQAAttrRangeRsp {}

// 编辑QA状态请求
message ModifyQAStatusReq {
  // 机器人ID
  string app_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 问答id
  string qa_biz_id = 2 [(validate.rules).string = {min_len: 1}];
  // 问答状态，停用，启用
  bool is_disabled = 3;
}

// 编辑QA状态响应
message ModifyQAStatusRsp {}

// 批量修改问答过期时间请求
message BatchModifyQaExpireReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 问答id集合
  repeated string qa_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
  // 知识库有效结束时间，unix时间戳，0代表永久有效
  string expire_end = 3;
}
// 批量修改问答过期时间
message BatchModifyQaExpireRsp {}

// 批量修改问答关联文档请求
message BatchModifyQaDocReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 问答id集合
  repeated string qa_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
  // 关联的文档id
  string doc_biz_id = 3;
}
// 批量修改问答关联文档响应
message BatchModifyQaDocRsp {}


// 批量编辑文档适用范围请求
message ModifyDocAttrRangeReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档业务ID【ids】
  repeated string doc_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
  // 属性标签适用范围 1：全部，2：按条件
  uint32 attr_range = 3;
  // 属性标签引用
  repeated AttrLabelRefer attr_labels = 4;
}

// 批量编辑文档适用范围响应
message ModifyDocAttrRangeRsp {}

// 更新QA状态请求
message ReleaseDetailNotifyReq {
  // QAID / SegmentID
  uint64 id = 1;
  // 是否发布成功
  bool is_success = 2;
  // 失败原因
  string reason = 3;
  // 机器人ID
  uint64 robot_id = 4;
  // 版本ID
  uint64 version_id = 5;
  // 类型 1:qa 2:segment 3:拒答问题
  uint32 type = 6;
}

// 更新QA状态响应
message ReleaseDetailNotifyRsp {}

// 发布结果同步请求
message ReleaseNotifyReq {
  // 机器人ID
  uint64 robot_id = 1;
  // 版本ID
  uint64 version_id = 2;
  // 是否成功
  bool is_success = 3;
  // 失败原因
  string message = 4;
  // 透传字段
  string transparent = 5;
  // 回调方标识 0：vector,1：任务型
  uint32 callback_source = 6;
  // 机器人业务ID
  uint64 robot_biz_id = 7;
}

// 发布结果同步响应
message ReleaseNotifyRsp {}

// 获取评测版本ID请求
message GetReviewVersionReq {}

// 获取评测版本ID响应
message GetReviewVersionRsp {
  // 版本ID
  repeated uint64 version_id = 1;
}

// 发布文档预览请求
message ListReleaseDocPreviewReq {
  // 查询内容
  string query = 1;
  // 版本ID
  string release_biz_id = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 行为 (1新增2修改3删除)
  repeated uint32 actions = 5;
  // 页码
  uint32 page_number = 6 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 7 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  string bot_biz_id = 8 [(validate.rules).string = {min_len: 1}];
}

// 发布文档预览响应
message ListReleaseDocPreviewRsp {
  message Doc {
    // 文档名称
    string file_name = 1;
    // 文档类型
    string file_type = 2;
    // 更新时间
    int64 update_time = 3;
    // 状态
    uint32 action = 4;
    // 状态描述
    string action_desc = 5;
    // 失败原因
    string message = 9;
    // 文档业务ID
    uint64 doc_biz_id = 10;
  }
  uint64 total = 1;
  repeated Doc list = 2;
}

message ListReleaseQAPreviewReq {
  // 查询内容
  string query = 1;
  // 版本ID
  string release_biz_id = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
  // 状态 (1新增2修改3删除)
  repeated uint32 actions = 5;
  // 页码
  uint32 page_number = 6 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 7 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  string bot_biz_id = 8 [(validate.rules).string = {min_len: 1}];
  // 发布状态
  repeated uint32 release_status = 9;
}

message ListReleaseQAPreviewRsp {
  message QA {
    // 问题
    string question = 1;
    // 更新时间
    int64 update_time = 2;
    // 状态
    uint32 action = 3;
    // 状态描述
    string action_desc = 4;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 5;
    // 来源描述
    string source_desc = 6;
    // 文档名称
    string file_name = 7;
    // 文档类型
    string file_type = 8;
    // 失败原因
    string message = 9;
    // 发布状态
    uint32 release_status = 10;
    // QAID
    uint64 qa_biz_id = 11;
    // 文档业务ID
    uint64 doc_biz_id = 12;
  }
  uint64 total = 1;
  repeated QA list = 2;
}

// 发布拒答问题预览请求
message ListRejectedQuestionPreviewReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 查询内容
  string query = 2;
  // 版本ID
  string release_biz_id = 3;
  // 状态 (1新增2修改3删除)
  repeated uint32 actions = 4;
  // 开始时间
  int64 start_time = 5;
  // 结束时间
  int64 end_time = 6;
  // 页码
  uint32 page_number = 7 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 8 [(validate.rules).uint32.gt = 0];
}

// 发布拒答问题预览响应
message ListRejectedQuestionPreviewRsp {
  // 发布拒答问题预览列表
  message RejectedQuestions {
    // 被拒答的问题
    string question = 1;
    // 更新时间
    int64 update_time = 2;
    // 状态
    uint32 action = 3;
    // 状态描述
    string action_desc = 4;
    // 失败原因
    string message = 5;
  }
  // 拒答问题预览总数
  uint64 total = 1;
  // 拒答问题列表
  repeated RejectedQuestions list = 2;
}

// 是否存在未确认问答
message CheckUnconfirmedQaReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 是否存在未确认问答
message CheckUnconfirmedQaRsp {
  // 是否存在
  bool exist = 1;
}

// 拉取相似问答对
message GetQaSimilarReq {
  // 页码
  uint32 page = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 拉取相似问答对
message GetQaSimilarRsp {
  // 相似问答对数量
  uint64 total = 1;
  // 相似问答对数量
  repeated uint64 similar_ids = 2;
}

// 拉取相似问答对
message ListQaSimilarReq {
  // 页码
  uint32 page_number = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 拉取相似问答对
message ListQaSimilarRsp {
  // 相似问答对数量
  uint64 total = 1;
  // QA相似问业务ID
  repeated uint64 similar_biz_ids = 2;
}

// 拉取相似问答对详情
message GetQaSimilarDetailReq {
  // 相似问答对ID
  uint64 similar_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 拉取相似问答对详情
message GetQaSimilarDetailRsp {
  message QA {
    // QA ID
    uint64 id = 1;
    // 问题
    string question = 2;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 3;
    // 来源描述
    string source_desc = 4;
    // 答案
    string answer = 5;
    // 文档名称
    string file_name = 6;
    // 文档类型
    string file_type = 7;
    // 更新时间
    int64 update_time = 8;
    // 文档id
    uint64 doc_id = 9;
  }
  repeated QA list = 1;
}

// 拉取相似问答对详情
message DescribeQaSimilarReq {
  // 相似问答对业务ID
  uint64 similar_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 拉取相似问答对详情
message DescribeQaSimilarRsp {
  message QA {
    // QA业务 ID
    uint64 qa_biz_id = 1;
    // 问题
    string question = 2;
    // 来源(1 文档生成 2 批量导入 3 手动添加)
    uint32 source = 3;
    // 来源描述
    string source_desc = 4;
    // 答案
    string answer = 5;
    // 文档名称
    string file_name = 6;
    // 文档类型
    string file_type = 7;
    // 更新时间
    int64 update_time = 8;
    // 文档业务ID
    uint64 doc_biz_id = 9;
  }
  repeated QA list = 1;
}

// 提交相似问答对选项
message SubmitQaSimilarReq {
  message QaSimilarInfo {
    uint64 similar_id = 1;
    uint64 del_qa_id = 2;
    uint64 save_qa_id = 3;
    // 是否忽略问答对
    bool is_ignore = 4;
    uint64 similar_biz_id = 5;
    uint64 del_qa_biz_id = 6;
    uint64 save_qa_biz_id = 7;
  }
  repeated QaSimilarInfo qa_similar_info = 1;
  // 是否忽略全部
  bool is_ignore_all = 2;
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 提交相似问答对选项
message SubmitQaSimilarRsp {}

// OpParams 操作参数
message OpParams {
  // cos路径
  string cos_path = 1;
  // 发布ID
  uint64 version_id = 2;
  uint32 appeal_type = 3;
  uint64 doc_biz_id = 4;
}

// OP 操作
message OP {
  // 操作类型 1跳转到文档库 2跳转到发布页详情 3跳转到问答库
  uint32 type = 1;
  // 操作参数
  OpParams params = 2;
}

// TaskType 任务类型枚举
enum TaskType {
  ModifyAttributeLabel = 0;
  ExportAttributeLabel = 1;
}

// GetTaskStatusReq 获取任务状态 请求
message GetTaskStatusReq {
  // 任务ID
  string task_id = 1 [(validate.rules).string.min_len = 1];
  // 任务类型
  string task_type = 2 ;
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string = {min_len: 1}];
}

// TaskStatus 任务状态
enum TaskStatus {
  // 执行中
  RUNNING = 0;
  // 成功
  SUCCESS = 1;
  // 失败
  FAILED = 2;
  // 等待中
  PENDING = 3;
}

// GetTaskStatusRsp 获取任务状态 响应
message GetTaskStatusRsp {
  string task_id = 1;
  // 任务类型
  string task_type = 2;
  // 任务状态
  string status = 3;
  // 任务消息,如失败信息
  string message = 4;
  // 任务参数
  TaskParams params = 5;
}

// TaskParams 任务参数
message TaskParams {
  // 下载地址,需要通过cos桶临时秘钥去下载
  string cos_path = 1;
}

// 特征标签
message VectorLabel {
  // 标签名
  string name = 1;
  // 标签值，一个标签多个标签值
  repeated string values = 2;
}

// 检索的额外信息，如排序和分数等字段
message RetrievalExtra  {
  int32 emb_rank = 1;
  float es_score = 2;
  int32 es_rank = 3;
  float rerank_score = 4;
  int32 rerank_rank = 5;
  float rrf_score = 6;
  int32 rrf_rank = 7;
}

// 检索结果类型
enum RetrievalResultType {
  RETRIEVAL = 0;    // 向量/混合检索的结果
  TEXT2SQL = 1;    // text2sql的结果
  IMAGE_RETRIEVAL_IMAGE = 2; // 图搜图
  TEXT_RETRIEVAL_IMAGE = 3;  // 文搜图
}

// 问题查询请求
message SearchPreviewReq {
  // 机器人business_id
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
  // 过滤器名称
  string filter_key = 3;
  // labels 标签
  repeated VectorLabel labels = 4;
  // 使用占位符
  bool use_placeholder = 5;
  // 图片URL
  repeated string image_urls = 6;
  // 搜索范围，1是QA 2是segment，为空时全量搜索，不为空时指定范围搜索
  uint32 search_scope = 7;
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 8;
  // 模型名称
  string model_name = 9;
}

// 问题查询响应
message SearchPreviewRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 1是QA 2是segment
    uint32 doc_type = 2;
    // QAID/SegmentID
    uint64 related_id = 3;
    // 问题
    string question = 4;
    // qa答案
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 文档片段
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
    // 自定义参数 qa自定义参数
    string custom_param = 13;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 14;
    // 检索的额外信息
    RetrievalExtra extra = 15;
    // 检索命中的图片URL
    repeated string image_urls = 16;
    // 检索结果类型
    RetrievalResultType result_type = 17;
    // 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
    SimilarQuestionExtra similar_question_extra = 18;
    // 检索切片的sheet信息 RetrievalResultType为TEXT2SQL时有值，匹配参考来源时使用，json格式存储
    string sheet_info = 19;
    // 问题描述 qa意图的描述
    string question_desc = 20;
  }
  repeated Doc docs = 1;
}

// 向量特征检索请求
message SearchReq {
  // 机器人business_id
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
  // 过滤器名称
  string filter_key = 3;
  // labels 标签
  repeated VectorLabel labels = 4;
  // 内容使用占位符替换链接和图片
  bool use_placeholder = 8;
  // 图片URL
  repeated string image_urls = 6;
  // 搜索范围，1是QA 2是segment，为空时全量搜索，不为空时指定范围搜索
  uint32 search_scope = 7;
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 9;
  // 模型名称
  string model_name = 10;
}

// 向量特征检索响应
message SearchRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    // 对应文档类型关联的数据ID
    uint64 related_id = 3;
    // 问题, 当文档类型为 QA(1) 时有效
    string question = 4;
    // 答案, 当文档类型为 QA(1) 时有效
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 文档段, 当文档类型为 文档段(2) 时有效
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
    // 自定义参数 当文档类型为 QA(1) 时有效
    string custom_param = 13;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 14;
    // 检索的额外信息
    RetrievalExtra extra = 15;
    // 检索命中的图片URL
    repeated string image_urls = 16;
    // 检索结果类型
    RetrievalResultType result_type = 17;
    // 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
    SimilarQuestionExtra similar_question_extra = 18;
    // 检索切片的sheet信息 RetrievalResultType为TEXT2SQL时有值，匹配参考来源时使用，json格式存储
    string sheet_info = 19;
    // 检索到的文档标题
    string doc_title = 20;
    // 问题描述 qa意图的描述
    string question_desc = 21;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 向量特征检索请求
message SearchReleaseReq {
  // 机器人business_id
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
  // 过滤器名称
  string filter_key = 3;
  // labels 标签
  repeated VectorLabel labels = 4;
  // 内容使用占位符替换链接和图片
  bool use_placeholder = 8;
  // 图片URL
  repeated string image_urls = 6;
  // 搜索范围，1是QA 2是segment，为空时全量搜索，不为空时指定范围搜索
  uint32 search_scope = 7;
}

// 向量特征检索响应
message SearchReleaseRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    // 对应文档类型关联的数据ID
    uint64 related_id = 3;
    // 问题, 当文档类型为 QA(1) 时有效
    string question = 4;
    // 答案, 当文档类型为 QA(1) 时有效
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 文档段, 当文档类型为 文档段(2) 时有效
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
    // 自定义参数 当文档类型为 QA(1) 时有效
    string custom_param = 13;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 14;
    // 检索的额外信息
    RetrievalExtra extra = 15;
    // 检索命中的图片URL
    repeated string image_urls = 16;
    // 检索结果类型
    RetrievalResultType result_type = 17;
    // 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
    SimilarQuestionExtra similar_question_extra = 18;
    // 检索切片的sheet信息 RetrievalResultType为TEXT2SQL时有值，匹配参考来源时使用，json格式存储
    string sheet_info = 19;
    // 检索到的文档标题
    string doc_title = 20;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 向量特征检索请求
message SearchKnowledgeReleaseReq {
  // 应用business_id
  uint64 app_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
}

// 向量特征检索响应
message SearchKnowledgeReleaseRsp {
  message KnowledgeItem {
    // 检索知识对应类型，QA:问答对，DOC:文档
    string knowledge_type = 1;
    // 检索知识ID
    string knowledge_id = 2;
    // 检索到的问题，当文档类型为QA时有效
    string question = 3;
    // 检索到内容，QA：问题的答案，DOC：为检索文档片段
    string content = 4;
    // 文档标题，当文档类型为DOC时有效
    string title = 5;
    // 关联文档ID，当文档类型为DOC时有效
    string related_doc_id = 6;
  }
  // 文档数据
  repeated KnowledgeItem knowledge_list = 1;
}


// 问题查询请求
message CustomSearchPreviewReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  message Filter {
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
  }
  repeated Filter filters = 3;
  // 取前 n 条 (默认3)
  uint32 top_n = 4;
  // labels 标签
  repeated VectorLabel labels = 5;
  // 使用占位符
  bool use_placeholder = 6;
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 7;
  // 模型名称
  string model_name = 8;
}

// 问题查询响应
message CustomSearchPreviewRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 1是QA 2是segment
    uint32 doc_type = 2;
    // QAID/SegmentID
    uint64 related_id = 3;
    // 问题
    string question = 4;
    // qa答案
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 原始文本
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
    // 检索的额外信息
    RetrievalExtra extra = 13;
    // 图片URL
    repeated string image_urls = 14;
    // 自定义参数 当文档类型为 QA(1) 时有效
    string custom_param = 15;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 16;
    // 检索结果类型
    RetrievalResultType result_type = 17;
    // 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
    SimilarQuestionExtra similar_question_extra = 18;
    // 检索切片的sheet信息 RetrievalResultType为TEXT2SQL时有值，匹配参考来源时使用，json格式存储
    string sheet_info = 19;
    // 问题描述 qa意图的描述
    string question_desc = 20;
  }
  repeated Doc docs = 1;
}

// 问题查询请求
message CustomSearchReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  message Filter {
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
  }
  // 筛选器
  repeated Filter filters = 3;
  // 取前 n 条 (默认3)
  uint32 top_n = 4;
  // labels 标签
  repeated VectorLabel labels = 5;
  // 使用占位符
  bool use_placeholder = 6;
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 7;
  // 模型名称
  string model_name = 8;
}

// 问题查询响应
message CustomSearchRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 文档类型 (1:QA 2:文档段 4:搜索引擎)
    uint32 doc_type = 2;
    // 对应文档类型关联的数据ID
    uint64 related_id = 3;
    // 问题, 当文档类型为 QA(1) 或 搜索引擎(4) 时有效
    string question = 4;
    // 答案, 当文档类型为 QA(1) 或 搜索引擎(4) 时有效
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 原始文本, 当文档类型为 文档段(2) 时有效
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
    // 检索的额外信息
    RetrievalExtra extra = 13;
    // 图片URL
    repeated string image_urls = 14;
    // 自定义参数 当文档类型为 QA(1) 时有效
    string custom_param = 15;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 16;
    // 检索结果类型
    RetrievalResultType result_type = 17;
    // 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
    SimilarQuestionExtra similar_question_extra = 18;
    // 检索切片的sheet信息 RetrievalResultType为TEXT2SQL时有值，匹配参考来源时使用，json格式存储
    string sheet_info = 19;
    // 问题描述 qa意图的描述
    string question_desc = 20;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 匹配来源请求
message MatchReferReq {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    // 对应文档类型关联的数据ID
    uint64 related_id = 3;
    // 问题, 当文档类型为 QA(1) 时有效
    string question = 4;
    // 答案, 当文档类型为 QA(1) 时有效
    string answer = 5;
    // 文档段, 当文档类型为 文档段(2) 时有效
    string org_data = 7;
    // 是否big_data true-表示org_data是由big_data填充 当文档类型为 文档段(2) 时有效
    bool is_big_data = 8;
    // 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
    SimilarQuestionExtra similar_question_extra = 9;
    // 文档段的sheet信息 json格式 当文档类型为 文档段(2) 时有效
    string sheet_info = 10;
  }
  // 机器人business_id
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 推理前的文档原文
  repeated Doc docs = 2;
  // 大模型回复内容
  string answer = 3 [(validate.rules).string = {min_len: 1}];
  // 是否匹配的是发布版本
  bool is_release = 4;
  // 消息ID
  string msg_id = 5 [(validate.rules).string.min_len = 1];
  // 问题
  string question = 6 [(validate.rules).string = {min_len: 1}];
  // 是否忽略相似度计算，默认false，为true则忽略相似度计算，默认全部关联；否则走相似度计算并过滤
  bool ignore_confidence_score = 7;
}

// 匹配来源响应
message MatchReferRsp {
  message Refer {
    // 来源ID
    uint64 refer_id = 1;
    // 文档类型 (1:QA 2:文档段)
    uint32 doc_type = 2;
    // 文档名称/问题
    string name = 3;
    // 文档链接
    string url = 4;
    // 文档类型 (1:QA 2:文档段 3:文档)
    uint32 refer_type = 5;
    // 文档ID
    uint64 doc_id = 6;
    // 文档业务ID
    uint64 doc_biz_id = 7;
    // 问答业务ID
    uint64 qa_biz_id = 8;
    // 文档名称（当doc_biz_id不为0时有效）
    string doc_name = 9;
  }
  // 来源数据
  repeated Refer Refers = 1;
}

// 获取来源 请求
message GetReferDetailReq {
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  repeated uint64 ids = 2 [(validate.rules).repeated .min_items = 1];
}

// 获取来源 响应
message GetReferDetailRsp {// TODO @sinute 前端接口
  message Detail {
    uint64 refer_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    string doc_name = 3;
    // 分片内容, 待下个版本前端修改后可删除 @sinutelu
    // /qbot/admin/getReferDetail
    //   page_content -> org_data
    string page_content = 4 [deprecated = true];
    string question = 5;
    string answer = 6;
    // 置信度
    float confidence = 7;
    // 标记
    uint32 mark = 8;
    // 分片高亮内容
    repeated Highlight highlights = 9;
    string org_data = 10;
  }
  repeated Detail details = 1;
}

// 获取来源 请求
message DescribeReferReq {
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  repeated string refer_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
}

// 获取来源 响应
message DescribeReferRsp {// TODO @sinute 前端接口
  message ReferDetail {
    uint64 refer_biz_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    string doc_name = 3;
    // 分片内容, 待下个版本前端修改后可删除 @sinutelu
    // /qbot/admin/getReferDetail
    //   page_content -> org_data
    string page_content = 4 [deprecated = true];
    string question = 5;
    string answer = 6;
    // 置信度
    float confidence = 7;
    // 标记
    uint32 mark = 8;
    // 分片高亮内容
    repeated Highlight highlights = 9;
    string org_data = 10;
    // 页码信息
    repeated uint32 page_infos = 11;
    // sheet信息
    repeated string sheet_infos = 12;
    // 文档ID
    uint64 doc_biz_id = 13;
  }
  repeated ReferDetail list = 1;
}

// 来源打标 请求
message MarkReferReq {
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // Deprecated
  uint64 refer_id = 2;
  uint32 mark = 3;
  uint64 refer_biz_id = 4;
}

// 来源请求 响应
message MarkReferRsp {}

// 导出QA列表请求
message ExportQAListReq {
  ListQAReq filters = 1; // 根据筛选数据导出
  string bot_biz_id = 2; // 机器人ID
  repeated string qa_biz_ids = 3; // 勾选导出
}

// 导出QA列表响应
message ExportQAListRsp {}

// Deprecated 导出QA列表请求
message ExportQAListReqV1 {
  GetQAListReq filters = 1; // 根据筛选数据导出
  uint64 bot_biz_id = 2; // 机器人ID
  repeated uint64 qa_ids = 3; // 勾选导出
}

// Deprecated 导出QA列表响应
message ExportQAListRspV1 {}

// 机器人模型配置
message RobotModelInfo {
  // 提示词
  string prompt = 1;
  // 提示词内容字符限制
  uint32 prompt_words_limit = 2;
  // 对话历史条数限制
  uint32 history_limit = 6;
  // 对话历史条数限制
  uint32 history_words_limit = 7;
  // 模型名称 (trpc 接口使用)
  string model_name = 8;
  // 下游服务名 (trpc 接口使用)
  string service_name = 9;
  // 模型是否开启
  bool is_enabled = 10;

  // 模型调用接口路由 (http 接口使用)
  string path = 3 [deprecated = true];
  // 模型调用接口地址 (http 接口使用)
  string target = 4 [deprecated = true];
  // 模型类型 (http 接口使用)
  uint32 type = 5 [deprecated = true];
}

// 机器人模型配置
message AppModelInfo {
  // 提示词
  string prompt = 1;
  // 提示词内容字符限制
  uint32 prompt_words_limit = 2;
  // 对话历史条数限制
  uint32 history_limit = 6;
  // 对话历史条数限制
  uint32 history_words_limit = 7;
  // 模型名称 (trpc 接口使用)
  string model_name = 8;
  // 下游服务名 (trpc 接口使用)
  string service_name = 9;
  // 模型是否开启
  bool is_enabled = 10;

  // 模型调用接口路由 (http 接口使用)
  string path = 3 [deprecated = true];
  // 模型调用接口地址 (http 接口使用)
  string target = 4 [deprecated = true];
  // 模型类型 (http 接口使用)
  uint32 type = 5 [deprecated = true];
}

// 机器人索引配置
message RobotFilters {
  // 总计TopN
  uint32 top_n = 1;
  // 索引配置
  repeated RobotFiltersInfo filter = 2;
}

// 机器人索引配置
message AppFilters {
  // 总计TopN
  uint32 top_n = 1;
  // 索引配置
  repeated AppFiltersInfo filter = 2;
}

// 机器人索引配置子项
message RobotFiltersInfo {
  // 文档类型
  uint32 doc_type = 1;
  uint32 index_id = 2;
  // 置信度
  float confidence = 3;
  // 子项TopN
  uint32 top_n = 4;
}

// 相似分数
message RougeScore {
  double f = 1;
  double p = 2;
  double r = 3;
}

// 机器人索引配置子项
message AppFiltersInfo {
  // 文档类型
  uint32 doc_type = 1;
  uint32 index_id = 2;
  // 置信度
  float confidence = 3;
  // 子项TopN
  uint32 top_n = 4;
  // Rouge分数
  RougeScore rouge_score = 5;
  // 是否启用
  bool is_enable = 6;
}

// 机器人切分配置
message RobotSplitDoc {
  // 文档解析配置
  RobotSplitDocParserConfig parser_config = 1;
  // 切分配置
  RobotSplitDocSplitterConfig splitter_config = 2;
  // 合并配置
  RobotSplitDocMergerConfig merger_config = 3;
  // 重组配置
  RobotSplitDocRechunkConfig rechunk_config = 4;
  // 模式切分配置
  RobotSplitDocPatternSplitterConfig pattern_splitter_config = 5;
}

// 机器人切分配置
message AppSplitDoc {
  // 文档解析配置
  AppSplitDocParserConfig parser_config = 1;
  // 切分配置
  AppSplitDocSplitterConfig splitter_config = 2;
  // 合并配置
  AppSplitDocMergerConfig merger_config = 3;
  // 重组配置
  AppSplitDocRechunkConfig rechunk_config = 4;
  // 模式切分配置
  AppSplitDocPatternSplitterConfig pattern_splitter_config = 5;
}


// RobotEmbedding 机器人 embedding 配置
message RobotEmbedding {
  // embedding 版本
  uint64 Version = 1;
}

// AppEmbedding 应用 embedding 配置
message AppEmbedding {
  // embedding 版本
  uint64 Version = 1;
}

// RobotSplitDocParserConfig 文档解析配置
message RobotSplitDocParserConfig {
  // 是否作为一整段处理
  bool single_paragraph = 1;
}

// AppSplitDocParserConfig 文档解析配置
message AppSplitDocParserConfig {
  // 是否作为一整段处理
  bool single_paragraph = 1;
}


// RobotSplitDocPatternSplitterConfig 模式切分配置
message RobotSplitDocPatternSplitterConfig {
  string regexp_json = 1;
}

// AppSplitDocPatternSplitterConfig 模式切分配置
message AppSplitDocPatternSplitterConfig {
  string regexp_json = 1;
}

// RobotSplitDocSplitterConfig 切分配置
message RobotSplitDocSplitterConfig {
  // 切分器
  string splitter = 1;
  // 句切分配置
  SplitterSentenceConfig splitter_sentence_config = 2;
  // token 切分配置
  SplitterTokenConfig splitter_token_config = 3;
}

// AppSplitDocSplitterConfig 切分配置
message AppSplitDocSplitterConfig {
  // 切分器
  string splitter = 1;
  // 句切分配置
  SplitterSentenceConfig splitter_sentence_config = 2;
  // token 切分配置
  SplitterTokenConfig splitter_token_config = 3;
}

// SplitterSentenceConfig 按句子切分
message SplitterSentenceConfig {
  // 是否处理表格
  bool enable_table = 1;
  // 是否处理图片
  bool enable_image = 2;
  // 句符号
  repeated string sentence_symbols = 3;
  // 最大分块大小
  uint64 max_mini_chunk_length = 4;
}

// SplitterTokenConfig 按 token 切分
message SplitterTokenConfig {
  // 是否处理表格
  bool enable_table = 1;
  // 是否处理图片
  bool enable_image = 2;
  // 切片大小
  uint32 mini_chunk_length = 3;
}

// RobotSplitDocMergerConfig 合并配置
message RobotSplitDocMergerConfig {
  // 合并器
  string merger = 1;
  // 按长度合并
  MergerLengthConfig merger_length_config = 2;
  // 按 mini chunk 数量合并
  MergerAmountConfig merger_amount_config = 3;
}

// AppSplitDocMergerConfig 合并配置
message AppSplitDocMergerConfig {
  // 合并器
  string merger = 1;
  // 按长度合并
  MergerLengthConfig merger_length_config = 2;
  // 按 mini chunk 数量合并
  MergerAmountConfig merger_amount_config = 3;
}

// MergerLengthConfig 按长度合并
message MergerLengthConfig {
  // 页体长度
  uint32 page_content_length = 1;
  // 页头长度
  uint32 head_overlap_length = 2;
  // 页尾长度
  uint32 tail_overlap_length = 3;
  // 表体长度
  uint32 table_page_content_length = 4;
  // 表头长度
  uint32 table_head_overlap_length = 5;
  // 表尾长度
  uint32 table_tail_overlap_length = 6;
  // 按符号trim
  repeated string trim_by_symbols = 7;
}

// MergerAmountConfig 按数量合并
message MergerAmountConfig {
  // 页体 mini chunk 数量
  uint32 page_content_size = 1;
  // 页头 mini chunk 数量
  uint32 head_overlap_size = 2;
  // 页尾 mini chunk 数量
  uint32 tail_overlap_size = 3;
  // 表体长度
  uint32 table_page_content_length = 4;
  // 表头  mini chunk 数量
  uint32 table_head_overlap_size = 5;
  // 表尾  mini chunk 数量
  uint32 table_tail_overlap_size = 6;
  // 按符号trim
  repeated string trim_by_symbols = 7;
}

// RobotSplitDocRechunkConfig 重组配置
message RobotSplitDocRechunkConfig {
  // 重组时, 头部分块的重组数
  uint32 head_overlap_size = 1;
  // 重组时, 尾部分块的重组数
  uint32 tail_overlap_size = 2;
  // 根据符号trim
  repeated string trim_by_symbols = 3;
}

// AppSplitDocRechunkConfig 重组配置
message AppSplitDocRechunkConfig {
  // 重组时, 头部分块的重组数
  uint32 head_overlap_size = 1;
  // 重组时, 尾部分块的重组数
  uint32 tail_overlap_size = 2;
  // 根据符号trim
  repeated string trim_by_symbols = 3;
}

// 机器人相似度配置
message RobotSearchVector {
  // 置信度
  float confidence = 1;
  // top_n 最多匹配条数
  uint32 top_n = 2;
}

// 应用相似度配置
message AppSearchVector {
  // 置信度
  float confidence = 1;
  // top_n 最多匹配条数
  uint32 top_n = 2;
}

message CreateAppealReq {
  // 机器人ID
  uint64 bot_biz_id = 1;
  // 审核类型
  uint32 appeal_type = 2 [(validate.rules).uint32.gt = 0];
  // Deprecated 发布版本ID
  uint64 version_id = 3;
  // Deprecated 问答ID
  uint64 qa_id = 4;
  // 申诉原因
  string reason = 5;
  // 发布版本ID
  uint64 release_biz_id = 7;
  // 问答ID
  uint64 qa_biz_id = 8;
  // 文档bizID
  string doc_biz_id = 9;
}

message CreateAppealRsp {}

// 获取意图请求
message GetIntentReq {
  // 意图策略
  uint32 policy_id = 1;
  // 意图名称
  string name = 2;
}

// 获取意图响应
message GetIntentRsp {
  // 意图名称
  string name = 1;
  // 意图类型
  string category = 2;
  // 所属策略ID
  uint32 policy_id = 3;
}

message UpdateAuditStatusReq {
  // t_audit 表中的 父审核 id
  uint64 audit_parent_id = 1;
  // t_audit 表中的 审核 id
  uint64 audit_id = 2;
  // 人工审核结果 是否通过
  bool is_pass = 3;
  // type t_audit 表中的类型 含义保持一致
  uint32 audit_type = 4;
  // 最后一次审核人
  string operator = 5;
  // 申诉单父 id
  uint64 appeal_parent_id = 6;
  // 申诉 id
  uint64 appeal_id = 7;
  // 申诉任务完成
  bool appeal_task_done = 8;
  // 申诉人工审核成功数
  uint32 num_success = 9;
  // 申诉人工审核失败数
  uint32 num_fail = 10;
  // 申诉人工审核总数
  uint32 num_total = 11;
  // t_appeal 申诉表 关联 id
  uint64 relate_id = 12;
  // corp_id 企业 ID
  uint64 corp_id = 13;
  // robot_id 机器人 ID
  uint64 robot_id = 14;
  // 申诉单创建人
  uint64 create_staff_id = 15;
}

message UpdateAuditStatusRsp {}

message GetDocsReq {
  repeated uint64 ids = 1;
}

message GetDocsRsp {
  message Doc {
    uint64 id = 1;
    string page_content = 2;
    string org_data = 3;
  }
  repeated Doc docs = 1;
}

// 待标注测试会话详情请求
message JudgeReq {
  // Deprecated 任务ID
  uint64 test_id = 1;
  // Deprecated 单条评测记录ID
  uint64 record_id = 2;
  // 1 准确 2错误
  uint32 answer_judge = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
  // 任务ID
  uint64 test_biz_id = 5;
  // 单条评测记录ID
  uint64 record_biz_id = 6;
}

// 待标注测试会话详情响应
message JudgeRsp {}

// 待标注测试记录详情请求
message GetOneJudgingReq {
  // 任务ID
  uint64 test_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 待标注测试记录详情响应
message GetOneJudgingRsp {
  // 总评测记录数量（包括已经评测和没有评测的）
  uint32 test_num = 1;
  // 已经完成标注数量
  uint32 judge_num = 2;
  // 单条评测记录ID
  uint64 record_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
}

// 待标注测试记录详情请求
message DescribeWaitJudgeRecordReq {
  // 任务ID
  uint64 test_biz_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 待标注测试记录详情响应
message DescribeWaitJudgeRecordRsp {
  // 总评测记录数量（包括已经评测和没有评测的）
  uint32 test_number = 1;
  // 已经完成标注数量
  uint32 judge_number = 2;
  // 单条评测记录ID
  uint64 record_biz_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
  // 回复类型
  uint32 reply_method = 6;

  string record = 7; // 历史消息记录
}

message MsgRecord {
  message Reference {
    uint64 id = 1;
    string url = 2;
    uint32 type = 3;
    string name = 4;
    uint64 doc_id = 5;
  }
  message TaskFlow {
    string  task_flow_name  = 1; // 任务流程名字
    string  task_flow_id    = 2; // 任务流程 id
    string  query_rewrite   = 3; // query 重写后内容
    string  hit_intent      = 4; // 命中意图
    uint32  type            = 5; // 回复类型, 0: 任务流回复; 1: 任务流静默; 2: 任务流拉回话术; 3: 任务流自定义回复
  }
  string content = 1;
  string record_id = 2;
  string related_record_id = 3;
  bool is_from_self = 4;
  string from_name = 5;
  string from_avatar = 6;
  int64 timestamp = 7; // 看代码, 这里返回的是创建时间的 s 单位 数据, boyucao 标注
  bool has_read = 8;
  uint32 score = 9;
  bool can_rating = 10;
  uint32 type = 11;
  repeated Reference references = 12;
  repeated string reasons = 13;
  bool is_llm_generated = 14;
  repeated string image_urls    = 15;
  TokenStat       token_stat    = 16; // token 统计信息
  uint32          reply_method  = 17; // 回复
  bool            can_feedback  = 18; // 控制前端反馈
  repeated string option_cards  = 19; // 卡片
  string          session_id    = 20; // 当前记录所在的 Session ID
  TaskFlow        task_flow     = 21; // 任务流程信息
  repeated FileInfo file_infos = 22; // 文件信息
}
// 值的类型
enum ValueType {
  VT_UNKNOWN = 0;   // 未知或者空内容
  STRING = 1;
  INT = 2;
  FLOAT = 3;
  BOOL = 4;
  ARRAY = 5;        // 字符串数组
  OBJECT_ARRAY = 6; // 结构体数组
  OBJECT = 7;       // 结构体
}

// token 统计
message TokenStat {
  string session_id  = 1; // 会话 ID
  string request_id  = 2; // 请求 ID
  string record_id   = 3; // 对应哪条会话, 会话 ID, 用于回答的消息存储使用, 可提前生成, 保存消息时使用

  uint32 used_count  = 4; // token 已使用数
  uint32 free_count  = 5; // 免费 token 数
  uint32 order_count = 6; // 订单总 token 数

  string status_summary         = 7;  // 当前执行状态汇总, 参考常量 ProcedureStatus* (使用中, 成功, 失败)
  string status_summary_title   = 8;  // 当前执行状态汇总后中文展示
  uint32 elapsed                = 9;  // 当前请求执行时间, 单位 ms
  uint32 token_count            = 10; // 当前请求消耗 token 数

  repeated Procedure procedures = 11; // 过程列表

  string trace_id   = 12; // traceID 调试信息展示使用
}
// Procedure 执行过程
message Procedure {
  string  name  = 1; // 英文名, 参考本文件常量定义
  string  title = 2; // 中文名, 用于展示
  string  status= 3; // 状态, 参考常量 ProcedureStatus* (使用中, 成功, 失败)
  uint32  count = 4; // 消耗 token 数
  ProcedureDebugging debugging = 5; // 调试信息
  uint32  resource_status = 6; // 计费资源状态，1：可用，2：不可用
}

message ProcedureDebugging {
  string content = 1;
  string system = 2;
  repeated HistorySummary histories = 3;
  repeated KnowledgeSummary knowledge = 4;
  TaskFlowSummary task_flow = 5;
}

message HistorySummary {
  string user = 1;
  string assistant = 2;
}

message KnowledgeSummary {
  uint32 type = 1;
  string content = 2;
}

message TaskFlowSummary {
  string intent_name = 1;
  repeated ValueInfo updated_slot_values = 2;
  repeated RunNodeInfo run_nodes = 3;
  repeated string purposes = 4;
}

message ValueInfo {
  string ID = 1;
  string Name = 2;
  ValueType ValueType = 3;
  string ValueStr = 4;
  int64 ValueInt = 5;
  float ValueFloat = 6;
  bool ValueBool = 7;
  repeated string ValueStrArray = 8;
}

message RunNodeInfo {
  FlowNodeType NodeType = 1;  // 节点类型
  string NodeID = 2;          // 节点ID
  string NodeName = 3;        // 节点名称
  InvokeAPI InvokeAPI = 4;    // 请求的API
  repeated ValueInfo SlotValues = 5; // 当前节点的所有槽位的值，key：SlotID。没有值的时候也要返回空。
}

// 节点类型
enum FlowNodeType {
  UNKNOWN = 0;    // 未指定
  //  START = 1 ;     // 开始节点，StartNodeData
  API = 2;        // API节点， APINodeData
  REQUEST = 3;    // 询问节点， RequestNodeData
  ANSWER = 4;     // 答案节点， AnswerNodeData
}

message StrValue {
  string Name = 1;
  string Value = 2;
}

message InvokeAPI {
  string Method = 1;           // 请求方法，如GET/POST等
  string URL = 2;              // 请求地址。
  repeated StrValue HeaderValues = 3;     // header参数
  repeated StrValue QueryValues = 4;      // 入参Query
  string RequestPostBody = 5;             // Post请求的原始数据
  string ResponseBody = 6;                // 返回的原始数据
  repeated ValueInfo ResponseValues = 7;  // 出参
  string FailMessage = 8;                 // 异常信息
}


// 查询标注记录详情请求
message GetRecordReq {
  // 任务ID
  uint64 test_id = 1;
  // 单条评测记录ID
  uint64 record_id = 2;
  // 机器人ID
  uint64 bot_biz_id = 3;
}

// 查询标注记录详情响应
message GetRecordRsp {
  // 总评测记录数量（包括已经评测和没有评测的）
  uint32 test_num = 1;
  // 已经完成标注数量
  uint32 judge_num = 2;
  // 单条评测记录ID
  uint64 record_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
  // 0 待标注 1 准确 2错误
  uint32 answer_judge = 6;
}

// 查询标注记录详情请求
message DescribeRecordReq {
  // 任务ID
  uint64 test_biz_id = 1;
  // 单条评测记录ID
  uint64 record_biz_id = 2;
  // 机器人ID
  uint64 bot_biz_id = 3;
}

// 查询标注记录详情响应
message DescribeRecordRsp {
  // 总评测记录数量（包括已经评测和没有评测的）
  uint32 test_number = 1;
  // 已经完成标注数量
  uint32 judge_number = 2;
  // 单条评测记录ID
  uint64 record_biz_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
  // 0 待标注 1 准确 2错误
  uint32 answer_judge = 6;
}

// 导出评测任务请求
message ExportEvaluateTaskReq {
  // 任务ID
  uint64 test_biz_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 导出评测任务请求
message ExportEvaluateTaskRsp {
  // cos路径
  string cos_path = 1;
}

// 任务删除请求
message DeleteTestReq {
  // 任务ID
  repeated uint64 test_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}
// 任务停止请求
message StopTestReq {
  // 任务ID
  repeated uint64 test_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 任务重试请求
message RetryTestReq {
  // 任务ID
  repeated uint64 test_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 任务删除响应
message DeleteTestRsp {}
// 任务停止响应
message StopTestRsp {}
// 任务重试响应
message RetryTestRsp {}

// 任务删除请求
message DeleteEvaluateTestReq {
  // 任务ID
  repeated uint64 test_biz_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}
// 任务停止请求
message StopEvaluateTestReq {
  // 任务ID
  repeated uint64 test_biz_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 任务重试请求
message RetryEvaluateTestReq {
  // 任务ID
  repeated uint64 test_biz_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 任务删除响应
message DeleteEvaluateTestRsp {}
// 任务停止响应
message StopEvaluateTestRsp {}
// 任务重试响应
message RetryEvaluateTestRsp {}

// 条件查询任务请求
message QueryTestReq {
  // 测试任务名称
  string test_name = 1 [(validate.rules).string = {max_len: 50}];
  // 页码
  uint32 page = 2;
  // 页面大小
  uint32 page_size = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 条件查询任务响应
message QueryTestRsp {
  // 任务数量
  uint32 total = 1;
  // 任务列表
  repeated SampleTest tests = 2;
}

// 条件查询任务请求
message ListEvaluateTestReq {
  // 测试任务名称
  string test_name = 1 [(validate.rules).string = {max_len: 50}];
  // 页码
  uint32 page_number = 2;
  // 页面大小
  uint32 page_size = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 条件查询任务响应
message ListEvaluateTestRsp {
  // 任务数量
  uint32 total = 1;
  // 任务列表
  repeated SampleTestDetail list = 2;
}

// 样本集详情
message SampleTestDetail {
  // 任务ID
  uint64 test_biz_id = 3;
  // 任务名称
  string test_name = 4;
  // 样本数量
  uint32 test_number = 5;
  // 导入时间
  uint64 create_time = 6;
  // 已经完成标注数量
  uint32 judge_number = 7;
  // 已经完成标注数量
  uint32 judge_right_number = 8;
  // 任务状态0 待评测  1 评测中  2 标注中 3 标注完成 4 删除 5 评测失败 6 人工停止
  uint32 status = 9;
  // 错误原因
  string message = 10;
  // 已经完成评测数量 -1 历史数据展示 - ,其他为具体数量
  int32 test_done_number = 11;
}

// 样本集详情
message SampleTest {
  // 任务ID
  uint64 test_id = 3;
  // 任务名称
  string test_name = 4;
  // 样本数量
  uint32 test_num = 5;
  // 导入时间
  uint64 create_time = 6;
  // 已经完成标注数量
  uint32 judge_num = 7;
  // 已经完成标注数量
  uint32 judge_right_num = 8;
  // 任务状态0 待评测  1 评测中  2 标注中 3 标注完成 4 删除 5 评测失败 6 人工停止
  uint32 status = 9;
  // 错误原因
  string message = 10;
}

// 创建评测任务请求
message CreateTestReq {
  // 集合ID
  uint64 set_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
  // 测试任务名称
  string test_name = 3 [(validate.rules).string = {max_len: 50}];
}

// 创建评测任务响应
message CreateTestRsp {
  // 任务ID Deprecated 上云后需删除
  uint64 test_id = 1;
}

// 创建评测任务请求
message CreateEvaluateTestReq {
  // 集合业务ID
  uint64 set_biz_id = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
  // 测试任务名称
  string test_name = 3 [(validate.rules).string = {max_len: 50}];
}

// 创建评测任务响应
message CreateEvaluateTestRsp {
  // 任务业务ID
  uint64 test_biz_id = 1;
}

// 批量删除样本集合请求
message DeleteSampleReq {
  // 集合ID列表
  repeated uint64 set_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 批量删除样本集合响应
message DeleteSampleRsp {}

// 批量删除样本集合请求
message DeleteSampleSetReq {
  // 集合ID列表
  repeated uint64 set_biz_ids = 1;
  // 机器人ID
  uint64 bot_biz_id = 2;
}

// 批量删除样本集合响应
message DeleteSampleSetRsp {}

// 查询样本集请求
message QuerySampleReq {
  // 文件名/集合名
  string set_name = 1;
  // 页码
  uint32 page = 2;
  // 页面大小
  uint32 page_size = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 样本集列表响应
message QuerySampleRsp {
  // 样本集数量
  uint32 total = 1;
  // 样本集
  repeated SampleSet samples = 2;
}

// 样本集详情
message SampleSet {
  // 集合名
  uint64 set_id = 3;
  // 集合名
  string set_name = 4;
  // 样本数量
  uint32 num = 5;
  // 导入时间
  uint64 create_time = 6;
}

// 查询样本集请求
message ListSampleSetReq {
  // 文件名/集合名
  string set_name = 1;
  // 页码
  uint32 page_number = 2;
  // 页面大小
  uint32 page_size = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 样本集列表响应
message ListSampleSetRsp {
  // 样本集数量
  uint32 total = 1;
  // 样本集
  repeated SampleSetDetail list = 2;
}

// 样本集详情
message SampleSetDetail {
  // 集合名
  uint64 set_biz_id = 3;
  // 集合名
  string set_name = 4;
  // 样本数量
  uint32 number = 5;
  // 导入时间
  uint64 create_time = 6;
}

// 保存样本文件请求
message UploadSampleReq {
  // 文件名/集合名
  string file_name = 1 [(validate.rules).string = {max_len: 50}];
  // cos路径
  string cos_url = 2;
  // ETag 全称为 Entity Tag，是对象被创建时标识对象内容的信息标签，可用于检查对象的内容是否发生变化
  string e_tag = 3;
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4;
  // 文件大小
  uint32 size = 5;
  // 机器人ID
  uint64 bot_biz_id = 6;
}

// 保存样本文件响应
message UploadSampleRsp {
  // 文档 ID
  uint64 id = 1;
  // 文档内样例数量
  uint32 total = 2;
  // 导入错误
  string error_msg = 3;
  // 错误链接
  string error_link = 4;
  // 错误链接文本
  string error_link_text = 5;
}

// 保存样本文件请求
message UploadSampleSetReq {
  // 文件名/集合名
  string file_name = 1 [(validate.rules).string = {max_len: 50}];
  // cos路径
  string cos_url = 2;
  // e_tag 全称为 Entity Tag,对象被创建时标识对象内容的信息标签
  string e_tag = 3;
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4;
  // 文件大小
  uint64 size = 5;
  // 机器人ID
  uint64 bot_biz_id = 6;
}

// 保存样本文件响应
message UploadSampleSetRsp {
  // 文档 业务ID
  uint64 set_biz_id = 1;
  // 文档内样例数量
  uint32 total = 2;
  // 导入错误
  string error_msg = 3;
  // 错误链接
  string error_link = 4;
  // 错误链接文本
  string error_link_text = 5;
}

// 带校验保存样本文件校验请求
message UploadSampleSetWithCheckReq {
  // 文件名/集合名
  string file_name = 1 [(validate.rules).string = {max_len: 50}];
  // cos路径
  string cos_url = 2;
  // e_tag 全称为 Entity Tag,对象被创建时标识对象内容的信息标签
  string e_tag = 3;
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4;
  // 文件大小
  uint64 size = 5;
  // 机器人ID
  uint64 bot_biz_id = 6;
}

// 带校验保存样本文件校验响应
message UploadSampleSetWithCheckRsp {
  // 文档 业务ID
  uint64 set_biz_id = 1;
  // 文档内样例数量
  uint32 total = 2;
  // 导入错误
  string error_msg = 3;
  // 错误链接
  string error_link = 4;
  // 错误链接文本
  string error_link_text = 5;
  // 是否允许导入
  bool is_allow = 6;
}

// 获取拒答问题列表请求
message GetRejectedQuestionListReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询内容
  string query = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
  // 页码
  uint32 page_number = 5;
}

// 获取拒答问题列表响应
message GetRejectedQuestionListRsp {
  // 拒答问题列表
  message RejectedQuestions {
    // 拒答问题ID
    uint64 id = 1;
    // 被拒答的问题
    string question = 2;
    // 状态
    uint32 status = 3;
    // 状态描述
    string status_desc = 4;
    // 更新时间
    int64 update_time = 5;
    // 是否允许编辑
    bool is_allow_edit = 6;
    // 是否允许删除
    bool is_allow_delete = 7;
  }
  // 拒答问题总数
  uint64 total = 1;
  // 拒答问题列表
  repeated RejectedQuestions list = 2;
}

// 获取拒答问题列表请求
message ListRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 查询内容
  string query = 2;
  // 页码
  uint32 page_number = 3 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
  //操作行为：1新增 2修改 3删除 4发布
  repeated uint32 actions = 5;
}

// 获取拒答问题列表响应
message ListRejectedQuestionRsp {
  // 拒答问题列表
  message RejectedQuestions {
    // 拒答问题ID
    uint64 rejected_biz_id = 1;
    // 被拒答的问题
    string question = 2;
    // 状态
    uint32 status = 3;
    // 状态描述
    string status_desc = 4;
    // 更新时间
    int64 update_time = 5;
    // 是否允许编辑
    bool is_allow_edit = 6;
    // 是否允许删除
    bool is_allow_delete = 7;
  }
  // 拒答问题总数
  uint64 total = 1;
  // 拒答问题列表
  repeated RejectedQuestions list = 2;
}

// 创建拒答问题请求
message CreateRejectedQuestionReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 拒答问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 拒答问题来源
  uint32 business_source = 3 [(validate.rules).uint32 = {in: [1, 2]}];
  // 拒答问题来源的数据源唯一id
  string business_id = 4;
}

// 创建拒答问题响应
message CreateRejectedQuestionRsp {}

// 修改拒答问题请求
message UpdateRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 拒答问题id
  uint64 id = 2;
  // 拒答问题
  string question = 3 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 拒答问题id
  uint64 rejected_biz_id = 4;
}

// 修改拒答问题响应
message UpdateRejectedQuestionRsp {}

// 修改拒答问题请求
message ModifyRejectedQuestionReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 拒答问题id
  string id = 2;
  // 拒答问题
  string question = 3 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 拒答问题id
  string rejected_biz_id = 4;
}

// 修改拒答问题响应
message ModifyRejectedQuestionRsp {}

// 删除拒答问题请求
message DeleteRejectedQuestionReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 拒答问题id列表
  repeated string ids = 2;
  // 拒答问题id业务列表
  repeated string rejected_biz_ids = 3;
}

// 删除拒答问题响应
message DeleteRejectedQuestionRsp {}

// 导出拒答问题请求
message ExportRejectedQuestionReq {
  GetRejectedQuestionListReq filters = 1;
  // 需要导出的拒答问题id列表
  repeated uint64 rejected_question_ids = 2;
  // 需要导出的拒答问题id列表
  repeated uint64 rejected_biz_ids = 3;
}

// 导出拒答问题响应
message ExportRejectedQuestionRsp {}

// 拒答问题测评库查询请求
message SearchPreviewRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 3;
  // 模型名称
  string model_name = 4;
}

// 拒答问题测评库查询响应
message SearchPreviewRejectedQuestionRsp {
  // 拒答问题测评库查询命中结果列表
  message RejectedQuestions {
    // 拒绝问题ID
    uint64 id = 1;
    // 内容
    string question = 2;
    // 置信度
    float confidence = 3;
  }
  repeated RejectedQuestions list = 1;
}

// 拒答问题线上库查询请求
message SearchReleaseRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 3;
  // 模型名称
  string model_name = 4;
}

// 拒答问题线上库查询响应
message SearchReleaseRejectedQuestionRsp {
  // 拒答问题测评库查询命中结果列表
  message RejectedQuestions {
    // 拒绝问题ID
    uint64 id = 1;
    // 内容
    string question = 2;
    // 置信度
    float confidence = 3;
  }
  repeated RejectedQuestions list = 1;
}

// 添加不满意回复请求
message AddUnsatisfiedReplyReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 消息记录ID
  string record_id = 2 [(validate.rules).string = {min_len: 1}];
  // 用户问题
  string question = 3 [(validate.rules).string = {min_len: 1}];
  // 机器人回复答案
  string answer = 4 [(validate.rules).string = {min_len: 1}];
  // 机器人回复上下文
  repeated UnsatisfiedReplyContext context = 5 [(validate.rules).repeated .min_items = 1];
  // 错误原因,C端反馈可不传原因
  repeated string reasons = 6;
}

// 不满意回复上下文
message UnsatisfiedReplyContext {
  // 消息记录ID
  string record_id = 1 [(validate.rules).string = {min_len: 1}];
  // 是否为用户
  bool is_visitor = 2;
  // 是否为机器人
  bool is_robot = 3;
  // 消息发送者ID
  uint64 from_id = 4 [(validate.rules).uint64 = {gte: 1}];
  // 消息内容
  string content = 5;
  // 文档信息
  repeated FileInfo file_infos = 6;
  // 回复类型
  uint32 reply_method = 7;
}

// 文档信息
message FileInfo {
  // 文档名称
  string file_name = 1;
  // 文档大小
  string file_size = 2;
  // 文档URL
  string file_url = 3;
  // 文档类型
  string file_type = 4;
  // 文档ID
  string doc_id = 5;
}

// 添加不满意回复响应
message AddUnsatisfiedReplyRsp {}

// 查询不满意回复列表
message GetUnsatisfiedReplyReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 检索，用户问题或答案
  string query = 2;
  // 错误类型检索
  repeated string reasons = 3;
  // 页码
  uint32 page = 4 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
}

// 查询不满意问题回复列表响应
message GetUnsatisfiedReplyRsp {
  message UnsatisfiedReply {
    // 不满意回复ID
    uint64 id = 1;
    // 消息记录ID
    string record_id = 2;
    // 用户问题
    string question = 3;
    // 机器人回复
    string answer = 4;
    // 错误类型
    repeated string reasons = 5;
  }
  // 总数
  uint64 total = 1;
  repeated UnsatisfiedReply list = 2;
}

// 查询不满意回复列表
message ListUnsatisfiedReplyReq {
  // 机器人业务ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 检索，用户问题或答案
  string query = 2;
  // 错误类型检索
  repeated string reasons = 3;
  // 页码
  uint32 page_number = 4 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
}

// 查询不满意问题回复列表响应
message ListUnsatisfiedReplyRsp {
  message UnsatisfiedReply {
    // 不满意回复ID
    uint64 reply_biz_id = 1;
    // 消息记录ID
    string record_biz_id = 2;
    // 用户问题
    string question = 3;
    // 机器人回复
    string answer = 4;
    // 错误类型
    repeated string reasons = 5;
  }
  // 总数
  uint64 total = 1;
  repeated UnsatisfiedReply list = 2;
}

// 批量忽略不满意回复
message IgnoreUnsatisfiedReplyReq {
  // 机器人业务ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
  // Deprecated 不满意回复ID数组
  repeated string ids = 2;
  // 不满意回复ID数组 后续需添加TRPC框架校验
  repeated string reply_biz_ids = 3;
}

message IgnoreUnsatisfiedReplyRsp {}

// 导出不满意回复请求
message ExportUnsatisfiedReplyReq {
  message Filters {
    // 检索，用户问题或答案
    string query = 1;
    // 错误类型检索
    repeated string reasons = 2;
  }
  // 机器人业务ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 根据筛选数据导出
  Filters filters = 2;
  // Deprecated 勾选导出
  repeated string ids = 3;
  // 勾选导出
  repeated string reply_biz_ids = 4;
}

// 导出不满意回复响应
message ExportUnsatisfiedReplyRsp {}

// 获取不满意回复上下文请求
message GetUnsatisfiedReplyContextReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 不满意问题ID
  uint64 id = 2 [(validate.rules).uint64 = {gte: 1}];
}

// 获取不满意回复上下文响应
message GetUnsatisfiedReplyContextRsp {
  message Context {
    // 消息记录ID信息
    string record_id = 1;
    // 是否为用户
    bool is_visitor = 2;
    // 昵称
    string nick_name = 3;
    // 头像
    string avatar = 4;
    // 消息内容
    string content = 5;
  }
  // 不满意回复上下文
  repeated Context list = 1;
}

// 获取不满意回复上下文请求
message DescribeUnsatisfiedReplyReq {
  // 机器人业务ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 不满意问题ID
  string reply_biz_id = 2 [(validate.rules).string.min_len = 1];
}

// 获取不满意回复上下文响应
message DescribeUnsatisfiedReplyRsp {
  message Context {
    // 消息记录ID信息
    string record_biz_id = 1;
    // 是否为用户
    bool is_visitor = 2;
    // 昵称
    string nick_name = 3;
    // 头像
    string avatar = 4;
    // 消息内容
    string content = 5;
    // 文档信息
    repeated FileInfo file_infos = 6;
    // 回复类型
    uint32 reply_method = 7;
  }
  // 不满意回复上下文
  repeated Context list = 1;
}

// 任务信息
message TaskInfo {
  // 任务ID
  uint64 id = 1;
  // 机器人ID
  uint64 robot_id = 2;
  // 任务类型
  uint32 task_type = 3;
  // 任务互斥
  uint32 task_mutex = 4;
  // 任务参数
  string params = 5;
  // 重试次数
  uint32 retry_times = 6;
  // 最大重试次数
  uint32 max_retry_times = 7;
  // 超时时间(s)
  uint32 timeout = 8;
  // 执行器
  string runner = 9;
  // 执行器实例
  string runner_instance = 10;
  // 本次结果
  string result = 11;
  // trace id
  string trace_id = 12;
  // 任务开始执行时间
  int64 start_time = 13;
  // 任务完成时间
  int64 end_time = 14;
  // 下次任务开始执行时间
  int64 next_start_time = 15;
  // 创建时间
  int64 create_time = 16;
  // 更新时间
  int64 update_time = 17;
}

// 历史任务信息
message TaskHistoryInfo {
  // 任务ID
  uint64 id = 1;
  // 机器人ID
  uint64 robot_id = 2;
  // 任务类型
  uint32 task_type = 3;
  // 任务互斥
  uint32 task_mutex = 4;
  // 任务参数
  string params = 5;
  // 重试次数
  uint32 retry_times = 6;
  // 最大重试次数
  uint32 max_retry_times = 7;
  // 超时时间(s)
  uint32 timeout = 8;
  // 执行器
  string runner = 9;
  // 执行器实例
  string runner_instance = 10;
  // 本次结果
  string result = 11;
  // 是否成功
  bool is_success = 12;
  // trace id
  string trace_id = 13;
  // 任务开始执行时间
  int64 start_time = 14;
  // 任务完成时间
  int64 end_time = 15;
  // 下次任务开始执行时间
  int64 next_start_time = 16;
  // 创建时间
  int64 create_time = 17;
}

// 获取admin任务列表
message GetAdminTaskListReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 任务类型
  repeated uint32 task_type = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 获取admin任务列表响应
message GetAdminTaskListRsp {
  // 总数
  uint64 total = 1;
  repeated TaskInfo list = 2;
}

// 获取admin历史任务列表
message GetAdminTaskHistoryListReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 任务类型
  repeated uint32 task_type = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 获取admin任务历史列表响应
message GetAdminTaskHistoryListRsp {
  // 总数
  uint64 total = 1;
  repeated TaskHistoryInfo list = 2;
}

// 获取vector_doc任务列表
message GetVectorDocTaskListReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 任务类型
  repeated uint32 task_type = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 获取vector_doc任务列表响应
message GetVectorDocTaskListRsp {
  // 总数
  uint64 total = 1;
  repeated TaskInfo list = 2;
}

// 获取vector_doc任务历史列表
message GetVectorDocTaskHistoryListReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 任务类型
  repeated uint32 task_type = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 获取vector_doc任务历史列表响应
message GetVectorDocTaskHistoryListRsp {
  // 总数
  uint64 total = 1;
  repeated TaskHistoryInfo list = 2;
}

// 属性标签信息
message AttributeLabelV1 {
  // 标签ID
  uint64 label_id = 1;
  // 标签名称
  string label_name = 2 [(validate.rules).string = {min_len: 1}];
  // 相似标签名称
  repeated string similar_labels = 3 [(validate.rules).repeated = {items: {string: {min_len: 1}}}];
}

// 属性标签信息
message AttributeLabel {
  // 标签ID
  string label_biz_id = 1;
  // 标签名称
  string label_name = 2 [(validate.rules).string = {min_len: 1}];
  // 相似标签名称
  repeated string similar_labels = 3 [(validate.rules).repeated = {items: {string: {min_len: 1}}}];
}

// 创建属性请求
message CreateAttributeLabelV1Req {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性标识
  string attr_key = 2 [(validate.rules).string = {min_len: 1}];
  // 属性名称
  string attr_name = 3 [(validate.rules).string = {min_len: 1}];
  // 属性标签
  repeated AttributeLabelV1 labels = 4 [(validate.rules).repeated .min_items = 1];
}

// 创建属性标签响应
message CreateAttributeLabelV1Rsp {}

// 创建属性请求
message CreateAttributeLabelReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 属性标识
  string attr_key = 2;// [(validate.rules).string = {min_len: 1}];
  // 属性名称
  string attr_name = 3 [(validate.rules).string = {min_len: 1}];
  // 属性标签
  repeated AttributeLabel labels = 4 [(validate.rules).repeated .min_items = 1];
}

// 创建属性标签响应
message CreateAttributeLabelRsp {
  // 标签ID
  string attr_biz_id = 1;
}

// Deprecated 删除属性标签
message DeleteAttributeLabelReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 属性ID
  repeated string ids = 2;
  // 属性ID
  repeated string attribute_biz_ids = 3;
}

message DeleteAttributeLabelRsp {}

// 编辑属性标签请求
message UpdateAttributeLabelReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性ID
  uint64 id = 2 [(validate.rules).uint64.gt = 0];
  // 属性标识
  string attr_key = 3;// [(validate.rules).string = {min_len: 1}];
  // 属性名称
  string attr_name = 4 [(validate.rules).string = {min_len: 1}];
  // 删除的属性标签
  repeated uint64 delete_label_ids = 5;
  // 新增或编辑的属性标签
  repeated AttributeLabelV1 labels = 6;
}

// 编辑属性标签响应
message UpdateAttributeLabelRsp {}

// 编辑属性标签请求
message ModifyAttributeLabelReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 属性ID
  string attribute_biz_id = 2 [(validate.rules).string = {min_len: 1}];
  // 属性标识
  string attr_key = 3;// [(validate.rules).string = {min_len: 1}];
  // 属性名称
  string attr_name = 4 [(validate.rules).string = {min_len: 1}];
  // 删除的属性标签
  repeated string delete_label_biz_ids = 5;
  // 新增或编辑的属性标签
  repeated AttributeLabel labels = 6;
}

// 编辑属性标签响应
message ModifyAttributeLabelRsp {
  // 任务ID
  uint64 task_id = 1;
}

// 查询属性标签列表请求
message GetAttributeLabelListReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 查询内容
  string query = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 查询属性标签列表响应
message GetAttributeLabelListRsp {
  // 属性标签
  message AttrLabel {
    // 属性ID
    uint64 id = 1;
    // 属性标识
    string attr_key = 2;
    // 属性名称
    string attr_name = 3;
    // 标签名称
    repeated string label_names = 4;
    // 属性标签是否在更新中
    bool is_updating = 5;
    // 发布状态
    uint32 status = 6;
    // 发布状态描述
    string StatusDesc = 7;
  }
  uint64 total = 1;
  // 列表
  repeated AttrLabel list = 2;
}

// 查询属性标签列表请求
message ListAttributeLabelReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 查询内容
  string query = 2;
  // 页码
  uint32 page_number = 3 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
  // 每个属性同步拉取的标签值数量
  uint32 label_size = 5;
}

// 查询属性标签列表响应
message ListAttributeLabelRsp {
  // 属性标签
  message AttrLabelDetail {
    // 属性ID
    uint64 attr_biz_id = 1;
    // 属性标识
    string attr_key = 2;
    // 属性名称
    string attr_name = 3;
    // 标签名称
    repeated string label_names = 4;
    // 属性标签是否在更新中
    bool is_updating = 5;
    // 发布状态
    uint32 status = 6;
    // 发布状态描述
    string StatusDesc = 7;
    // 标签总数
    uint64 label_total_count = 8;
  }
  uint64 total = 1;
  // 列表
  repeated AttrLabelDetail list = 2;
}

// 查询属性标签详情请求
message GetAttributeLabelDetailReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 属性ID
  uint64 id = 2 [(validate.rules).uint64.gt = 0];
  // 查询标签或相似标签
  string query = 3;
  // 滚动加载游标的标签ID
  uint64 last_label_id = 4;
  // 每次加载数量
  uint32 limit = 5 [(validate.rules).uint32.gt = 0];
}

message GetAttributeLabelDetailRsp {
  // 属性ID
  uint64 id = 1;
  // 属性标识
  string attr_key = 2;
  // 属性名称
  string attr_name = 3;
  // 标签数量
  uint64 label_count = 4;
  // 标签名称
  repeated AttributeLabelV1 labels = 5;
}

// 查询属性标签详情请求
message DescribeAttributeLabelReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 属性ID
  string attribute_biz_id = 2 [(validate.rules).string = {min_len: 1}];
  // 查询标签标准词或相似词
  string query = 3;
  // 滚动加载游标的标签ID
  string last_label_biz_id = 4;
  // 每次加载数量
  uint32 limit = 5 [(validate.rules).uint32.gt = 0];
  // 查询范围 all(或者传空):标准词和相似词 standard:标准词 similar:相似词
  string query_scope = 6;
}

message DescribeAttributeLabelRsp {
  // 属性ID
  uint64 attribute_biz_id = 1;
  // 属性标识
  string attr_key = 2;
  // 属性名称
  string attr_name = 3;
  // 标签数量
  uint64 label_number = 4;
  // 标签名称
  repeated AttributeLabel labels = 5;
}

// 导入属性标签请求
message UploadAttributeLabelReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 文件名
  string file_name = 2 [(validate.rules).string = {min_len: 1, max_len: 255}];
  // cos路径
  string cos_url = 3 [(validate.rules).string.min_len = 1];
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4 [(validate.rules).string.min_len = 1];
  // 文件大小
  string size = 5 [(validate.rules).string.min_len = 1];
}

// 导入属性标签响应
message UploadAttributeLabelRsp {
  // 导入错误
  string error_msg = 1;
  // 错误链接
  string error_link = 2;
  // 错误链接文本
  string error_link_text = 3;
}

// 导出属性标签请求
message ExportAttributeLabelReq {
  message Filters {
    // 检索，属性或标签名称
    string query = 1;
  }
  // 机器人业务ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 根据筛选数据导出
  Filters filters = 2;
  // Deprecated 勾选导出
  repeated string ids = 3;
  // 勾选导出
  repeated string attribute_biz_ids = 4;
}

// 导出属性标签响应
message ExportAttributeLabelRsp {
  // 任务ID
  uint64 task_id = 1;
}

// 检查属性下标签是否引用请求
message CheckAttributeLabelReferReq {
  // 机器人业务ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // Deprecated 属性IDs
  repeated string ids = 2;
  // Deprecated 属性标签ID
  string label_id = 3;
  // 属性IDs 后续补充必要校验[(validate.rules).repeated = {items: {uint64: {gte: 1}}}];
  repeated string attribute_biz_id = 4;
  // 属性标签ID
  string label_biz_id = 5;
}

// 检查属性下标签是否引用响应
message CheckAttributeLabelReferRsp {
  // 是否引用
  bool is_refer = 1;
  // 引用的工作流
  repeated trpc.KEP.bot_task_config_server.AttributeLabelRefByWorkflow list = 2;
}

// 检查属性下的标签名是否存在请求
message CheckAttributeLabelExistReq {
  // 机器人业务ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // Deprecated 属性ID
  string id = 2;
  // Deprecated滚动加载，最后一个属性标签ID
  string last_label_id = 3;
  // 属性标签名称
  string label_name = 4 [(validate.rules).string.min_len = 1];
  // 属性ID 后续补充[(validate.rules).repeated = {items: {uint64: {gte: 1}}}];
  string attribute_biz_id = 5;
  // 滚动加载，最后一个属性标签ID
  string last_label_biz_id = 6;
}

// 检查属性下的标签名是否存在响应
message CheckAttributeLabelExistRsp {
  // 是否存在
  bool is_exist = 1;
}

// 记录用户最后一次访问待校验问答时间请求
message RecordUserAccessUnCheckQATimeReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 记录用户最后一次访问待校验问答时间响应
message RecordUserAccessUnCheckQATimeRsp {}

// 操作生成问答标记请求
message RecordUserFirstGenQAReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 操作生成问答标记响应
message RecordUserFirstGenQARsp {}

// 问题查询请求
message GlobalKnowledgeReq {
  // 问题
  string question = 1 [(validate.rules).string = {min_len: 1}];
  // 过滤器名称
  string filter_key = 2;
  // labels 标签
  repeated VectorLabel labels = 3;
}

// 问题查询响应
message GlobalKnowledgeRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 1是QA 2是segment
    uint32 doc_type = 2;
    // QAID/SegmentID
    uint64 related_id = 3;
    // 问题
    string question = 4;
    // qa答案
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 文档片段
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 检索额外信息，全局知识库场景没有用到，保持协议一致
    RetrievalExtra extra = 10;
    // 图片URL
    repeated string image_urls = 11;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 12;
    // 检索结果类型
    RetrievalResultType result_type = 13;
    // 检索切片的sheet信息 RetrievalResultType为TEXT2SQL时有值，匹配参考来源时使用，json格式存储
    string sheet_info = 14;
    // 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
    SimilarQuestionExtra similar_question_extra = 15;
  }
  repeated Doc docs = 1;
}

// 全局干预知识列表请求
message ListGlobalKnowledgeReq {
  // 查询内容
  string query = 1;
  // 页码
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
}

// 全局干预知识列表响应
message ListGlobalKnowledgeRsp {
  message GlobalKnowledge {
    // ID
    uint64 id = 1;
    // 问题
    string question = 2;
    // 答案
    string answer = 3;
    // 是否同步
    bool is_sync = 4;
    // 是否删除
    bool is_deleted = 5;
    // 创建时间
    int64 create_time = 6;
    // 更新时间
    int64 update_time = 7;
  }
  uint64 total = 1;
  repeated GlobalKnowledge list = 2;
}

// 添加全局干预知识
message AddGlobalKnowledgeReq {
  // 问题
  string question = 4 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // qa答案
  string answer = 5 [(validate.rules).string = {min_len: 1, max_len: 2000}];
}

// 添加全局干预知识
message AddGlobalKnowledgeRsp {
  // ID
  uint64 id = 1;
}

// 删除全局干预知识
message DelGlobalKnowledgeReq {
  // ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 删除添加全局干预知识
message DelGlobalKnowledgeRsp {}

// 更新全局干预知识
message UpdGlobalKnowledgeReq {
  // ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 4 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // qa答案
  string answer = 5 [(validate.rules).string = {min_len: 1, max_len: 2000}];
}

// 更新全局干预知识
message UpdGlobalKnowledgeRsp {}

// 强制同步全局干预知识
message ForceSyncGlobalKnowledgeReq {
  // ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 强制同步全局干预知识
message ForceSyncGlobalKnowledgeRsp {}

// 获取相似度
message CustomSimilarityReq {
  string ori = 1 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  repeated string docs = 2;
}

message CustomSimilarityRsp {
  message RougeScore {
    double f = 1;
    double p = 2;
    double r = 3;
  }
  repeated RougeScore scores = 1;
}

// 创建通知请求
message CreateNoticeReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 页面ID 标记页面
  uint32 page_id = 2;
  // 通知业务类型
  uint32 type = 3;
  // 通知级别
  string level = 4;
  // 业务ID
  uint64 relate_id = 5;
  // 通知主题
  string subject = 6;
  // 通知内容
  string content = 7;
  // 是否全局通知
  bool is_global = 8;
  // 是否允许被关闭
  bool is_allow_close = 9;
  // 企业ID
  uint64 corp_id = 10;
  // 员工ID
  uint64 staff_id = 11;
  // 通知操作
  message Operation {
    // 操作参数
    message Params {
      // cos下载地址
      string cos_path = 1;
      // 发布ID
      string version_id = 2;
      // 申诉类型
      uint32 appeal_type = 3;
    }
    // 对应操作type
    uint32 type = 1;
    // 操作参数
    Params params = 2;
  }
  // 操作列表
  repeated Operation operations = 12;
}

// 创建通知响应
message CreateNoticeRsp {}

// 应用基础配置
message BaseConfig {
  // 机器人昵称
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 20}];
  // 机器人头像
  string avatar = 2 [(validate.rules).string.min_len = 1];
  // 机器人描述
  string desc = 3 [(validate.rules).string = {min_len: 0, max_len: 200}];
}

// 模型配置
message AppModel {
  // 模型名称
  string name = 1;
  // 模型描述
  string desc = 2;
  // 多轮上下文指代轮次
  uint32 context_limit = 3;
  // 模型别名
  string alias_name = 4;
}

// 知识问答搜索配置
message KnowledgeQaSearch {
  // 知识来源 doc：文档，qa：问答  taskflow：业务流程，search：搜索增强
  string type = 1;
  // 问答-回复灵活度 1：已采纳答案直接回复 2：已采纳润色后回复
  uint32 reply_flexibility = 2;
  // 搜索增强-搜索引擎状态
  bool use_search_engine = 3;
  // 是否显示搜索引擎检索状态
  bool show_search_engine = 4;
  // 知识来源，是否选择
  bool is_enabled = 5;
  // 问答最大召回数量, 默认2，限制5
  uint32 qa_top_n = 6 [(validate.rules).uint32 = {gte:0, lte: 5}];
  // 问答最大召回数量, 默认2，限制5
  uint32 doc_top_n = 7 [(validate.rules).uint32 = {gte:0, lte: 5}];
}

// 知识应用输出配置
message KnowledgeQaOutput {
  // 输出方式 1：流式 2：非流式
  uint32 method = 1;
  // 是否使用行业通用知识库
  bool use_general_knowledge = 2;
  // 未知回复语，300字符以内
  string bare_answer = 3;
}

// 检索范围
message SearchRange {
  string condition = 1; //检索条件
  message ApiVarAttrInfo {
    string api_var_id = 1;
    uint64 attr_biz_id = 2;
  }
  repeated ApiVarAttrInfo api_var_attr_infos = 2;
}

// 知识问答配置
message KnowledgeQaConfig {
  // 欢迎语，200字符以内
  string greeting = 1 [(validate.rules).string = {min_len: 0, max_len: 200}];
  // 机器人描述(prompt 场景使用)
  string role_description = 2 [(validate.rules).string = {min_len: 0, max_len: 1000}];
  // 模型配置
  AppModel model = 3;
  // 检索配置
  repeated KnowledgeQaSearch search = 4;
  // 输出配置
  KnowledgeQaOutput output = 5;
  // 检索范围
  SearchRange search_range = 6;
}

// 知识摘要输出配置
message SummaryOutput {
  // 输出方式 1：流式 2：非流式
  uint32 method = 1;
  // 输出要求 1：文本总结 2：自定义要求
  uint32 requirement = 2;
  // 自定义要求指令
  string require_command = 3 [(validate.rules).string = {min_len: 0, max_len: 500}];
}

// 知识摘要应用配置
message SummaryConfig {
  // 模型配置
  AppModel model = 1;
  // 输出配置
  SummaryOutput output = 2;
}

// 标签信息
message ClassifyLabel {
  // 标签名称
  string name = 1 [(validate.rules).string = {min_len: 0, max_len: 10}];
  // 标签描述
  string description = 2 [(validate.rules).string = {min_len: 0, max_len: 100}];
  // 标签取值范围
  repeated string values = 3 [(validate.rules).repeated = {items: {string: {max_len: 1000}}}];
}

// 标签提取配置
message ClassifyConfig {
  // 模型配置
  AppModel model = 1;
  // 标签列表
  repeated ClassifyLabel labels = 2;
}

// 应用配置
message AppConfig {
  // 知识问答应用配置
  KnowledgeQaConfig knowledge_qa = 1;
  // 知识摘要应用配置
  SummaryConfig summary = 2;
  // 标签配置
  ClassifyConfig classify = 3;
}

// GetAppKnowledgeCountReq 获取知识库问答信息请求结构体
message GetAppKnowledgeCountReq {
  string app_biz_id = 1; // 应用ID
  string type = 2; // doc-文档，qa-问答对
}

// GetAppKnowledgeCountRsp 获取知识库问答信息返回结构体
message GetAppKnowledgeCountRsp {
  uint64 total = 1; // 个数
}

// 获取意图列表请求
message ListIntentReq{
  // 意图名称
  string name = 1;
  // 是否删除
  int32 is_deleted = 2;
  // 是否使用
  int32 is_used = 3;
  // 是否筛选了使用状态
  bool used_filter = 4;
  // 页码
  uint32 page = 5;
  // 页面大小
  uint32 page_size = 6;
}

// 获取意图列表响应
message ListIntentRsp{
  message Intents{
    // 意图ID
    uint64 intent_id = 1 [(validate.rules).uint64 = {gte: 1}];
    // 意图名称
    string name = 2;
    // 是否删除
    int32 is_deleted = 3;
    // 是否使用
    int32 is_used = 4;
    // 创建时间
    int64 create_time = 5;
    // 更新时间
    int64 update_time = 6;
    // 操作人
    string operator = 7;
  }
  // 意图列表
  repeated Intents list = 1;
  // 数量
  uint32 total = 2;
}

// 创建意图请求
message CreateIntentReq{
  // 意图名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 意图分类
  string category = 2;
  // 操作人
  string operator = 3;
  // 意图策略
  uint32 policy_id = 4;
}

// 创建意图响应
message CreateIntentRsp{}

// 更新意图请求
message UpdateIntentReq{
  // 意图ID
  uint64 intent_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 意图名称
  string name = 2;
  // 意图分类
  string category = 3;
  // 操作人
  string operator = 4;
  // 意图策略
  uint32 policy_id = 5;
}

// 更新意图响应
message UpdateIntentRsp{}

// 删除意图请求
message DeleteIntentReq{
  // 意图ID
  uint64 intent_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 操作人
  string operator = 2;
}

// 删除意图响应
message DeleteIntentRsp{}

// 获取策略列表请求
message ListIntentPolicyReq{
  // 策略名称
  string name = 1;
  // 页码
  uint32 page = 2;
  // 页面大小
  uint32 page_size = 3;
}

// 获取策略列表响应
message ListIntentPolicyRsp{
  // 意图
  message Intent{
    // 意图ID
    uint64 intent_id = 1;
    // 意图名称
    string name = 2;
    // 意图分类
    string category = 3;
  }
  // 策略
  message IntentPolicy{
    // 策略ID
    uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
    // 意图名称
    string name = 2;
    // 是否使用
    int32 is_used = 3;
    // 创建时间
    int64 create_time = 4;
    // 更新时间
    int64 update_time = 5;
    // 操作人
    string operator = 6;
    // 策略绑定的意图内容
    repeated Intent intents = 7;
  }
  // 策略列表
  repeated IntentPolicy list = 1;
  // 数量
  uint32 total = 2;
}

// 创建策略请求
message CreateIntentPolicyReq{
  // 策略名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 操作人
  string operator = 2;
  // 知识问答意图id列表
  repeated uint32 knowledge_intent_id = 3;
  // 搜索引擎id列表
  repeated uint32 engine_intent_id = 4;
  // 文档摘要id列表
  repeated uint32 summary_intent_id = 5;
  // 通用模型闲聊id列表
  repeated uint32 chat_intent_id = 6;
  // 自我认知id列表
  repeated uint32 self_intent_id = 7;
}

// 创建策略响应
message CreateIntentPolicyRsp{}

// 更新策略请求
message UpdateIntentPolicyReq{
  // 策略ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 策略名称
  string name = 2;
  // 需要更新的意图分类
  string category = 3;
  // 变更后的意图id列表
  repeated uint32 intent_id = 4;
  // 操作人
  string operator = 5;
  // 是否为新增绑定关系
  bool is_new = 6;
  // 是否手动删除所有意图
  bool is_manual = 7;
  // 配置入口意图名称
  string intent_name = 8;
}

// 更新策略响应
message UpdateIntentPolicyRsp{}

// 删除策略请求
message DeleteIntentPolicyReq{
  // 策略ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 操作人
  string operator = 2;
}

// 删除策略响应
message DeleteIntentPolicyRsp{}

// 获取策略绑定的意图列表请求
message ListIntentByPolicyIDReq{
  // 策略ID
  uint64 policy_id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 获取策略绑定的意图列表响应
message ListIntentByPolicyIDRsp{
  message Intents{
    // 意图ID
    uint64 intent_id = 1;
    // 意图名称
    string name = 2;
    // 意图分类
    string category = 3;
  }
  repeated Intents list = 1;
}

// 获取未使用未删除的策略意图列表请求
message ListUnusedIntentKeyMapReq{}

// 获取未使用未删除的策略意图列表响应
message ListUnusedIntentKeyMapRsp{
  message Options{
    // 意图ID
    uint64 intent_id = 1;
    // 意图名称
    string name = 2;
  }
  repeated Options options = 1;
}

// 获取策略列表请求
message ListIntentPolicyKeyMapReq{}

// 获取策略列表列表响应
message ListIntentPolicyKeyMapRsp{
  message Options{
    // 策略ID
    uint64 id = 1;
    // 策略名称
    string name = 2;
  }
  repeated Options options = 1;
}

// 新增自定义模型请求
message CreateCorpCustomModelReq{
  // 企业ID
  uint64 corp_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 模型名称
  string model_name = 2 [(validate.rules).string = {min_len: 1}];
  // 对话类型
  string model_type = 3 [(validate.rules).string = {min_len: 1}];
  // 应用类型
  string app_type = 4;
  // 模型展示名称
  string alias = 5 [(validate.rules).string = {min_len: 1}];
  // prompt
  string prompt = 6;
  // path
  string path = 7;
  // target
  string target = 8;
  // 对话历史内容字符限制
  uint32 history_words_limit = 9;
  // 对话历史条数限制
  uint32 history_limit = 10;
  // 下游服务名
  string service_name = 11;
  // prompt文本大小限制
  uint32 prompt_words_limit = 12;
  // 添加原因备注
  string note = 13;
  // 过期时间
  int64 expired_time = 14;
}

// 新增自定义模型响应
message CreateCorpCustomModelRsp{}

// 机器人检索配置
message RobotRetrievalConfig {
  bool enable_es_recall = 1;
  bool enable_vector_recall = 2;
  bool enable_rrf = 3;
  bool enable_text2sql = 4;
  float rerank_threshold = 5;
  float rrf_vec_weight = 6;
  float rrf_es_weight = 7;
  float rrf_rerank_weight = 8;
  string operator = 9;
  uint32 doc_vec_recall_num  = 10;
  uint32 qa_vec_recall_num   = 11;
  uint32 es_recall_num       = 12;
  uint32 es_rerank_min_num   = 13;
  int32 rrf_reciprocal_const = 14;
}

// 获取机器人检索配置请求
message GetRobotRetrievalConfigReq {
  // 机器人ID
  uint64 robot_id = 1;
}

// 获取机器人检索配置响应
message GetRobotRetrievalConfigRsp {
  RobotRetrievalConfig settings = 1;
}

// 保存检索配置请求
message SaveRobotRetrievalConfigReq {
  // 机器人ID
  uint64 robot_id = 1;
  // 检索配置信息
  RobotRetrievalConfig settings = 2;
}

// 保存检索配置响应
message SaveRobotRetrievalConfigRsp{}

// ListNonRoleModelReq 角色模型列表请求
message ListNonRoleModelReq {}

// ListNonRoleModelRsp 角色模型列表响应
message ListNonRoleModelRsp {
  repeated string list = 1;
}

// GetLikeDataCountReq 点赞点踩数据统计请求
message GetLikeDataCountReq {
  uint64  start_time = 1; //开始日期, 时间单位 unix 秒
  uint64  end_time = 2;   //结束日期, 时间单位 unix 秒
  repeated string  app_biz_id = 3;  //应用id
  uint32  type = 4;  //消息来源
}

// GetLikeDataCountRsp 点赞点踩数据统计响应
message GetLikeDataCountRsp {
  uint32   total = 1; // 可评价消息数
  uint32   appraisal_total = 2; // 评价数
  float    participation_rate = 3; // 参评率
  uint32   like_total = 4; // 点赞数
  float    like_rate = 5; // 点赞率
  uint32   dislike_total = 6; // 点踩数
  float    dislike_rate = 7; // 点踩率
}

// GetAnswerTypeDataCountReq 回答类型数据统计请求
message GetAnswerTypeDataCountReq {
  uint64 start_time = 1; //开始日期, 时间单位 unix 秒
  uint64 end_time = 2;   //结束日期, 时间单位 unix 秒
  repeated string  app_biz_id = 3;  //应用id
  uint32 type = 4;  //消息来源
}

// GetAnswerTypeDataCountRsp 回答类型数据统计响应
message GetAnswerTypeDataCountRsp {
  uint32   total = 1; // 总消息数
  uint32   model_reply_count = 2; // 大模型直接回复总数
  uint32   knowledge_count = 3; // 知识型回复总数
  uint32   task_flow_count = 4; // 任务流回复总数
  uint32   search_engine_count = 5; // 搜索引擎回复总数
  uint32   image_understanding_count = 6; // 图片理解回复总数
  uint32   reject_count = 7; // 拒答回复总数
  uint32   sensitive_count = 8; // 敏感回复总数
  uint32   concurrent_limit_count = 9; // 并发超限回复总数
  uint32   unknown_issues_count = 10; // 未知问题回复总数
}

// GetCharacterUsageReq 获取字符使用量与容量请求
message GetCharacterUsageReq {
}

// GetCharacterUsageRsp 获取账号下字符使用量与容量响应
message GetCharacterUsageRsp {
  uint32 total = 1;    // 知识库可用字符总数
  uint32 used = 2;     // 已使用的字符数
  uint32 exceed = 3;   // 已超量的字符数
}

// ResumeDocReq 恢复文档请求
message ResumeDocReq {
  string bot_biz_id = 1;           // 应用ID
  repeated string doc_biz_ids = 2; // 本次恢复的文档ID列表
}

// ResumeDocRsp 恢复文档响应
message ResumeDocRsp {
}

// ResumeQAReq 恢复问答请求
message ResumeQAReq {
  string bot_biz_id = 1;          // 应用ID
  repeated string qa_biz_ids = 2; // 本次恢复的问答ID列表
}

// ResumeQARsp 恢复问答响应
message ResumeQARsp {
}

// RenameDocReq 恢复文档请求
message RenameDocReq {
  string bot_biz_id = 1;           // 应用ID
  string doc_biz_id = 2;           // 本次恢复的文档ID列表
  string new_name = 4;             // 重命名新名称
}

// RenameDocRsp 恢复文档响应
message RenameDocRsp {
}

// NotifyKnowledgeCapacityExpiredReq 通知知识库容量到期请求
message NotifyKnowledgeCapacityExpiredReq {
  string uin = 1 [(validate.rules).string.min_len = 1]; // 腾讯云主账号
  double capacity = 2 [(validate.rules).double = {gt:0}]; // 本次到期资源包容量
  uint64 expire_time = 3 [(validate.rules).uint64.gt = 0]; // 本次到期资源包到期时间
  string resource_id = 4 [(validate.rules).string.min_len = 1]; // 资源包ID
}

// NotifyKnowledgeCapacityExpiredRsp 通知知识库容量到期响应
message NotifyKnowledgeCapacityExpiredRsp {
}


// DeleteDocSegmentImagesReq 删除文档切片图片请求
message DeleteDocSegmentImagesReq {
  // 应用ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 文档ID列表
  repeated uint64 doc_ids = 2 [(validate.rules).repeated = {
    min_items: 1,
    items: {uint64: {gt: 0}}
  }];
}

// DeleteDocSegmentImagesRsp 删除文档切片图片响应
message DeleteDocSegmentImagesRsp {

}

// GetCorpCharacterUsageReq 获取企业下应用字符数使用情况请求
message GetCorpCharacterUsageReq {
  // 页码
  uint32 page = 1;
  // 每页大小
  uint32 page_size = 2;
  // 企业ID
  uint64 corp_biz_id = 3;
  // 应用ID列表
  repeated uint64 app_biz_ids = 4;
}

// GetCorpCharacterUsageRsp 获取企业下应用字符数使用情况响应
message GetCorpCharacterUsageRsp {
  // 企业ID
  uint64 corp_biz_id = 1;
  // 企业知识库已用字符总数
  uint64 corp_used_char_size = 2;

  message UsageInfo {
    uint64 app_biz_id = 1;        // 应用ID
    uint64 used_char_size = 2;    // 应用知识库已用字符总数
    uint64 exceed_char_size = 3;  // 应用知识库超量字符总数
  }
  // 应用数量，如果指定了应用查询，这里是指定应用的数量
  uint32 total = 3;
  // 应用知识库容量使用情况列表
  repeated UsageInfo usage_info = 4;
}


message DocParsingInterventionReq {
  // 文件名，要跟原始文档保持一致
  string file_name = 1 [(validate.rules).string = {min_len: 1, max_len: 255}];
  // cos路径，要按照前端上传的规范来
  string cos_url = 2 [(validate.rules).string.min_len = 1];
  // 文件类型
  string file_type = 3 [(validate.rules).string.min_len = 1];
  // 机器人ID
  string bot_biz_id = 4 [(validate.rules).string = {min_len: 1}];
  // ETag 全称为 Entity Tag，是对象被创建时标识对象内容的信息标签，可用于检查对象的内容是否发生变化
  string e_tag = 5 [(validate.rules).string.min_len = 1];
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 6 [(validate.rules).string.min_len = 1];
  // 文件大小
  string size = 7 [(validate.rules).string = {min_len: 1}];

  // 原始文档ID，如果干预过多次，则是最近一次干预的文档ID
  uint64 origin_doc_id = 8;
}


message DocParsingInterventionRsp {
  // 原始文档ID，本次干预前的文档ID
  uint64 origin_doc_id = 1;
  // 新的文档ID
  uint64 doc_id = 2;
}

message UpdateAttrLabelsCacheProdReq {
  // 应用ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 属性标识
  repeated string attr_key = 2;
}

message UpdateAttrLabelsCacheProdRsp {}

// CheckVarIsUsedReq 检查自定义参数是否被使用入参
message CheckVarIsUsedReq {
  uint64 app_biz_id = 1;
  // 自定义变量信息
  string var_id = 2;
}

// CheckVarIsUsedReq 检查自定义参数是否被使用返回
message CheckVarIsUsedRsp {
  bool is_used = 1;
}

// ModifyAppVarReq 修改应用检索范围的自定义变量入参
message ModifyAppVarReq {
  message varInfo {
    string var_id = 1;
    string var_name = 2;
  }
  uint64 app_biz_id = 1;
  // 自定义变量信息
  varInfo var_info = 2;
}

// ModifyAppVarRsp 修改应用检索范围的自定义变量返回
message ModifyAppVarRsp {}

// AppealCallbackReq 申诉回调请求
message AppealCallbackReq{
  // t_audit 表中的 父审核 id
  uint64 audit_parent_id = 1;
  // t_audit 表中的 审核 id
  uint64 audit_id = 2;
  // 人工审核结果 是否通过
  bool is_pass = 3;
  // 审核类型
  uint32 appeal_type = 4;
  // 最后一次审核人
  string operator = 5;
  // 申诉单父 id
  uint64 appeal_parent_id = 6;
  // 申诉 id
  uint64 appeal_id = 7;
  // 申诉任务完成
  bool appeal_task_done = 8;
  // 申诉人工审核成功数
  uint32 num_success = 9;
  // 申诉人工审核失败数
  uint32 num_fail = 10;
  // 申诉人工审核总数
  uint32 num_total = 11;
  // t_appeal 申诉表 关联 id
  uint64 relate_id = 12;
  // corp_id 企业 ID
  uint64 corp_id = 13;
  // robot_id 机器人 ID
  uint64 robot_id = 14;
  // 申诉单创建人
  uint64 create_staff_id = 15;
  // 审核拒绝的原因
  string appeal_reject_reason = 16;
}

// AppealCallbackRsp 申诉回调返回
message AppealCallbackRsp{}

// 查询标签信息请求
message GetAttributeInfoReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 标签ID列表
  repeated uint64 attr_biz_ids = 2 [(validate.rules).repeated .min_items = 1];
}

// 查询标签信息返回
message GetAttributeInfoRsp {
  // 标签详情
  message LabelInfo {
    // 标签ID
    uint64 label_biz_id = 1;
    // 标签名称
    string label_name = 2 [(validate.rules).string = {min_len: 1}];
  }
  // 属性标签
  message AttrLabelInfo {
    // 属性ID
    uint64 attr_biz_id = 1;
    // 属性标识
    string attr_key = 2;
    // 属性名称
    string attr_name = 3;
    // 标签名称
    repeated LabelInfo labels = 4;
  }
  // 列表
  repeated AttrLabelInfo attr_label_infos = 1;
}

// CateInfo 分类信息(文档,同义词等)
message CateInfo {
  // 分类的业务ID
  uint64 cate_biz_id = 1;
  // 分类名称
  string name = 2;
  // 分类对象的数量(如文档,同义词等)
  uint32 total = 3;
  // 是否可新增
  bool can_add = 4;
  // 是否可编辑
  bool can_edit = 5;
  // 是否可删除
  bool can_delete = 6;
  // 子分类
  repeated CateInfo children = 7;
}

// 获取分类分组请求
message ListCateReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
}

// 获取分类分组响应
message ListCateRsp {
  repeated CateInfo list = 1;
}

// 创建分类请求
message CreateCateReq {
  // 分类名称
  string name = 1 [(validate.rules).string.min_len = 1];
  // 父分类业务ID,0为未分类
  string parent_biz_id = 2;
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string.min_len = 1];
}

// 创建分类响应
message CreateCateRsp {
  // 分类业务ID
  uint64 cate_biz_id = 1;
  // 是否可新增
  bool can_add = 2;
  // 是否可编辑
  bool can_edit = 3;
  // 是否可删除
  bool can_delete = 4;
}

// 分类修改请求
message ModifyCateReq {
  // 分类ID
  string id = 1;
  // 分类名称
  string name = 2 [(validate.rules).string.min_len = 1];
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string = {min_len: 1}];
  // 分类业务ID
  string cate_biz_id = 4;
}

// 分类修改响应
message ModifyCateRsp {}

// 分类删除请求
message DeleteCateReq {
  // 分类ID
  string id = 1;
  // 机器人ID
  string bot_biz_id = 2 [(validate.rules).string.min_len = 1];
  // 分类业务ID
  string cate_biz_id = 3;
}

// 分类删除响应
message DeleteCateRsp {}

// 批量操作对象分组 请求
message GroupObjectReq {
  // 操作对象的业务ID列表(如文档,同义词等)
  repeated string ids = 1;
  // 分类 ID
  string cate_id = 2;
  // 机器人ID
  string bot_biz_id = 3 [(validate.rules).string.min_len = 1];
  // 分类对象的业务ID列表【ids】
  repeated uint64 biz_ids = 4;
  // 分类 业务ID【cate_id】
  string cate_biz_id = 5;
}

// 批量操作对象分组 响应
message GroupObjectRsp {}

// 同义词详情
// 同义词不会作为独立的 item 返回给前端，而是作为同义词列表的一部分返回给前端
message Synonym {
  // Synonym ID
  string synonym_biz_id = 1;
  // 标准词
  string standard_word = 2;
  // 更新时间
  int64 update_time = 3;
  // 状态 2未发布3发布中4已发布5发布失败
  uint32 status = 4;
  // 状态描述
  string status_desc = 5;
  // 创建时间
  int64 create_time = 6;
  // 如果是标准词，对应的同义词列表
  repeated string synonyms = 7;
}

// 获取同义词列表请求
message ListSynonymsReq {
  // 查询内容
  string query = 1;
  // 分类ID -1代表不过滤
  string cate_biz_id = 2;
  // 发布状态(2待发布 3发布中 4已发布 5发布失败)
  repeated uint32 release_status = 3;
  // 页码
  uint32 page_number = 4 [(validate.rules).uint32.gt = 0];
  // 每页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  string bot_biz_id = 6 [(validate.rules).string.min_len = 0];
  // 同义词 ID
  string synonym_biz_id = 7;
  // 指定多个同义词 ID 列表
  repeated string synonym_biz_ids = 8;
}

// 获取同义词列表响应
message ListSynonymsRsp {
  uint64 total = 1;
  // 页码
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  // 列表
  repeated Synonym list = 3;
}

// 新增同义词请求
message CreateSynonymsReq {
  // 分组 ID
  string cate_biz_id = 1;
  // 标准词
  string standard_word = 2 [(validate.rules).string.min_len = 1];
  // 同义词列表
  repeated string synonyms = 3;
  // 机器人 ID
  string bot_biz_id = 4;
}

// 新增同义词响应
message CreateSynonymsRsp {
  // 同义词业务ID
  string synonym_biz_id = 1;
  // 冲突类型
  uint32 conflict_type = 2; // 0 无冲突 1 当前标准词与已有标准词冲突 2 当前标准词的同义词与已有标准词冲突 3 当前标准词的同义词与已有标准词的同义词冲突
  // 冲突内容（标准词或者同义词的内容）
  string conflict_content = 3;
}

// 删除同义词请求
message DeleteSynonymsReq {
  // 同义词业务ID 列表
  repeated string synonym_biz_ids = 1 [(validate.rules).repeated .min_items = 1];
  // 机器人 ID
  string bot_biz_id = 2 [(validate.rules).string.min_len = 1];
}

// 删除同义词响应
message DeleteSynonymsRsp {}

// 修改同义词请求
message ModifySynonymsReq {
  // 同义词业务ID
  string synonym_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 标准词
  string standard_word = 2 [(validate.rules).string.min_len = 1];
  // 同义词列表
  repeated string synonyms = 3;
  // 分类 ID
  string cate_biz_id = 4;
  // 机器人 ID
  string bot_biz_id = 5 [(validate.rules).string.min_len = 1];
}

// 修改同义词响应
message ModifySynonymsRsp {
  // 冲突类型
  uint32 conflict_type = 1; // 0 无冲突 1 当前标准词与已有标准词冲突 2 当前标准词的同义词与已有标准词冲突 3 当前标准词的同义词与已有标准词的同义词冲突
  // 冲突内容（标准词或者同义词的内容）
  string conflict_content = 2;
}

// 导入同义词请求
message UploadSynonymsListReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 1];
  // 文件名
  string file_name = 2 [(validate.rules).string = {min_len: 1, max_len: 255}];
  // cos路径
  string cos_url = 3 [(validate.rules).string.min_len = 1];
  // cos_hash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string cos_hash = 4 [(validate.rules).string.min_len = 1];
  // 文件大小
  string size = 5 [(validate.rules).string.min_len = 1];
}

// 导入同义词响应
message UploadSynonymsListRsp {
  // 导入错误
  string error_msg = 1;
  // 错误链接
  string error_link = 2;
  // 错误链接文本
  string error_link_text = 3;
}

// 导出同义词请求
message ExportSynonymsListReq {
  ListSynonymsReq filters = 1; // 根据筛选数据导出
  string bot_biz_id = 2; // 机器人ID
  repeated string synonyms_biz_ids = 3; // 勾选导出
}

// 导出同义词响应
message ExportSynonymsListRsp {}

// 同义词NER请求
message SynonymsNERReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string.min_len = 0];
  // 查询内容
  string query = 2;
  // 场景 1是评测 2是正式
  uint32 scenes = 3 [(validate.rules).uint32 = {in: [1, 2]}];
}

// 同义词NER响应
message SynonymsNERRsp {
  message NERInfo {
    uint32 num_tokens = 1;
    string original_text = 2;
    string ref_value = 3;
  }
  // 替换后的查询内容
  string replaced_query = 1;
  // 匹配到的所有同义词
  repeated NERInfo ner_info = 2;
}

// 批量获取文档详情（内部接口）请求
message InnerDescribeDocsReq {
  // 机器人ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 文档ID列表
  repeated string doc_biz_ids = 2;
}

// 批量获取文档详情（内部接口）响应
message InnerDescribeDocsRsp {
  message DocDetail {
    // 文档ID
    uint64 doc_biz_id = 1;
    // 文件名
    string file_name = 2;
    // cos路径
    string cos_url = 3;
    // 更新时间
    int64 update_time = 4;
    // 状态值(5审核通过 7审核中 8审核不通过 9审核通过 10待发布 11发布中 12发布成功 13学习中 14学习失败)
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 文件类型
    string file_type = 7;
    // 生成失败原因
    string reason = 8;
    // 答案中是否引用
    bool is_refer = 9;
    // qa对数量
    uint32 qa_num = 10;
    // 是否删除
    bool is_deleted = 11;
    // 文档来源
    uint32 source = 12;
    // 来源描述
    string source_desc = 13;
    // 是否允许重新生成
    bool is_allow_restart = 14;
    // qa是否已删除
    bool is_deleted_qa = 15;
    // 问答是否生成中
    bool is_creating_qa = 16;
    // 是否允许删除
    bool is_allow_delete = 17;
    // 是否允许操作引用开关
    bool is_allow_refer = 18;
    // 问答是否生成过
    bool is_created_qa = 19;
    // 文档字符量
    uint64 doc_char_size = 20;
    // 是否允许编辑
    bool is_allow_edit = 21;
    // 属性标签适用范围 1：全部，2：按条件范围
    uint32 attr_range = 22;
    // 属性标签
    repeated AttrLabel attr_labels = 23;
    // 分类ID
    uint64 cate_biz_id = 24;
    // 文档主键ID（内部使用的ID）
    uint64 doc_id = 25;
  }
  repeated DocDetail docs = 1;
}

// 清理应用知识库资源请求
message ClearAppKnowledgeResourceReq {
  // 应用ID
  string bot_biz_id = 1 [(validate.rules).string = {min_len: 1}];
  // 任务ID
  uint64 task_id = 2;
}

// 清理应用知识库资源响应
message ClearAppKnowledgeResourceRsp {

}


// 文档信息
message DocInfo {
  // 文档id
  string doc_biz_id = 1;
  // 文档名称
  string doc_name = 2;
  // 文档创建时间
  int64 create_time = 3;
  // 生成问答数量
  uint32 qa_count = 4;
  // 文档链接
  string doc_url = 5;
  // 引用文档状态枚举
  int32 status = 6;
  // 引用文档类型
  string file_type = 7;
  // 是否已删除
  bool is_deleted = 8;
}

// 文档diff列表请求
message ListDocDiffTaskReq {
  // 机器人ID
  string app_biz_id = 1;
  // 过滤任务状态(0待处理 1处理中 2已完成 3已失效)
  repeated int32 statuses = 2;
  // 页码
  uint32 page_number = 3;
  // 每页数量
  uint32 page_size = 4;
}

// 文档diff列表响应
message ListDocDiffTaskRsp {
  message DocDiffTask {
    // 文档diff任务id
    string doc_diff_task_biz_id = 1;
    // 新文档信息
    DocInfo new_doc = 2;
    // 旧文档信息
    DocInfo old_doc = 3;
    // 对比原因(1名称相同 2手动添加 3网址相同)
    uint32 comparison_reason = 4;
    // 对比类型(1新文档有生成问答对，旧文档没有 2旧文档有生成问答对,新文档没有 3新文档和旧文档都有问答对 4新文档和旧文档都没有问答对)
    uint32 diff_type = 5;
    // 文档操作类型(1删除旧文档 2删除新文档 3旧文档重命名 4新文档重命名 5不处理)
    uint32 doc_operation = 6;
    // 问答操作
    uint32 qa_operation = 7;
    // 文档操作对比任务状态(0待处理 1处理中 2已完成 3已失效)
    uint32 status = 8;
    // 文档diff任务创建时间
    int64 create_time = 9;
    // 文档diff详情比对任务状态(0待处理 1处理中 2已完成 3已失败)
    uint32 diff_data_process_status = 10;
  }
  uint64 total = 1;
  // 页码
  uint32 page_number = 2;
  // 列表
  repeated DocDiffTask list = 3;
}

// 创建文档diff任务请求
message CreateDocDiffTaskReq {
  // 机器人ID
  string app_biz_id = 1;
  // 新文档id
  string new_doc_biz_id = 2;
  // 旧文档id
  string old_doc_biz_id = 3;
  // 对比原因(1名称相同 2手动添加 3网址相同)
  uint32 comparison_reason = 4;
}
// 创建文档diff任务响应
message CreateDocDiffTaskRsp {
  // 文档diff任务id
  string doc_diff_task_biz_id = 1;
}

// 文档diff任务详情请求
message DescribeDocDiffTaskReq {
  // 机器人ID
  string app_biz_id = 1;
  // 对比任务ID
  string doc_diff_task_biz_id = 2;
}


// 文档diff任务详情响应
message DescribeDocDiffTaskRsp {
  message doc_result {
    // 文档操作
    uint32 doc_operation = 1;
    // 文档操作结果 文档操作结果(0处理中，1操作成功，2操作失败)
    uint32 doc_operation_result = 2;
    // 重命名操作新文件名
    string new_doc_rename = 3;
    // 重命名操作旧文件名
    string old_doc_rename = 4;
  }
  message qa_result {
    // 问答操作
    uint32 qa_operation = 1;
    // 问答操作状态 问答操作结果(0处理中，1操作成功，2操作失败)
    uint32 qa_operation_status = 2;
    // 问答操作结果 (操作成功或失败的结果提示)
    string qa_operation_result = 3;
  }
  // 文档diff任务id
  uint64 doc_diff_task_biz_id = 1;
  // 新文档信息
  DocInfo new_doc = 2;
  // 旧文档信息
  DocInfo old_doc = 3;
  // 对比原因(1名称相同 2手动添加 3网址相同)
  uint32 comparison_reason = 4;
  // 对比类型(1新文档有生成问答对，旧文档没有 2旧文档有生成问答对,新文档没有 3新文档和旧文档都有问答对 4新文档和旧文档都没有问答对)
  uint32 diff_type = 5;
  // 文档操作结果
  doc_result doc_result_info = 6;
  // 问答操作结果
  qa_result qa_result_info = 7;
  // 文档操作对比任务状态(0待处理 1处理中 2已完成 3已失效)
  uint32 status = 8;
  // 文档diff任务创建时间
  int64 create_time = 9;
}

// 处理文档diff任务请求
message HandleDocDiffTaskReq {
  message DocDiffTask {
    // 对比任务ID
    string doc_diff_task_biz_id = 1;
    // 对比类型
    uint32 diff_type = 2;
    // 文档操作
    uint32 doc_operation = 3;
    // 问答操作
    uint32 qa_operation = 4;
    // 重命名名称
    string doc_rename = 5;
  }
  // 机器人ID
  string app_biz_id = 1;
  // 对比任务列表
  repeated DocDiffTask list = 2;
}

// 处理文档diff任务响应
message HandleDocDiffTaskRsp {
  message FailDocDiffTask {
    // 对比任务ID
    string doc_diff_task_biz_id = 1;
    // 处理失败错误信息
    string err_msg = 2;
  }
  // 成功条数
  uint32 success_total = 1;
  // 失败任务列表
  repeated FailDocDiffTask fail_list = 2;
}

// 删除文档diff任务请求
message DeleteDocDiffTaskReq {
  // 机器人ID
  string app_biz_id = 1;
  // 对比任务ID列表
  repeated string doc_diff_task_biz_ids= 2;
}
// 删除文档diff任务响应
message DeleteDocDiffTaskRsp {

}

// 获取文档diff结果请求
message ListDocDiffDataReq {
  // 机器人ID
  string app_biz_id = 1;
  // 对比任务ID
  string doc_diff_task_biz_id = 2;
  // 页码
  uint32 page_number = 3;
  // 页大小
  uint32 page_size = 4;
}

// 获取文档diff结果响应
message ListDocDiffDataRsp {
  // 片段diff详情列表,每个元素为一个片段diff详情json字符串
  repeated string diff_data = 1;
  // 片段diff总数
  uint64 total = 2;
}

// 校验知识库权限请求
message CheckKnowledgePermissionReq {
  // 机器人ID
  string app_biz_id = 1;
  // 操作 infinity_attribute_label:无限属性标签
  repeated string operations = 2;
}

// 知识库权限信息
message KnowledgePermission {
  // 操作
  string operation = 1;
  // 是否有权限
  bool allowed = 2;
}

// 校验知识库权限响应
message CheckKnowledgePermissionRsp {
  // 权限校验结果
  repeated KnowledgePermission check_result = 1;
}

// 获取应用端权限配置状态
message GetAclConfigStatusReq {
  string app_biz_id = 1;
}

// 获取应用端权限配置状态响应
message GetAclConfigStatusRsp {
  uint32 status = 1;
}

// 知识库选择标签结构
message ChooseLabel {
  message Label {
    string label_biz_id = 1;
    string label_name = 2;
    uint32 is_deleted = 3; //是否删除，0正常，1已删除
  }
  string attr_biz_id = 1;
  string attr_name = 2;
  repeated Label labels = 3;
  uint32 is_deleted = 4; //是否删除，0正常，1已删除
}

// 知识库库选择内容结构
message KnowChoose {
  string knowledge_biz_id = 1; //知识库业务id
  string knowledge_name = 2; //知识库名字
  uint32 type = 3; //知识库类型 1私有 2共享
  uint32 search_type = 4; //检索范围(1全部知识 2按特定知识 3按标签)
  repeated string doc_biz_ids = 5; //选中的文档业务id
  repeated string doc_cate_biz_ids = 6; //选中的文档分类业务id
  repeated string ques_ans_biz_ids = 7; //选中的问答业务id
  repeated string ques_ans_cate_biz_ids = 8; //选中的问答分类业务id
  repeated string db_biz_ids = 9; //选中的数据库业务id
  repeated ChooseLabel labels = 10; //关联的标签信息
  int32 condition = 11; //操作符 1AND, 2OR
}

// 新增角色
message CreateRoleReq {
  string app_biz_id = 1;
  string name = 2;
  string description = 3;
  uint32 search_type = 4; //整体检索范围(1全部知识 2按知识库)
  repeated KnowChoose know_choose = 5; //知识库库选择内容
}

// 新增角色响应
message CreateRoleRsp {
  uint64 role_biz_id = 1; //角色业务id
}

// 编辑角色
message ModifyReq {
  string role_biz_id = 1; //预置角色传-1
  uint32 type = 2; //角色类型 1预置 2自定义
  string app_biz_id = 3;
  string name = 4;
  string description = 5;
  uint32 search_type = 6; //整体检索范围(1全部知识 2按知识库)
  repeated KnowChoose know_choose = 7; //知识库库选择内容
}

// 编辑角色响应
message ModifyRsp {
}

// 获取角色详情
message DescribeKnowledgeRoleReq {
  string role_biz_id = 1;
  string app_biz_id = 2;
}

message RoleInfo {
  uint64 app_biz_id = 1;
  uint64 role_biz_id = 2;
  string name = 3;
  int32 type = 4; //角色类型(1 预置 2 自定义)
  string description = 5;
  uint32 search_type = 6; //整体检索范围(1全部知识 2按知识库)
  repeated KnowChoose know_choose = 7; //知识库库选择内容
  uint32 is_deleted = 8;
  int64 create_time = 9;
  int64 update_time = 10;
}

// 获取角色详情响应
message DescribeKnowledgeRoleRsp {
  RoleInfo role_info = 1;
}

// 获取角色选择的特定知识
message DescribeRoleSearchReq {
  message RoleSearch {
    uint32 type = 1; //类型(1 文档 2 文档分类 3问答 4问答分类 5数据库)
    repeated string search_biz_ids = 2;
  }
  string app_biz_id = 1;
  string role_biz_id = 2;
  string know_biz_id = 3;
  repeated RoleSearch role_search = 4;
}

// 获取角色选择的特定知识响应
message DescribeRoleSearchRsp {
  message SearchInfo {
    uint32 type = 1; //类型(1 文档 2 文档分类 3问答 4问答分类 5数据库)
    string name = 2;
    uint64 search_biz_id = 3;
    uint64 cate_biz_id = 4; //如果是文档或者问答，带上分类业务id
  }
  repeated SearchInfo role_search = 1; 
}

// 获取角色列表
message ListRoleReq {
  string app_biz_id = 1;
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  repeated string role_biz_ids = 4;
  string name = 5;
}

// 角色列表展示的角色信息
message ListRoleInfo {
  message KnowSearch {
    uint64 knowledge_biz_id = 1; //知识库业务id
    uint32 type = 2; //知识库类型 1私有 2共享
    string knowledge_name = 3; //知识库名字
    uint32 search_type = 4; //检索范围(1全部知识 2按特定知识 3按标签)
    uint32 is_empty = 5; //0检索条件不为空 1为空
  }
  uint64 app_biz_id = 1;
  uint64 role_biz_id = 2;
  string name = 3;
  int32 type = 4; //角色类型(1 预置 2 自定义)
  string description = 5;
  uint32 search_type = 6; //整体检索范围(1全部知识 2按知识库)
  repeated KnowSearch know_choose = 7; //知识库库选择内容
  uint32 is_deleted = 8;
  int64 create_time = 9;
  int64 update_time = 10;
}

// 获取角色列表响应
message ListRoleRsp {
  uint64 total = 1;
  repeated ListRoleInfo role_list = 2;
}

// 删除角色前检查引用
message CheckDeleteRoleReq {
  repeated string role_biz_ids = 1;
  string app_biz_id = 2;
}

// 角色基础信息
message RoleBaseInfo {
  uint64 role_biz_id = 1; //角色业务id
  string role_name = 2;
}

message CheckDeleteRoleRsp {
  repeated RoleBaseInfo can_delete_role = 1; //可以删除的角色列表
  repeated RoleBaseInfo cannot_delete_role = 2; //不可以删除的角色列表
}

// 删除角色
message DeleteRoleReq {
  repeated string role_biz_ids = 1;
  string app_biz_id = 2;
}

// 删除角色响应
message DeleteRoleRsp {
  repeated uint64 delete_role_biz_ids = 1; //返回已删除的用户ids
}

// 新增用户
message CreateCustUserReq {
  string app_biz_id = 1;
  string name = 2;
  string third_user_id = 3; //用户ID，由客户填写维护
  repeated string role_biz_ids = 4; //关联的角色business_id 1是默认角色
}

// 新增用户响应
message CreateCustUserRsp {
  uint64 user_biz_id = 1; //用户业务id
}

// 编辑用户
message ModifyCustUserReq {
  string user_biz_id = 1; //用户业务id
  string app_biz_id = 2;
  string name = 3;
  string third_user_id = 4; //用户ID，由客户填写维护
  repeated string role_biz_ids = 5; //关联的角色business_id 1是默认角色
}

// 编辑用户响应
message ModifyCustUserRsp {
}

// 批量编辑用户
message BatchModifyUserReq {
  repeated string user_biz_ids = 1; //用户业务id
  string app_biz_id = 2;
  repeated string role_biz_ids = 3; //关联的角色business_id
}

// 批量编辑用户响应
message BatchModifyUserRsp {
}

// 获取用户列表
message ListCustUserReq {
  string app_biz_id = 1;
  uint32 page_number = 2; //page和page_size都传0获取全部
  uint32 page_size = 3;
  repeated string user_biz_ids = 4;
  string query = 5; //查询用户名称或者第三方用户id
}

// 用户信息
message CustUserInfo {
  uint64 app_biz_id = 1;
  uint64 user_biz_id = 2;
  string name = 3;
  string third_user_id = 4; //用户ID，由客户填写维护
  uint32 is_deleted = 5;
  int64 create_time = 6;
  int64 update_time = 7;
  repeated RoleBaseInfo role_list = 8; //关联的角色信息
}

// 获取用户列表响应
message ListCustUserRsp {
  uint64 total = 1;
  repeated CustUserInfo user_list = 2;
}

// 获取用户详情
message DescribeCustUserReq {
  string user_biz_id = 1;
  string app_biz_id = 2;
}

// 获取用户详情响应
message DescribeCustUserRsp {
  CustUserInfo user_info = 1;
}

// 删除用户
message DeleteCustUserReq {
  repeated string user_biz_ids = 1;
  string app_biz_id = 2;
}

// 删除用户响应
message DeleteCustUserRsp {
  repeated uint64 delete_user_biz_ids = 1;
}

// 设置特殊权限配置
message SetCustUserConfigReq {
  string app_biz_id = 1;
  string not_set_role_biz_id = 2; //没传第三方user_id时兜底角色 0是不检索 1是默认角色
  string not_use_role_biz_id = 3; //传了第三方user_id无法使用时兜底角色 0是不检索 1是默认角色
}

// 特殊权限配置
message SetCustUserConfigRsp {
}

// 获取特殊权限配置
message GetCustUserConfigReq {
  string app_biz_id = 1;
}

// 特殊权限配置
message CustUserConfig {
  RoleBaseInfo not_userId_role_info = 1; //没传第三方user_id时兜底角色
  RoleBaseInfo not_use_role_info = 2; //传了第三方user_id无法使用时兜底角色
}

// 获取特殊权限配置响应
message GetCustUserConfigRsp {
  CustUserConfig user_config = 1;
}

// 设置外部权限接口配置
message SetThirdAclConfigReq {
  string app_biz_id = 1;
  string knowledge_biz_id = 2; //知识库业务id
  uint32 type = 3; //鉴权方式 1使用token 2使用secret_key
  string secret_id = 4;
  string secret_key = 5;
  string third_token = 6; //配置token
  string get_token_url = 7; //获取第三方token地址
  string check_permissions_url = 8; //外部权限接口地址
}

// 设置外部权限接口配置响应
message SetThirdAclConfigRsp {
}

// 获取外部权限接口配置
message GetThirdAclConfigReq {
  string app_biz_id = 1;
  string knowledge_biz_id = 2; //知识库业务id
}

// 外部权限接口配置
message ThirdAclConfig {
  uint64 app_biz_id = 1;
  uint64 knowledge_biz_id = 2; //知识库业务id
  uint32 type = 3; //鉴权方式 1使用token 2使用secret_key
  string secret_id = 4;
  string secret_key = 5;
  string third_token = 6; //支持直接取
  string get_token_url = 7; //获取第三方token地址
  string check_permissions_url = 8; //外部权限接口地址
}

// 获取外部权限接口配置响应
message GetThirdAclConfigRsp {
  ThirdAclConfig third_acl_config = 1;
}


// ----------- 外部数据库相关 -----------

// 测试数据库连通性请求
message DbSourceConnectionReq {
  string db_type = 1;
  string host = 2;
  int32 port = 3;
  string db_name = 4;
  string username = 5;
  string password = 6;
}

// 测试数据库连通性响应
message TestDbSourceConnectionRsp {
  repeated string db_names = 1;
}

// 数据库前端展示
message DbSourceView {
  uint64 db_source_biz_id = 1;
  string db_name = 2;
  string alias_name = 3;
  string description = 4;
  string db_type = 5;
  string host = 6;
  int32 port = 7;
  string username = 8;
  string password = 9;
  repeated string table_names = 10;
  bool alive = 11;
  int64 last_sync_time = 12;
  int64 create_time = 13;
  uint32 status = 14;
}


// 新增数据库请求
message AddDbSourceReq {
  uint64 app_biz_id = 1;
  string db_name = 2;
  string alias_name = 3;
  string description = 4;
  string db_type = 5;
  string host = 6;
  int32 port = 7;
  string username = 8;
  string password = 9;
  repeated string table_names = 10;
}

// 新增数据库响应
message AddDbSourceRsp {
  DbSourceView db_source = 1;
}

// 删除数据库请求
message DeleteDbSourceReq {
  uint64 app_biz_id = 1;
  uint64 db_source_biz_id = 2;
}

// 删除数据库响应
message DeleteDbSourceRsp {
}



// 更新数据库请求
message UpdateDbSourceReq {
  uint64 app_biz_id = 1;
  uint64 db_source_biz_id = 2;
  string db_name = 3;
  string alias_name = 4;
  string description = 5;
  string db_type = 6;
  string host = 7;
  int32 port = 8;
  string username = 9;
  string password = 10;
  repeated string table_names = 11;
}

// 更新数据库响应
message UpdateDbSourceRsp {
  DbSourceView db_source = 1;
}


// 分页请求
message ListDbSourceReq {
  uint64 app_biz_id = 1;
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  string filter_db_name =4;
}

// 分页响应
message ListDbSourceRsp {
  repeated DbSourceView list = 1;
  int32 total = 2;
}

// 根据 db_source_biz_id 获取详情
message GetDbSourceReq {
  uint64 app_biz_id = 1;
  uint64 db_source_biz_id = 2;
}

message GetDbSourceRsp {
  DbSourceView db_source = 1;
}

// 表信息（只包含表名）
message TableInfo {
  string table_name = 1;
}

// 分页获取表列表请求
message ListTablesReq {
  DbSourceConnectionReq db_conn = 1;
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
}

// 分页获取数据源表数据，用于选择将哪些表添加到知识库。
message ListTablesRsp {
  repeated TableInfo list = 1;
  int32 total = 2;
}


// 表元数据
message DbTableView {
  uint64 db_table_biz_id = 1;
  string table_name = 2;
  string table_schema = 3;
  string alias_name = 4;
  string description = 5;
  int32 row_count = 6;
  int32 column_count = 7;
  int64 table_added_time = 8;
  int64 table_modified_time = 9;
  uint32 status = 10;
}

// 新建表数据请求
message CreateDbTableReq {
  uint64 db_source_biz_id = 1;
  string table_name = 2;
  string table_schema = 3;
  string alias_name = 4;
  string description = 5;
}

// 新建表数据响应
message CreateDbTableRsp {
  DbTableView db_table = 1;     // 创建成功时返回表数据
}


// 删除单一表数据请求
message DeleteDbTableReq {
  uint64 app_biz_id = 1;
  uint64 db_table_biz_id = 2;
}

// 删除单一表数据响应
message DeleteDbTableRsp {
}


// 更新表数据请求
message UpdateDbTableReq {
  uint64 db_table_biz_id = 1;
  string alias_name = 2;
  string description = 3;
}

// 更新表数据响应
message UpdateDbTableRsp {
  DbTableView db_table = 1;
}

// 查询单一表数据请求
message GetDbTableReq {
  uint64 app_biz_id = 1;
  uint64 db_table_biz_id = 2;
}

// 查询单一表数据响应
message GetDbTableRsp {
  DbTableView db_table = 1;
}

// 分页查询已经添加的数据源下的表数据请求
message ListDbTableReq {
  uint64 app_biz_id = 1;
  uint64 db_source_biz_id = 2;
  uint32 page_number = 3 [(validate.rules).uint32.gt = 0];
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
  string filter_table_name = 5;
}

// 分页查询表数据响应
message ListDbTableRsp {
  repeated DbTableView list = 1;
  bool alive = 2;
  int32 total = 3;
}


// 列元数据
message DbTableColumnView {
  uint64 db_table_column_biz_id = 1;
  string column_name = 2;
  string data_type = 3;
  string alias_name = 4;
  string description = 5;
  string unit = 6;
  bool is_indexed = 7;
}

// 根据 db_table_biz_id 批量获取 t_db_table_column 列表
message ListDbTableColumnReq {
  uint64 app_biz_id = 1;
  uint64 db_table_biz_id = 2;
}

message ListDbTableColumnRsp {
  repeated DbTableColumnView columns = 1;
}

// 更新列数据
message UpdateDbTableColumnReq {
  uint64 db_table_column_biz_id = 1;
  string alias_name = 2;
  string description = 3;
  string unit = 4;
  bool is_indexed = 5;
}

// 合并后的整体更新请求
message UpdateDbTableAndColumnsReq {
  uint64 app_biz_id = 1;
  UpdateDbTableReq table = 2; // 表的更新信息
  repeated DbTableColumnView columns = 3; // 需要更新的列
}

message UpdateDbTableAndColumnsResp {
}

// 获取多个数据库请求
message BatchGetDbSourcesReq {
  uint64 app_biz_id = 1;
  repeated uint64 db_source_biz_id = 2;
}

// 获取多个数据库响应
message BatchGetDbSourcesRsp {
  repeated DbSourceView db_sources = 1;
}


message PreviewTableReq {
  uint64 app_biz_id = 1;
  uint64 db_source_biz_id = 2;
  string table_name = 3;
  uint32 page_number = 4 [(validate.rules).uint32.gt = 0];
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
  string filter_column = 6;
  string filter_value = 7;
}

message PreviewTableRsp {
  repeated string columns = 1;
  repeated RowData rows = 2;
  int32 total = 3;
}

message RowData {
  repeated string values = 1;
}

// ----------- 外部数据库相关 -----------

// ----------- 解析干预 -----------

message Text2SqlPreviewTableReq {
  uint64 app_biz_id = 1;
  uint64 doc_biz_id = 2;
  uint32 page_number = 3 [(validate.rules).uint32.gt = 0];
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

message Text2SqlPreviewTableRsp {
  repeated Text2SqlPreviewSheet sheet_data = 1;
}

message Text2SqlPreviewSheet {
  uint64 doc_sheet_biz_id = 1;
  string sheet_name = 2;
  repeated string columns = 3;
  repeated RowData rows = 4;
  int32 total = 5;
}

message GetText2SqlColumnsReq {
  uint64 app_biz_id = 1;
  uint64 doc_sheet_biz_id = 2;
}

message GetText2SqlColumnsRsp {
  string description = 1;
  repeated Text2SqlTableColumnView columnViews = 2;
}

message Text2SqlTableColumnView {
  uint64 db_table_column_biz_id = 1;
  string column_name = 2;
  string alias_name = 3;
  string description = 4;
  string unit = 5;
  string data_type = 6;
}


message UpdateText2SqlColumnsReq {
  uint64 app_biz_id = 1;
  uint64 doc_sheet_biz_id = 2;
  string description = 3;
  repeated UpdateText2SqlColumnItem columns = 4;
}

message UpdateText2SqlColumnsRsp {
}

message UpdateText2SqlColumnItem {
  uint64 db_table_column_biz_id = 1;
  string alias_name = 2;
  string description = 3;
  string unit = 4;
}


// ----------- 解析干预 -----------


message GenerateKnowledgeSchemaReq {
  uint64 app_biz_id = 1;
}

message GenerateKnowledgeSchemaRsp {
}

message GetKnowledgeSchemaReq {
  uint64 app_biz_id = 1;
  string env_type = 2; // 环境类型："sandbox": 沙箱环境, "product": 正式环境
}

message GetKnowledgeSchemaRsp {
  message SchemaItem {
    string business_id = 1; // 文档："doc_xxxxx" 文档聚类："doc_cluster_xxxxx"
    string name = 2; // 名称，文件名或者文件聚类名
    string summary = 3; // 文件摘要或者文件聚类摘要
  }
  repeated SchemaItem schemas = 1;
  bool schema_need_update = 2; // 知识库是否有更新
}

message GetKnowledgeSchemaTaskReq {
  uint64 app_biz_id = 1;
}

message GetKnowledgeSchemaTaskRsp {
  string status = 1; // 任务状态，"processing：进行中, "success"：成功, "failed"：失败
  int64 latest_success_time = 2; // 最近一次成功时间
}

message ListReleaseDbReq {
  uint64 app_biz_id = 1;
  // 发布ID，非必选，为空表示当前未发布的。
  uint64 release_biz_id = 2;
  // 页码
  uint32 page_number = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

message ReleaseDb {
  uint64 db_source_biz_id = 1;
  string db_name = 2;
  // 最后的修改时间戳
  uint64 update_time = 3;
  // 状态
  uint32 action = 4;
  string action_desc = 5;
}

message ReleaseDbTable {
  uint64 db_table_biz_id = 1;
  string table_name = 2;
  string db_name = 3;
  // 最后的修改时间戳
  uint64 update_time = 4;
  // 状态
  uint32 action = 5;
  string action_desc = 6;
}

message ListReleaseDbRsp {
  int32 total = 1;
  repeated ReleaseDb list = 2;
}

message ListReleaseDbDbTableReq {
  uint64 app_biz_id = 1;
  // 发布ID，非必选，为0表示当前未发布的。
  uint64 release_biz_id = 2;
  // 页码
  uint32 page_number = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

message GetDbSourcePublicKeyReq {
  uint64 app_biz_id = 1;
}

message  GetDbSourcePublicKeyRsp {
   string public_key = 1;
}

message ListReleaseDbDbTableRsp {
  int32 total = 3;
  repeated ReleaseDbTable list = 4;
}

message SendPublishDbTaskEventReq {
  uint64 app_biz_id = 1;
  // 发布ID，由上游主动传入; 仅 COLLECT 事件可以为空
  uint64 release_biz_id = 2;
  // 发布事件 COLLECT|RELEASE|PAUSE|RETRY
  string event = 3;
}

message SendPublishDbTaskEventRsp {
}

message GetUnreleasedDbCountReq {
  uint64 app_biz_id = 1;
}

message GetUnreleasedDbCountRsp {
  int32 count        = 1; // 数量
}

message GetPublishDbTaskReq {
  uint64 app_biz_id = 1;
  // 发布ID，由上游主动传入; 仅 COLLECT 事件可以为空
  uint64 release_biz_id = 2;
}

message GetPublishDbTaskRsp {
  // 发布ID，能找到对应的发布ID返回原发布ID，否则返回0
  uint64 release_biz_id = 1;
  int32 count        = 2; // 数量
}


message SetKnowledgeConfigReq {
  string app_biz_id = 1; // 应用业务ID
  string knowledge_biz_id = 2; // 知识库业务id
  KnowledgeSchemaConfig knowledge_schema_config = 3;
}

message SetKnowledgeConfigRsp {
}

message GetKnowledgeConfigReq {
  string app_biz_id = 1; // 应用业务ID
  string knowledge_biz_id = 2; // 知识库业务id
}

message GetKnowledgeConfigRsp {
  KnowledgeSchemaConfig knowledge_schema_config = 1;
}

// KnowledgeSchemaConfig 知识库schema配置
message KnowledgeSchemaConfig {
  string dynamic_update_schema = 1; // 是否随知识库更新动态更新schema，字符串类型！可不传，传值必须是"true","false"！
}